.agent-select-wrapper {
  display: flex;

  .dialog-footer {
    display: flex;
    justify-content: center;
  }

  .agent-select-input{
    min-height: 32px;
    border: 1px solid #d7dbe7;
    border-radius: 6px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 14px;
    padding: 0 12px;
    margin-right: 12px;
  }

  .form-content {
    flex: auto;
    display: flex;
  }

  form {
    margin-bottom: 16px;
    display: flex;
    width: 100%;
    .el-form-item {
      margin-bottom: 0;
      margin-right: 12px;
    }
  }



  .agent-select-dialog-content {
    margin-bottom: 16px;
    height: 500px;
    overflow: scroll;
  }

  .agent-select-content{
    display: flex;
    justify-content: space-between;

    .agent-select-list{
        width: 49%;
    }

    .agent-select-selected{
        width: 49%;
        border: 1px solid rgba(0, 0, 0, 0.1);
        padding: 0 12px;
        overflow: hidden;
        border-radius: 10px;

        .title {
            display: flex;
            justify-content: space-between;
        }

        .agent-select-tag{
            margin-bottom: 8px;
            margin-right: 8px;
            height: 30px;
            display: inline-block;
            .el-tag {
              height: 30px;
              color: #333;
            }
        }
    }
  }
}
