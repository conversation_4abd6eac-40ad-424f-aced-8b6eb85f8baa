import { update_bank_info, query_bank_list } from '@/services/bank';
import { Select } from 'common/src/components/select/Select';
import { useRequest, usePagination } from 'common';
import { ElForm, ElMessage, FormInstance } from 'element-plus';
import { defineComponent, ref, watch, computed } from 'vue';
import {
  banks,
  brand_ledger_balance_query,
  query_brand_ledger_list,
  account,
  clearing_account,
  bind_pingan_bank_card,
  bind_bank_card,
  bind_verify,
  terms_agreement,
} from '@/services/ledger';
import success from '@/assets/success.png';
interface FormData {
  company_name?: string;
  company_cert_no?: string | number;
  legal_name?: string;
  legal_cert_no?: string | number;
  bank_account?: string;
  bank_id?: number;
  bank_card_no?: string | number;
  type?: string;
  mobile?: number;
  verify_amount?: string | number;
  verify_code?: string | number;
  checked?: boolean;
}

export default defineComponent({
  setup(_, ctx) {
    const loading = ref(false);
    const tabIndex = ref(1);
    const is_account_info = ref(false);
    const formDataRef = ref<FormInstance>();
    const formData = ref<FormData>({});
    const rules = {
      company_name: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
      company_cert_no: [{ required: true, message: '请输入统一社会信用代码证号', trigger: 'blur' }],
      legal_name: [{ required: true, message: '请输入法人名称', trigger: 'blur' }],
      legal_cert_no: [{ required: true, message: '请输入法人身份证号', trigger: 'blur' }],
      bank_account: [{ required: true, message: '请输入银行账号', trigger: 'blur' }],
      bank_id: [{ required: true, message: '请输入开户银行', trigger: 'blur' }],
      bank_card_no: [{ required: true, message: '请输入银行账号', trigger: 'blur' }],
      type: [{ required: true, message: '请输入账户类型', trigger: 'blur' }],
      mobile: [{ required: true, message: '请输入手机号', trigger: 'blur' }],
      verify_amount: [{ required: true, message: '请输入校验金额', trigger: 'blur' }],
      verify_code: [{ required: true, message: '请输入校验码', trigger: 'blur' }],
    };

    const reqBankList = usePagination(query_bank_list, {
      manual: true,
      transform(e) {
        return (e.data || []).map(item => {
          return {
            label: item.name,
            value: item.id,
            code: item.code,
          };
        });
      },
    });
    const onQueryBank = (val: string) => {
      reqBankList.runAsync(
        {
          page_num: 1,
          page_size: 10,
        },
        {
          name: val,
        },
      );
    };

    //是否平安银行
    const isPP = computed(() => {
      return formData.value.bank_id === 91;
    });

    const preBtn = () => {
      if (tabIndex.value === 1) {
        ctx.emit('close');
        return;
      }
      tabIndex.value--;
    };

    const nextBtn = () => {
      if (isPP.value && tabIndex.value === 2) {
        tabIndex.value = 4;
        return;
      }
      if (!isPP.value && tabIndex.value === 2) {
        tabIndex.value = 3;
        return;
      }
      tabIndex.value++;
    };

    const checkboxClick = async e => {
      formData.value.checked = e;
      if (e) {
        await useRequest(terms_agreement, { manual: true }).runAsync();
      }
    };

    // 获取账户信息
    const reqGetAccount = useRequest(account, {
      manual: true,
    });

    //创建子台账
    const reqAccount = useRequest(clearing_account, {
      manual: true,
    });

    //平安银行对公
    const reqBindPinganBankCard = useRequest(bind_pingan_bank_card, {
      manual: true,
    });

    //非平安银行对公
    const reqBindBankCard = useRequest(bind_bank_card, {
      manual: true,
    });

    //短信验证
    const reqGetVerifySms = useRequest(bind_verify, {
      manual: true,
    });

    const onSaveBtnClick = () => {
      formDataRef.value?.validate(async valid => {
        if (valid) {
          if (!formData.value.checked) {
            ElMessage.error('请勾选商户协议');
            return;
          }
          loading.value = true;
          if (tabIndex.value === 1) {
            if (is_account_info.value) {
              nextBtn();
              loading.value = false;
              return;
            }
            try {
              await reqAccount.runAsync(formData.value);
              nextBtn();
              loading.value = false;
            } catch (error) {
              loading.value = false;
            }
            return;
          }
          if (tabIndex.value === 2 && isPP.value) {
            try {
              await reqBindPinganBankCard.runAsync(formData.value);
              nextBtn();
              loading.value = false;
            } catch (error) {
              loading.value = false;
            }
            return;
          }
          if (tabIndex.value === 2 && !isPP.value) {
            try {
              await reqGetVerifySms.runAsync(formData.value);
              nextBtn();
              loading.value = false;
            } catch (error) {
              loading.value = false;
            }
            return;
          }
          if (tabIndex.value === 3) {
            try {
              const verify_amount = formData.value.verify_amount * 100;
              await reqBindBankCard.runAsync({ ...formData.value, verify_amount: verify_amount });
              nextBtn();
              loading.value = false;
            } catch (error) {
              loading.value = false;
            }
            return;
          }
        }
      });
    };

    const show = async row => {
      loading.value = false;
      tabIndex.value = row.tabIndex;
      if (row.account_info) {
        is_account_info.value = true;
        const account_info = row.account_info;
        formData.value.company_name = account_info.company_name;
        formData.value.company_cert_no = account_info.company_cert_no;
        formData.value.legal_name = account_info.legal_name;
        formData.value.legal_cert_no = account_info.legal_cert_no;
      }
    };
    // 核心
    ctx.expose({ show, onSaveBtnClick });

    watch(
      () => tabIndex.value,
      () => {
        ctx.emit('changeTab', tabIndex.value);
      },
    );

    return () => (
      <div class="edit-bank-dialog-container">
        <ElForm
          ref={formDataRef}
          style="width: 400px"
          model={formData.value}
          status-icon={false}
          rules={rules}
          label-width="auto"
          class="demo-formData"
        >
          {tabIndex.value === 1 && (
            <>
              <el-form-item label="公司名称" prop="company_name">
                <el-input
                  disabled={is_account_info.value}
                  maxlength={30}
                  v-model={formData.value.company_name}
                  autocomplete="off"
                  placeholder="请输入公司名称"
                />
              </el-form-item>
              <el-form-item label="统一社会信用代码证号" prop="company_cert_no">
                <el-input
                  disabled={is_account_info.value}
                  maxlength={30}
                  v-model={formData.value.company_cert_no}
                  autocomplete="off"
                  placeholder="请输入统一社会信用代码证号"
                />
              </el-form-item>
              <el-form-item label="法人名称" prop="legal_name">
                <el-input
                  disabled={is_account_info.value}
                  maxlength={10}
                  v-model={formData.value.legal_name}
                  autocomplete="off"
                  placeholder="请输入法人名称"
                />
              </el-form-item>
              <el-form-item label="法人身份证号" prop="legal_cert_no">
                <el-input
                  disabled={is_account_info.value}
                  maxlength={18}
                  v-model={formData.value.legal_cert_no}
                  autocomplete="off"
                  placeholder="请输入法人身份证号"
                />
              </el-form-item>
            </>
          )}

          {tabIndex.value === 2 && (
            <>
              <el-form-item label="开户银行" prop="bank_id">
                <Select
                  class="bank_select"
                  filterable
                  v-model={formData.value.bank_id}
                  placeholder="请选择开户银行"
                  remote
                  reserve-keyword
                  remote-method={onQueryBank}
                  options={(reqBankList.data || []) as any[]}
                ></Select>
              </el-form-item>
              <el-form-item label="银行账号" prop="bank_card_no">
                <el-input
                  maxlength={18}
                  v-model={formData.value.bank_card_no}
                  autocomplete="off"
                  placeholder="请输入银行账号"
                />
              </el-form-item>
              {!isPP.value && (
                <el-form-item label="手机号" prop="mobile">
                  <el-input
                    maxlength={18}
                    v-model={formData.value.mobile}
                    autocomplete="off"
                    placeholder="请输入手机号"
                  />
                </el-form-item>
              )}
            </>
          )}
          {tabIndex.value === 3 && (
            <>
              <el-form-item label="验证金额" prop="verify_amount">
                <el-input
                  maxlength={18}
                  v-model={formData.value.verify_amount}
                  autocomplete="off"
                  placeholder="请输入绑定账户收到的转账金额"
                />
              </el-form-item>
              <el-form-item label="短信指令号" prop="verify_code">
                <el-input
                  maxlength={18}
                  v-model={formData.value.verify_code}
                  autocomplete="off"
                  placeholder="请输入收到的短信指令号"
                />
              </el-form-item>
            </>
          )}

          {tabIndex.value === 2 && (
            <el-form-item label=" " prop="checked">
              <el-checkbox label={``} v-model={formData.value.checked} onChange={checkboxClick} />
              我已阅读并同意{' '}
              <a
                onClick={() => {
                  window.open('/agreement.pdf');
                }}
              >
                《平安银行结算通商户协议》
              </a>
            </el-form-item>
          )}
        </ElForm>

        {tabIndex.value === 4 && (
          <div class="mc-meijia-agent-apply-page-container">
            <img class="img" src={success}></img>
            <div class="line_1">开通成功</div>
            <div class="line_2">恭喜您，收款帐户开通！</div>
          </div>
        )}
        <div class="btn-line-footer">
          {tabIndex.value === 4 ? (
            <el-button type="primary" onClick={() => ctx.emit('close')}>
              确定
            </el-button>
          ) : (
            <>
              {tabIndex.value === 1 && <el-button onClick={() => preBtn()}>关闭</el-button>}
              {tabIndex.value === 3 && <el-button onClick={() => preBtn()}>上一步</el-button>}
              <el-button type="primary" onClick={() => onSaveBtnClick()} loading={loading.value}>
                下一步
              </el-button>
            </>
          )}
        </div>
      </div>
    );
  },
});
