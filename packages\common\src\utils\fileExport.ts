import qs from 'query-string';
import { ObjectFilterEmpty } from './func';
import { getToken } from '../config';
export const fileExport = (option: { url: string; params: Record<string, any> }) => {
  const { url, params } = option || {};
  const _paramsstr = qs.stringify(ObjectFilterEmpty(params));
  const token = getToken();
  const { host, protocol } = location;
  const serverURL = `${protocol}//${host}`;
  window.open(`${serverURL}${url}?${_paramsstr}&Authorization=${token}`);
};
