export const useFormEvent = () => {
  const onKeyup = (e: KeyboardEvent, handler: () => any) => {
    if (e.key !== 'Enter') return;
    const input = e.target as HTMLInputElement;
    if (input.tagName.toUpperCase() !== 'INPUT') return;
    if (input.className !== 'el-input__inner') return;
    if (!input.parentElement) return;
    let className = input.parentElement?.className || '';
    if (!className.includes('el-input')) return;
    // 这里额外处理, 如果是下拉框中的input也跳过,  后期看情况添加其他表单中的input
    className = input.parentElement?.parentElement?.className || '';
    if (className.includes('el-select')) return;
    handler?.();
  };
  return {
    onKeyup,
  };
};

export const transformVue2Slots = (ctx: any) => {
  const slots = new Proxy(ctx as any, {
    get(target, p) {
      const slot = ctx.slots;
      if (p !== 'default' && slot[p]) return slot[p];
      let defaultNode;
      switch (p) {
        case 'default':
          return () => slot?.default()?.filter(t => !t.props?.slot);
        default:
          defaultNode = slot?.default().find(t => t?.props?.slot === p);
          if (!defaultNode) return defaultNode;
          return () => defaultNode;
      }
    },
  });

  return new Proxy(ctx, {
    get(target: any, p: string | symbol, receiver: any): any {
      if (p === 'slots') return slots;
      return Reflect.get(target, p, receiver);
    },
  });
};
