.template {
  flex-direction: column;
  overflow: hidden;

  .searchBar {
    padding: 16px 0 4px 16px;
    display: flex;
    margin-bottom: 10px;
    flex-direction: column;
  }

  .bodyContainer {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    padding: 12px 16px 0 16px;
    background-color: white;

    .btnLine {
      padding-top: 0px;
      padding-bottom: 12px;
      // 重置其他全局样式
      :global {
        .tg-btn {
          margin-bottom: 0 !important;
        }
      }
    }

    .tableContainer {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      :global {
        .el-table-container {
          overflow: hidden;
          flex: 1;
        }
      }
    }
  }
}
