<script src="./index.tsx"></script>
<style lang="scss" scoped>
  .formbtn {
    margin-left: 10px;
  }
  .disfelx {
    display: flex;
  }
  .flex_col {
    display: flex;
    flex-direction: column;
  }
  .pro_div {
    width: 150px;
    border-right: 1px solid #d7dbe7;
    margin-right: 25px;
    height: 240px;
    overflow: hidden;
    overflow-y: scroll;
  }
  .city_div {
    flex: 1;
  }
  .footer {
    font-size: 12px;
  }
  .disfelxcenter {
    display: flex;
    align-items: center;
  }
  .itemdiv {
    width: auto;
    font-size: 12px;
    height: 14px;
    line-height: 14px;
    cursor: pointer;
  }
</style>
