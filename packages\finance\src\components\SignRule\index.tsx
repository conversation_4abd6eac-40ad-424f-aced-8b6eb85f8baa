import { defineComponent, onMounted, ref } from 'vue';
import './index.scss';
import { getPointCouponsList, getSignConfig } from '@/services/pointMall';
import { useRequest } from 'common';
import { ElIcon, ElInput, ElInputNumber } from 'element-plus';
import { Delete } from '@element-plus/icons-vue';
import { MJFuzzySelect } from '@common/components';

const initData = { index: 0, days: null, reward: null, coupon_id: null, coupon_count: null };

export default defineComponent({
  name: 'SignRule',
  setup(props, { expose }) {
    const list = ref<any[]>([initData]);
    const signInfo = ref<string>('');
    const point = ref<number>(0);

    expose({
      list,
      signInfo,
      point,
    });

    const onAdd = () => {
      list.value.push({ ...initData, index: Math.max(...list.value.map(item => item.index)) + 1 });
    };

    const onDelete = item => {
      list.value = list.value.filter(i => i.index !== item.index);
    };

    const getConfigReq = useRequest(getSignConfig, {
      manual: true,
      onSuccess(data) {
        signInfo.value = data.sign_in_rule;
        list.value = data.reward_setting.map((item, index) => {
          return { ...item, index };
        });
        point.value = data.point;
      },
    });

    onMounted(() => {
      getConfigReq.runAsync();
    });

    const changeItem = index => {
      if (!list.value[index].coupon_id) {
        list.value[index].coupon_count = undefined;
      }
    };

    return () => (
      <div class="sign-rule-wrapper">
        <div>奖励发放规则：</div>
        <br />
        <div class="sign-everyday-wrapper">
          每日签到，领取
          <ElInputNumber
            style="width: 60px; margin: 0 8px;"
            precision={0}
            v-model={point.value}
            controls={false}
            min={0}
          />
          积分；
        </div>
        {list.value.map((item, index) => {
          return (
            <div class="sign-list">
              连续签到
              <ElInputNumber
                style="width: 60px; margin: 0 8px;"
                v-model={list.value[index].days}
                controls={false}
                precision={0}
                min={0}
              />
              天，领取
              <ElInputNumber
                style="width: 60px; margin: 0 8px;"
                v-model={list.value[index].reward}
                controls={false}
                precision={0}
                min={0}
              />
              积分，领取
              <MJFuzzySelect
                style="width: 120px; margin: 0 8px;"
                v-model={list.value[index].coupon_id}
                api={getPointCouponsList}
                params={{ issue_type: '5' }}
                clearable
                fuzzyName="name_internal"
                convertLabel="name_internal"
                convertValue="id"
                onChangeItem={() => changeItem(index)}
                extendsOption={
                  list.value[index].coupon_id
                    ? [
                        {
                          key: list.value[index].coupon_id,
                          value: list.value[index].coupon_id,
                          label: list.value[index].name_internal,
                        },
                      ]
                    : []
                }
              />
              <ElInputNumber
                style="width: 60px; margin: 0 8px;"
                v-model={list.value[index].coupon_count}
                controls={false}
                disabled={!list.value[index].coupon_id}
                precision={0}
                min={0}
              />
              张。
              {list.value.length === 1 ? (
                ''
              ) : (
                <ElIcon class="delete-sign-item">
                  <Delete onClick={() => onDelete(item)} />
                </ElIcon>
              )}
            </div>
          );
        })}
        <el-button icon="plus" class="add-sign-item" type="primary" onClick={onAdd}>
          添加
        </el-button>
        <div class="sign-info-title">签到规则说明</div>
        <ElInput
          type="textarea"
          rows={3}
          placeholder="请输入签到规则说明"
          v-model={signInfo.value}
        />
      </div>
    );
  },
});
