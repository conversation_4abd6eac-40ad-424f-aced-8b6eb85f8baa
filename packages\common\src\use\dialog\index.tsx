import {
  reactive,
  ref,
  provide,
  nextTick,
  onUnmounted,
  type Component,
  createApp,
  h,
  onMounted,
  defineComponent,
} from 'vue';
// import store from '@/store'
import './index.scss';
import MjUI from '../../components';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import zhCn from 'element-plus/es/locale/lang/zh-cn';

export interface IUseDialogConfig extends Record<string, any> {
  props?: Record<string, any>;
  on?: Record<string, any>;
  provide?: Record<string, any>;
  component?: Component | string;
  width?: string;
  class?: string;
  footer?: boolean;
  title?: string;
  okText?: string;
  cancelText?: string;
  disabledOK?: boolean;
  /** 是否禁用保存按钮 **/
  disabledSave?: boolean;
  /** 保存文字 **/
  saveText?: string;
  data?: any;
  config?: boolean;
  loading?: boolean;
  /** 单独设置标题style,居中、内外间距等 */
  headerStyle?: Record<string, any>;
}

type AnyFunc = (...args: any) => any;
type EmptyFunc = () => void;
export type IUseDialogResult<T> = {
  show: AnyFunc;
  class?: string;
  close: EmptyFunc;
  update: (config: T) => IUseDialogResult<T>;
  props: (value: any) => IUseDialogResult<T>;
  config: T;
  on: (eventName: string, fn: AnyFunc, rewrite: boolean) => IUseDialogResult<T>;
  emit: (eventName: string, ...args: any[]) => void;
  data?: any;
};
export type IUseDialog<T extends IUseDialogConfig = Record<string, any>> = (
  config: T,
) => IUseDialogResult<T>;

let useDialogId = 1;
const createDialogContainer = () => {
  const div = document.createElement('div');
  div.id = `dialog_${useDialogId++}`;
  document.body.appendChild(div);
  return div;
};

const empty = new Function();
const appendEvents = (
  _uEvents: Record<string, any> | undefined,
  appendEvents: Record<string, any>,
) => {
  let uEvents = _uEvents as NonNullable<typeof _uEvents>;
  if (uEvents === undefined) uEvents = {};
  Object.keys(appendEvents).forEach(key => {
    const oriEvent = uEvents[key] || empty;
    const appendEvent = appendEvents[key];
    uEvents[key] = (...args: any) => {
      if (appendEvent(...args) !== false) {
        return oriEvent(...args);
      }
    };
  });
  return uEvents;
};
// 弹框
export const useDialog: IUseDialog = (config = {}) => {
  const vm = ref();
  const dialogRef = ref<any>();
  const dialogProps = reactive({
    visible: false,
    title: undefined,
    width: undefined,
    footer: true,
    okText: '提交',
    cancelText: '取消',
    disabledOK: false,
    disabledCancel: false,
    loading: false,
    disabledSave: true,
    saveText: '保存',
    headerStyle: {},
    onClose: {},
  });
  const destroyed = ref(true);
  if (typeof config.width === 'number') {
    config.width = `${config.width}px`;
  }
  if (!config.width) {
    config.width = '500px';
  }

  const transformConfig = (config: IUseDialogConfig) => {
    Object.keys(config).forEach(key => {
      if (dialogProps.hasOwnProperty.call(dialogProps, key)) {
        // @ts-ignore
        dialogProps[key] = config[key];
      }
    });
  };
  transformConfig(config);
  const onBind = ref<any>(
    appendEvents(config.on, {
      close() {
        dialogProps.visible = false;
      },
      updateDialog(config = {}) {
        transformConfig(config);
      },
    }),
  );

  function appendComponentEvents() {
    const newObj: any = {};
    for (const key in config.on) {
      const newKey = key.slice(0, 1).toUpperCase() + key.slice(1);
      const onValue = config.on[key];
      newObj['on' + newKey] =
        key === 'close'
          ? () => {
              close();
              onValue;
            }
          : onValue;
    }
    const closeFn = newObj.onClose || close;
    newObj.onClose = closeFn;
    return newObj;
  }

  const componentOn = ref(appendComponentEvents());
  const onSaveBtnClick = () => {
    // const dialogRef = (vm.value.$refs && vm.value.$refs.dialogRef) || {}
    if (dialogRef.value?.dialogSaveSubmit) dialogRef.value?.dialogSaveSubmit();
    else {
      console.log('子组件没有dialogSaveSubmit');
    }
  };

  const onSubmitBtnClick = () => {
    // const dialogRef = (vm.value.$refs && vm.value.$refs.dialogRef) || {}
    if (dialogRef.value?.dialogSubmit) dialogRef.value.dialogSubmit();
    else if (dialogRef.value?.onSaveBtnClick) dialogRef.value.onSaveBtnClick();
    else {
      console.log('子组件没有dialogSubmit或onSaveBtnClick');
    }
  };

  const hasMediumSize = Number(config.width.replace(/[^\d]/g, '')) >= 500;
  const cApp = createApp({
    setup() {
      if (config.provide !== undefined) {
        Object.keys(config.provide).forEach(key => {
          provide(key, (config.provide as any)[key]);
        });
      }
    },
    render() {
      if (!destroyed.value) return undefined as any;
      if (!dialogProps.visible) return undefined as any;
      // 这里很多弹框属性没封装,后期自行扩展
      const dialog_class: string[] = [];
      if (config.class) dialog_class.push(config.class);
      if (dialogProps.footer) dialog_class.push('showFooter');
      if (dialogProps.title) dialog_class.push('showTitle');
      if (hasMediumSize) dialog_class.push('mediumSize');
      return (
        <el-dialog
          class={`bl-dialog-component-container ${dialog_class.join(' ')} `}
          {...dialogProps}
          v-model={dialogProps.visible}
          append-to-body={true}
          close-on-click-modal={false}
          close-on-press-escape={false}
          wrapperClosable={false}
          on={onBind.value}
          isDialog={true}
          // show-close={false}
          modal-class="bl-dialog-component-overlay"
          title={dialogProps.title}
        >
          {{
            default: () => [
              <div class="dialog-new-body">
                {dialogProps.visible &&
                  config.component &&
                  (typeof config.component !== 'string' ? (
                    h(
                      'div',
                      { class: 'dialog-div' },
                      h(config.component, {
                        ref: dialogRef,
                        ...config.props,
                        isDialog: true,
                        'v-loading': true,
                        ...componentOn.value,
                        directives: dialogProps.loading
                          ? [
                              {
                                name: 'loading',
                                value: true,
                              },
                            ]
                          : undefined,
                      }),
                    )
                  ) : (
                    <div class="dialog-str-div" {...config.props}>
                      {config.component}
                    </div>
                  ))}
              </div>,
            ],
            footer: () => {
              return (
                dialogProps.footer && (
                  <div class="footer-buttons">
                    {!dialogProps.disabledCancel && (
                      <el-button disabled={dialogProps.loading} onClick={() => close()}>
                        {dialogProps.cancelText}
                      </el-button>
                    )}
                    {!dialogProps.disabledSave && (
                      <el-button
                        type="primary"
                        style="padding-left: 12px; padding-right: 12px;"
                        disabled={dialogProps.loading}
                        onClick={onSaveBtnClick}
                      >
                        {dialogProps.saveText}
                      </el-button>
                    )}
                    {!dialogProps.disabledOK && (
                      <el-button
                        type="primary"
                        loading={dialogProps.loading}
                        onClick={onSubmitBtnClick}
                      >
                        {dialogProps.okText}
                      </el-button>
                    )}
                  </div>
                )
              );
            },
          }}
        </el-dialog>
      );
    },
  });
  const rootDom = createDialogContainer();
  cApp
    .use(ElementPlus, {
      locale: zhCn,
    })
    .mount(rootDom);
  cApp.use(MjUI);
  vm.value = cApp;
  const show = (...args: any) => {
    if (dialogProps.visible) return;
    dialogProps.visible = true;
    nextTick(() => {
      // const dialogRef = vm.value.$refs && vm.value.$refs.dialogRef
      if (!dialogRef.value) {
        console.log('未找到子组件请检查代码');
        return;
      }
      if (dialogRef.value?.dialogShow) dialogRef.value.dialogShow(...args);
      else if (dialogRef.value?.show) dialogRef.value.show(...args);
    });
  };

  const update = (config: IUseDialogConfig) => {
    transformConfig(config);
    return result;
  };

  const props = (value: any) => {
    config.props = value;
    return result;
  };

  function close() {
    dialogProps.visible = false;
    if (dialogRef.value?.onCloseBtnClick) {
      dialogRef.value.onCloseBtnClick();
    } else {
      emit('close');
      if (document.body.contains(rootDom)) {
        document.body.removeChild(rootDom);
      }
    }
  }

  /**
   * 侦听弹框事件
   * @param eventName 事件名
   * @param fn 侦听函数
   * @param rewrite 是否覆盖
   */
  const on = (eventName: string, fn: any, rewrite = false) => {
    if (rewrite === true) {
      onBind.value[eventName] = fn;
    } else {
      appendEvents(onBind.value, { [eventName]: fn });
    }
    return result;
  };
  onMounted(() => {
    document.body.removeChild(rootDom);
  });
  onUnmounted(() => {
    destroyed.value = false;
    dialogProps.visible = false;
    // vm.value.$destroy();
  });

  const emit = (event: string, ...args: any[]) => {
    onBind.value[event] && onBind.value[event](...args);
  };
  const result = reactive({
    show,
    update,
    on,
    close,
    props,
    config,
    emit,
    data: config.data,
  });

  return result;
};

export const DialogDetail = defineComponent({
  emits: ['save', 'clean'],
  setup(props, ctx) {
    const emitSave = () => ctx.emit('save');
    const emitClean = () => ctx.emit('clean');
    return () => {
      return (
        <div class="mj-dialog-detail">
          <div class="mj-dialog-body">{ctx.slots?.default?.()}</div>
          <div class="mj-dialog-footer">
            <el-button onClick={emitClean}>取消</el-button>
            <el-button type="primary" onClick={emitSave}>
              保存
            </el-button>
          </div>
        </div>
      );
    };
  },
});
