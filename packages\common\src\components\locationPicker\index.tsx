import { defineComponent, onMounted, onUnmounted, PropType, ref } from 'vue';
import AMapLoader from '@amap/amap-jsapi-loader';
import { ElMessage } from 'element-plus';

const AMapSecurityJsCode = '0fe8fb2e648f3e316f0cf6b9ec0e4947';
const AMapSecurityJsKey = '630bb1313397bc68f14ffe2afb9df2f6';
declare const AMapUI: any;
export default defineComponent({
  name: 'MjLocationPicker',
  props: {
    modelValue: {
      type: Object as PropType<{
        lng: number;
        lat: number;
      }>,
    },
    address: {
      type: String,
    },
  },
  setup(props, ctx) {
    const keyword = ref<string>(props.address || '');
    let map: any = null;
    let AMapObj;
    let marker;
    let infoWindow;
    let geocoder;
    onMounted(async () => {
      await initMap();
      map?.on('click', (e: any) => {
        const lnglat = { lng: e.lnglat.getLng(), lat: e.lnglat.getLat() };
        geocoderAddress(lnglat, address => {
          addDot(lnglat, address);
        });
      });
    });

    onUnmounted(() => {
      map?.destroy();
    });

    async function initMap() {
      (window as any)._AMapSecurityConfig = {
        securityJsCode: AMapSecurityJsCode,
      };
      await AMapLoader.load({
        key: AMapSecurityJsKey, // 申请好的Web端开发者Key，首次调用 load 时必填
        version: '2.0', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        plugins: ['AMap.Scale', 'AMap.MapType', 'AMap.Geocoder'], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['...','...']
        AMapUI: {
          version: '1.1',
          plugins: [],
        },
      })
        .then(AMap => {
          AMapObj = AMap;
          // marker = new AMap.Marker();
          geocoder = new AMap.Geocoder({
            // city: "010", //城市设为北京，默认：“全国”
            radius: 1000, //范围，默认：500
          });
          map = new AMap.Map('container', {
            // 设置地图容器id
            // viewMode: "3D", // 是否为3D地图模式
            zoom: 11, // 初始化地图级别
          });

          AMapUI.loadUI(['misc/PoiPicker'], function (PoiPicker) {
            const poiPicker = new PoiPicker({
              input: 'pickerInput',
            });
            //初始化poiPicker
            poiPickerReady(poiPicker);
          });
          if (props.modelValue?.lat && props.modelValue.lng)
            geocoderAddress(props.modelValue, address => {
              addDot(props.modelValue, address);
            });
        })
        .catch(e => {
          console.log(e);
        });
    }

    function addDot(lnglat: any, name?: string) {
      if (marker) {
        marker.setMap(null);
        marker = null;
      }
      const { lng, lat } = lnglat || {};
      marker = new AMapObj.Marker({
        position: new AMapObj.LngLat(lng, lat),
      });
      // map.add(marker);
      infoWindow = new AMapObj.InfoWindow({
        offset: new AMapObj.Pixel(0, -20),
      });
      marker.setMap(map);
      infoWindow.setMap(map);
      // marker.setPosition(lnglat.value);
      infoWindow.setPosition(marker.getPosition());
      infoWindow.setContent(' <pre>' + name + '</pre>');
      infoWindow.open(map, marker.getPosition());
      map.setCenter(marker.getPosition());
      ctx.emit('update:modelValue', { lng, lat });
      keyword.value = name || '';
    }

    function poiPickerReady(poiPicker) {
      // window.poiPicker = poiPicker;

      //选取了某个POI
      poiPicker.on('poiPicked', function (poiResult) {
        const poi = poiResult.item,
          info = poi.name;
        // info = {
        //   source: source,
        //   id: poi.id,
        //   name: poi.name,
        //   location: poi.location.toString(),
        //   address: poi.address,
        // };
        // lnglat.value = poi.location;
        addDot(poi.location, info);
      });

      // poiPicker.onCityReady(function () {
      //   poiPicker.suggest("美食");
      // });
    }

    function geocoderAddress(lnglat: any, succeed: (detailAddress: string) => void) {
      geocoder.getAddress(lnglat, function (status, result) {
        if (status === 'complete' && result.regeocode) {
          const address = result.regeocode.formattedAddress;
          succeed(address);
        } else {
          ElMessage.error('根据经纬度查询地址失败');
        }
      });
    }

    return () => (
      <div class="mj-location-picker-page-container">
        <div class="filter-section">
          <div class="filter-item">
            <span class="label">关键词：</span>
            <input
              id="pickerInput"
              placeholder=""
              v-model={keyword.value}
              // onChange={keyowrdChange}
            ></input>
          </div>
          <div class="filter-item">
            <span class="label">经度：</span>
            <el-input
              disabled
              placeholder=""
              value={props.modelValue?.lng}
              // onChange={lnglatChange}
            ></el-input>
          </div>
          <div class="filter-item">
            <span class="label">纬度：</span>
            <el-input
              disabled
              placeholder=""
              value={props.modelValue?.lat}
              // onChange={lnglatChange}
            ></el-input>
          </div>
        </div>
        <div id="container"></div>
      </div>
    );
  },
});
