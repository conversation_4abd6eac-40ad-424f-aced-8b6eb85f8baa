import { VNode } from 'vue';
import dialogStore from './dialogStore';
import { IUseDialogConfig, useDialog } from '@common/use/dialog';
export type ISelectComponentConfig = IUseDialogConfig & {
  component?: 'store' | VNode;
};
/**
 * 一个较通用的选择弹框
 */
export const useSelectComponent = (config?: ISelectComponentConfig) => {
  const { component = 'store', props = {}, ...rest } = config || {};
  let targetComponent: any = component;
  if (component === 'store') {
    targetComponent = dialogStore;
  }
  const defaultConfig = {
    title: '选择门店',
    width: 1200,
  };
  const dialog = useDialog({
    component: targetComponent,
    props: props,
    ...Object.assign({}, defaultConfig, rest),
  });
  return {
    show(...args) {
      dialog.show(...args);
    },
  };
};
