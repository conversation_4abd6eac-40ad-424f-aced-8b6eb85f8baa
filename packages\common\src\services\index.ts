import { HttpResponse, IGPageQuery, ListResponse } from '@common/types/base/http';
import { ObjectFilterEmpty } from '@common/utils/func';
import { Get } from '@common/utils/request';
import { ICity } from '@common/types/city';

export const get_export_process_list = async (pager: IGPageQuery): Promise<ListResponse<any>> =>
  Get('/api/order/page', {
    params: {
      ...ObjectFilterEmpty({
        ...pager,
      }),
    },
  });

/**
 * 查询级联省份、城市和地区
 */
export const get_resources_cities = async (payload?: {
  level?: number;
}): Promise<HttpResponse<ICity[]>> =>
  Get('/api/resources/cities', {
    params: {
      ...ObjectFilterEmpty(payload),
    },
  });

/**
 * 级联的城市管理列表
 */
export const get_store_city_tree = async (payload?: {
  level?: number;
}): Promise<HttpResponse<ICity[]>> =>
  Get('/api/store_city/tree', {
    params: {
      ...ObjectFilterEmpty(payload),
    },
  });

/**
 * 获取当前登录用户服务城市级联列表
 */
export const get_service_cities_tree = async (): Promise<HttpResponse<ICity[]>> =>
  Get('/api/store_city/service_cities/tree');

/**
 * 代理商级联的城市管理列表
 */
export const get_agents_store_city_tree = async (payload?: {
  level?: number;
}): Promise<HttpResponse<ICity[]>> =>
  Get('/api/store/cities/tree', {
    params: {
      ...ObjectFilterEmpty(payload),
    },
  });

/**
 *  获取当前登录用户服务城市列表
 */
export const get_service_cities = async (
  payload?: any,
): Promise<HttpResponse<{ city_id: number; city_name: string }[]>> =>
  Get('/api/store_city/service_cities', {
    params: {
      ...ObjectFilterEmpty(payload),
    },
  });

/**
 * 获取代理商负责城市列表
 */
export const get_service_agents_cities = async (
  payload?: any,
): Promise<HttpResponse<{ city_id: number; city_name: string }[]>> =>
  Get('/api/store/cities', {
    params: {
      ...ObjectFilterEmpty(payload),
    },
  });

export const get_account_list_dropdown = async (payload: {
  name: string;
  page_size: number;
  page_num: number;
}): Promise<ListResponse<{ username: string; id: number }>> =>
  Get('/api/account/dropdown', {
    params: {
      ...ObjectFilterEmpty({
        ...(payload || {}),
      }),
    },
  });

export const query_agent_list_no_right_codes = async (
  pager: IGPageQuery,
  payload: any,
): Promise<ListResponse<any>> =>
  Get('/api/agent/dropdown', {
    params: {
      ...ObjectFilterEmpty({
        ...pager,
        ...(payload || {}),
      }),
    },
  });
/**
 * 店铺列表-无权限
 */
export const query_store_list_no_right_codes = async (
  pager: IGPageQuery,
  payload: any,
): Promise<ListResponse<any>> =>
  Get('/api/store/dropdown', {
    params: {
      ...ObjectFilterEmpty({
        ...pager,
        ...(payload || {}),
      }),
    },
  });

/**
 * 查询城市下的地区
 */
export const query_city_area = async (payload: any): Promise<HttpResponse<any>> =>
  Get('/api/resources/city/area', {
    params: {
      ...ObjectFilterEmpty({
        ...(payload || {}),
      }),
    },
  });

/**
 * 查询城市负责人
 */
export const query_city_manager_users = async (payload: any): Promise<ListResponse<any>> =>
  Get('/api/store_city/manager_users', {
    params: {
      ...ObjectFilterEmpty({
        ...(payload || {}),
      }),
    },
  });
