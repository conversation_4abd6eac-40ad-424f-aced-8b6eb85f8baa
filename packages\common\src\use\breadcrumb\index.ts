import { HeaderMenuType } from '@common/components/header';
import { deepClone } from '@common/utils/func';
import { inject, onActivated, onDeactivated, onMounted, onUnmounted, provide, Ref } from 'vue';
import { useRoute } from 'vue-router';

export interface BreadcrumbsRoutes {
  /** @deprecated 路由地址 */
  path?: string;
  /** 路由名称 */
  name?: string;
  /** 路由名称(显示用) */
  title: string;
  params?: object;
  query?: object;
}

export type RoutesType = {
  append?: boolean;
  routes: Pick<BreadcrumbsRoutes, 'name' | 'title' | 'params'>[];
};

export const MJCustomBreadcrumbRoutesKey = 'MJCustomBreadcrumbRoutes_Key';
let route: ReturnType<typeof useRoute> = null as any;
export const getCurrentBreadcrumbRoutes = (menus?: HeaderMenuType[]) => {
  const sourceMenuMap = new Map<string, HeaderMenuType>();
  const transformationTreeStructure = (menus, parent) => {
    if (!menus) return;
    for (const menu of menus) {
      menu.parent = parent;
      if (menu.name) sourceMenuMap.set(menu.name, menu);
      transformationTreeStructure(menu.children, menu);
    }
  };
  transformationTreeStructure(menus, null);
  if (!route) {
    route = useRoute();
  }
  let current: HeaderMenuType | undefined = sourceMenuMap.get(route.name as string);
  const result: any[] = [];
  while (current) {
    if (current.name) {
      result.unshift({
        title: current.breadcrumbName || current.label,
        name: current.name,
      });
    }

    current = current.parent;
  }
  if (result.length === 0) {
    result.push({
      title: route.meta?.name || '',
      name: route.name,
    });
  }

  return { values: result };
};

export const provideBreadcrumbRoutes = (routes: Ref<RoutesType>) => {
  provide(MJCustomBreadcrumbRoutesKey, routes);
};

export const useCustomBreadCrumbRoutes = (options: RoutesType) => {
  const customRoutes = inject(MJCustomBreadcrumbRoutesKey) as Ref<RoutesType>;
  onMounted(setRoutes);
  onActivated(setRoutes);
  onUnmounted(resetRoutes);
  onDeactivated(resetRoutes);
  function resetRoutes() {
    console.log('reset Router');
    if (customRoutes) {
      customRoutes.value = {
        append: false,
        routes: [],
      };
    }
  }
  function setRoutes() {
    if (customRoutes) {
      customRoutes.value = deepClone(options);
    }
  }
};
