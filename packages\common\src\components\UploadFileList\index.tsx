import { defineComponent, computed, PropType } from 'vue';
// import utils from '@/utils';
import { downloadFileFromLink, basename } from '../../utils/func';
// import { getToken } from '@/utils/token';
import ImageViewer from '../..//components/Image/ImageViewer';
import { getToken } from '../../config';
import { Close, Document } from '@element-plus/icons-vue';
import { ElIcon } from 'element-plus';
import { supportPreview } from '@common/utils/FormValidation';

const enum UploadFileTheme {
  default = 1,
}

export default defineComponent({
  name: 'MjUploadFileList',
  props: {
    modelValue: {},
    /** 是否显示左侧icon */
    icon: {
      type: Boolean,
      default: true,
    },
    iconUrl: {
      type: String,
      default: '',
    },
    iconStyle: {
      type: String,
      default: '',
    },
    /** 是否显示删除 */
    delete: {
      type: Boolean,
      default: true,
    },
    /** 下载样式 icon|cn **/
    deleteStyle: {
      type: String,
      default: 'icon',
    },
    /** 是否一行展示(辣椒)  */
    inline: {
      type: Boolean,
      default: false,
    },
    /** 一行展示有几列 **/
    column: {
      type: Number,
      default: 1,
    },
    /** 主题风格
     * 1 标题类型
     * 2 图片类型
     * **/
    theme: {
      type: Number as PropType<UploadFileTheme>,
      default: 1,
    },
    /** 是否溢出隐藏 */
    ellipsis: {
      type: String,
      default: '',
    },
    download: {
      type: Boolean,
      default: false,
    },
    auth: {
      type: Boolean,
      default: false,
    },
    /** 是否显示预览 */
    showPreview: {
      type: Boolean,
      default: false,
    },
    previewText: {
      type: String,
      default: '',
    },
    //文件名称宽度
    TitleMaxWidth: {
      type: String,
      default: 'none',
    },
  },
  setup(prop) {
    const className = computed(() => {
      return (
        'upload-file-list' +
        (prop.inline ? ' upload-file-list-inline' : '') +
        (prop.column === 3 ? ' column-3' : '') +
        (prop.column === 2 ? ' column-2' : '') +
        (prop.download ? ' is-download' : '')
      );
    });
    const downloadClick = (file: string) => {
      if (prop.download) downloadFileFromLink(file, prop.auth as boolean);
    };
    /** 预览事件 */
    const proviewOn = (file: string) => {
      const url = `${file}?Authorization=${getToken()}`;
      if (
        file.includes('.png') ||
        file.includes('.jpg') ||
        file.includes('.jpeg') ||
        file.includes('.pdf') ||
        file.includes('.webp')
      ) {
        const hasPDF = /\.pdf\?.+$|\.pdf\??$/.test(file);
        if (hasPDF) {
          window.open(url);
        } else {
          ImageViewer.show([url]);
        }
      } else {
        window.open('https://view.officeapps.live.com/op/view.aspx?src=' + encodeURIComponent(url));
      }
    };
    return {
      proviewOn,
      className,
      downloadClick,
    };
  },
  render() {
    let value: any = this.modelValue;
    if (!!value && !Array.isArray(value)) value = [value];
    if (value?.length === 0) return null as any;
    return (
      <div class={this.className}>
        {value?.map((item: string, key: number) => {
          const m = /\.[^\.]+(\?.+)?$/.exec(item);
          let showExtPreview = this.showPreview;
          if (m) {
            if (!supportPreview.includes(m[0])) showExtPreview = false;
          }

          const iconDom = this.icon && (
            <ElIcon class="icon-doc">
              <Document />
            </ElIcon>
          );
          const filenameDom = this.ellipsis ? (
            <p
              class="file-name"
              v-ellipsis={this.ellipsis}
              style={{ maxWidth: this.TitleMaxWidth }}
            >
              {basename(item)}
            </p>
          ) : (
            <p class="file-name" style={{ maxWidth: this.TitleMaxWidth }}>
              {basename(item)}
            </p>
          );
          let deleteDom;
          if (this.delete) {
            if (this.deleteStyle === 'icon') {
              deleteDom = (
                // <tg-icon
                //   class="icon-delete"
                //   name="ico-a-quseguanbiicon2x"
                //   onClick={() => {
                //     value.splice(key, 1);
                //     this.$emit("delete");
                //   }}
                // />
                <ElIcon
                  class="icon-delete"
                  onClick={() => {
                    value.splice(key, 1);
                    this.$emit('delete');
                  }}
                >
                  <Close />
                </ElIcon>
              );
            } else if (this.deleteStyle === 'cn') {
              deleteDom = (
                <a
                  class="btn-download mgl-8"
                  onclick={() => {
                    value.splice(key, 1);
                    this.$emit('delete');
                  }}
                >
                  删除
                </a>
              );
            }
          }
          return (
            <div class="file-item" key={item + key}>
              {this.$props.iconUrl ? (
                <img style={this.$props.iconStyle} src={this.$props.iconUrl} />
              ) : (
                iconDom
              )}
              {filenameDom}
              {this.$slots.append && this.$slots.append(item)}
              <a class="btn-download" onclick={() => this.downloadClick(item)}>
                下载
              </a>
              {showExtPreview && (
                <a class="btn-pre" onclick={() => this.proviewOn(item)}>
                  {this.previewText || '预览'}
                </a>
              )}
              {deleteDom}
            </div>
          );
        })}
      </div>
    );
  },
});
