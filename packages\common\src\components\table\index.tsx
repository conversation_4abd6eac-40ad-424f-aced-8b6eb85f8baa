/**
 * 基于el-table封装的表格组件
 */
import { defineComponent, PropType, ref, watch, h } from 'vue';
import { IDataTypeEnum, IDataType, MjTableColumn } from '../../types/vendor/column';
// import { numberFormat } from "@/utils/formatMoney";
import moment from 'moment';
import { createEnumStatusLabel } from '../StatusLabel';
import { FD } from '../../utils/formatData';
import { ElTable, ElPagination } from 'element-plus';
import './index.scss';

/** el-table需要调用的方法 */
export const elTableMethod = ['setCurrentRow', 'clearFilter'];
/** 数据显示类型 */
export const dataTypeDefault: { [key in IDataTypeEnum]: IDataType } = {
  text: { type: 'text', toFixed: 2 },
  money: { type: 'money', dec_point: '.', thousands_sep: ',', toFixed: 2 },
  number: { type: 'number' },
  enum: { type: 'enum' },
  date: { type: 'date' },
  datetime: { type: 'datetime' },
  dateMonth: { type: 'dateMonth' },
};

export const replaceServerFormatTime = (value: any) => {
  if (typeof value === 'string') {
    return value.replace(/\./g, '-');
  }
  return value;
};
export default defineComponent({
  name: 'MjTable',
  props: {
    /** 列数组 */
    columns: Array as PropType<MjTableColumn<any>[]>,
    /**
     * 合并行或列的计算方法
     * 类型：Function({ row, column, rowIndex, columnIndex })
     */
    spanMethod: {},
    /**
     * 是否在表尾显示合计行
     * 类型：Boolean
     */
    'show-summary': {},
    showHeader: {},
    /**
     * 自定义的合计计算方法
     * 类型：Function({ columns, data })
     */
    summaryMethod: {},
    /** Table 的高度，默认为自动高度。如果 height 为 number 类型，单位 px；如果 height 为 string 类型，则这个高度会设置为 Table 的 style.height 的值，Table 的高度会受控于外部样式。 */
    height: {
      type: String,
    },
    /** 显示的数据 */
    data: {
      type: Array as PropType<any[]>,
    },
    /**
     * 分页数据
     * 类型：参见el-pagination, https://element.eleme.cn/#/zh-CN/component/pagination
     */
    pagination: {
      type: Object as PropType<any>,
    },
    stripe: {
      type: Boolean,
      default: () => false,
    },
    border: {
      type: Boolean,
      default: () => false,
    },
    sortBy: {},
    defaultSort: {},
    maxHeight: String,
    treeProps: {},
    rowStyle: {},
    cellStyle: {},
    emptyStr: {
      type: String,
      default: '暂无数据',
    },
  },
  components: {
    ElTable,
    ElPagination,
  },
  setup: props => {
    /** 传递给真实table的列配置 */
    const ProcessColumns = ref<undefined | any[]>();

    const map = (children?: MjTableColumn<any>[]) => {
      if (children?.length) {
        return children.map(column => {
          // 读取出所有配置属性
          const result = {
            ...column,
          };
          if (result.children?.length) {
            result.children = map(result.children) as any;
            return result;
          }
          // 没有配置自定义格式化才启用
          if (column.formatter !== undefined) {
            // 支持字符串模板
            if (typeof column.formatter === 'string') {
              result.formatter = (row: any) => {
                const variableRegex = /\{(.+?)\}/g;
                let hasPlusSign = false;
                const formattedString = (column.formatter as string).replace(
                  variableRegex,
                  (_, variable) => {
                    // 判断符,为假返回'--
                    if (variable.startsWith('?')) {
                      const key: string =
                        variable.slice(1) === 'A' ? column.prop : variable.slice(1);
                      const value = row[key];
                      return value || (hasPlusSign = true);
                    }
                    // 如果是表达式, 就使用 eval() 方法执行表达式求值
                    if (/[+/*]/.test(variable)) {
                      const expression = variable.replace(
                        /(\w+)/g,
                        (word: string) => row[word] || word,
                      );
                      try {
                        const value = eval(expression);
                        return value;
                      } catch (error) {
                        console.error(`Error evaluating expression: ${expression}`, error);
                      }
                    }
                    // 支持A默认值
                    const { prop } = column;
                    const value = variable === 'A' && prop ? row[prop] : row[variable];
                    return value || '';
                  },
                );

                return hasPlusSign ? '--' : formattedString;
              };
            } else {
              result.formatter = (...args: any[]) => {
                return (column.formatter as any).apply({ $createElement: h }, args);
              };
            }
          } else {
            let dataTypeConfig: IDataType;
            if (typeof column.dataType === 'string' || typeof column.dataType === 'undefined') {
              dataTypeConfig = dataTypeDefault[column.dataType || 'text'];
            } else {
              dataTypeConfig = {
                ...dataTypeDefault[column.dataType.type || 'text'],
                ...column.dataType,
              };
            }
            /** 兼容层级属性 */
            const getHierarchyValue = (row: any, prop: any): any => {
              let result: any;
              if (!prop) return undefined;
              const props = prop.split('.');
              let parent = row;
              for (const item of props) {
                if (parent === undefined) return parent;
                result = parent[item];
                parent = result;
              }

              return result;
            };

            result.formatter = (row: any) => {
              const OriginalVal = getHierarchyValue(row, column.prop);
              let newVal = OriginalVal;
              // 如果数据没有 则填写 --
              if (OriginalVal === undefined || OriginalVal === null || OriginalVal === '')
                return '--';
              // 如果有数字单位, 那就除以单位, 默认按分处理
              // if (dataTypeConfig.unit && Number(newVal) !== 0) {
              //   newVal = (OriginalVal / dataTypeConfig.unit).toFixed(dataTypeConfig.toFixed);
              // }

              switch (dataTypeConfig.type) {
                case 'money':
                  newVal = FD.formatAmount(newVal, {
                    toFixed: dataTypeConfig.toFixed,
                    div: dataTypeConfig.unit,
                  });
                  break;
                case 'enum':
                  if (dataTypeConfig.colors) {
                    newVal = createEnumStatusLabel(
                      dataTypeConfig.enum,
                      dataTypeConfig.colors,
                    )(newVal);
                  } else {
                    newVal = dataTypeConfig.enum.get(newVal) ?? '--';
                  }
                  break;
                case 'date':
                  newVal = replaceServerFormatTime(newVal);
                  newVal = moment(newVal).format(dataTypeConfig.format || 'YYYY.MM.DD');
                  break;
                case 'dateMonth':
                  newVal = replaceServerFormatTime(newVal);
                  newVal = moment(newVal).format(dataTypeConfig.format || 'YYYY.MM');
                  break;
                case 'datetime':
                  newVal = replaceServerFormatTime(newVal);
                  newVal = moment(newVal).format(dataTypeConfig.format || 'YYYY.MM.DD HH:mm:ss');
                  break;
              }

              // 如果需要补充前缀 就补充前缀
              if (dataTypeConfig.prefix) {
                newVal = `${dataTypeConfig.prefix}${newVal}`;
              }
              // 如果需要补充后缀,就在后面追加后缀
              if (dataTypeConfig.suffix) {
                newVal = `${newVal}${dataTypeConfig.suffix}`;
              }
              return newVal;
            };
          }

          return result;
        });
      } else {
        return [];
      }
    };
    ProcessColumns.value = map(props.columns);

    watch(
      () => props.columns,
      () => {
        ProcessColumns.value = map(props.columns);
      },
      { deep: true },
    );

    const elTableMethods: { [key: string]: () => void } = {};
    const tableRef = ref<any>();
    elTableMethod.forEach(key => {
      elTableMethods[key] = (...args: any[]) => {
        tableRef?.value[key](...args);
      };
    });

    const clearSort = () => {
      tableRef.value?.clearSort();
    };

    const setCurrentRow = (row?: any) => {
      tableRef.value?.setCurrentRow(row);
    };

    const toggleRowSelection = (row: any, selected: boolean = true) => {
      tableRef.value?.toggleRowSelection(row, selected);
    };

    const renderColumns = columns => {
      return columns?.map((col, colIndex) => {
        const { renderHeader, children, ...rest } = col;
        return (
          <el-table-column key={col.key || colIndex} {...rest}>
            {{
              header: renderHeader,
              default: () => renderColumns(children),
            }}
          </el-table-column>
        );
      });
    };

    return {
      tableRef,
      clearSort,
      setCurrentRow,
      toggleRowSelection,
      ProcessColumns,
      renderColumns,
      ...elTableMethods,
    };
  },
  render() {
    const { columns, emptyStr, ...propsRest } = this.$props as any;
    const props = {
      ...this.$attrs,
      ...propsRest,
      stripe: true, // 添加斑马纹效果
    };
    const style: any = {};
    const propsData: any = this.$options?.propsData;
    if (propsData?.height) {
      style.height = propsData.height;
    }
    const pagination: any = this.pagination;
    const empty = this.$slots.empty ? (
      this.$slots.empty()
    ) : (
      <empty-common detail-text={this.emptyStr ? this.emptyStr : '暂无数据'} />
    );
    let showPagination = false;

    if (pagination?.total > 0) {
      showPagination = true;
    }
    const table = (
      <el-table class={['mj-table']} {...props} ref="tableRef">
        {{
          default: () => {
            return (
              <>
                {this.$slots.default?.()}
                {this.ProcessColumns && this.renderColumns(this.ProcessColumns)}
              </>
            );
          },
          empty: () => empty,
        }}
      </el-table>
    );

    if (showPagination === false) return table;
    // console.log('----showPagination', showPagination);
    return (
      <div class="el-table-container" style={style}>
        {table}
        {showPagination && (
          <el-pagination
            background
            layout={pagination.layout || 'total,sizes, prev, pager, next, jumper'}
            pageSize={pagination.page_size}
            pageSizes={pagination.page_sizes}
            currentPage={pagination.page_num}
            total={pagination.total}
            onCurrentChange={$event => pagination.onCurrentChange($event)}
            onSizeChange={$event => pagination.onSizeChange($event)}
          />
        )}
      </div>
    );
  },
});
