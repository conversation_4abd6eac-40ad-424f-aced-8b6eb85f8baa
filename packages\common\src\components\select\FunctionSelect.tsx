import { h, PropType, ref, defineComponent, watch, computed } from 'vue';
import { Select } from './Select';
import { OptionType } from '@common/types/base';
import { useGlobalCity } from '@common/use/globalCity';
import {
  get_account_list_dropdown,
  query_agent_list_no_right_codes,
  query_store_list_no_right_codes,
  query_city_area,
  query_city_manager_users,
  get_service_cities,
} from '@common/services';

export enum EFunctionSelectType {
  /** 系统用户 */
  ACCOUNT_DROPDOWN = 1,
  AGENT_DROPDOWN = 2,
  STORE_DROPDOWN = 3,
  /** 城市区域 **/
  CITY_AREA = 4,
  CITY_MANAGER_USERS = 5,
  /** 品牌端负责城市*/
  BRAND_CITY = 6,
}

export interface IFunctionSelectConfig {
  request?: (value: string) => Promise<OptionType[]>;
  /** 自定义label，目前实现的类型如下，
   * FLOWER_NAME
   * 需要自行添加
   */
  customLabel?: (label: string, item: any) => string;
  otherOption?: () => OptionType[];
}

interface GetOptionsFunc {
  (): OptionType<any>;

  (value: any): any;
}

export interface IFunctionSelectMethod {
  getOptions: GetOptionsFunc;
}

export interface IFunctionSelectRef {
  search: (val?: any) => void;
  options: OptionType[];
  updateOptions: (val?: any[]) => void;
}

export const FunctionSelect = defineComponent({
  name: 'FunctionSelect',
  props: {
    // 自定义配置, 异步返回搜索结果
    config: {
      type: Object as PropType<IFunctionSelectConfig>,
    },
    // 模式类型, 用于常用的搜索
    modeType: {
      type: Number as PropType<EFunctionSelectType>,
      required: true,
    },
    placeholder: {
      type: String as PropType<string>,
    },
    clearable: {
      type: Boolean,
    },
    defaultValue: {
      type: Array as PropType<OptionType[]>,
    },
    size: {
      type: String as PropType<'mini' | 'small'>,
    },
    multiple: {
      type: Boolean,
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    otherParams: {
      type: Object as PropType<any>,
      default: () => ({}),
    },
    showOtherOption: {
      type: Boolean,
    },
    collapseTags: {
      type: Boolean,
    },
    remote: {
      type: Boolean,
      default: () => false,
    },
    onModelChange: {
      type: Function,
    },
    useGlobalCity: {
      type: Boolean,
    },
  },
  emits: ['change'],
  methods: {},
  setup: (props, ctx) => {
    const { cityId } = useGlobalCity({
      onCityChange: val => {
        if (!props.useGlobalCity) return;
        (RemoteMethodMap[props.modeType] as any)();
      },
    });
    const otherOptions = computed(() => {
      if (props.showOtherOption) {
        return (
          props.config?.otherOption?.() || [
            {
              label: '其他',
              value: 0,
            },
          ]
        );
      }
      return [];
    });
    const options = ref<OptionType[]>(props.defaultValue || otherOptions.value);
    const selectModels = ref<OptionType[]>(props.defaultValue || []);
    const RemoteMethodMap: Record<EFunctionSelectType, (value: string) => void> = {
      [EFunctionSelectType.ACCOUNT_DROPDOWN]: (keyword: string) => {
        get_account_list_dropdown({
          name: keyword,
          page_size: 1000,
          page_num: 1,
        }).then(res => {
          options.value =
            res.data?.data?.data?.map((item: any) => {
              return { label: item.username, value: item.id };
            }) || [];
        });
      },
      [EFunctionSelectType.AGENT_DROPDOWN]: (keyword: string) => {
        query_agent_list_no_right_codes(
          {
            page_num: 1,
            page_size: 1000,
          },
          {
            name: keyword,
            global_city_id: cityId.value,
          },
        ).then(res => {
          options.value =
            res.data?.data?.data?.map((item: any) => {
              return { label: item.name, value: item.id };
            }) || [];
        });
      },
      [EFunctionSelectType.STORE_DROPDOWN]: (keyword: string) => {
        query_store_list_no_right_codes(
          {
            page_num: 1,
            page_size: 1000,
          },
          {
            name: keyword,
            global_city_id: cityId.value,
          },
        ).then(res => {
          options.value =
            res.data?.data?.data?.map((item: any) => {
              return { label: item.name, value: item.id };
            }) || [];
        });
      },
      [EFunctionSelectType.CITY_AREA]: (keyword: string) => {
        if (!cityId.value) {
          options.value = [];
          return;
        }
        query_city_area({
          name: keyword,
          city_id: cityId.value,
        }).then(res => {
          options.value =
            res.data?.data?.map((item: any) => {
              return { label: item.name, value: item.id };
            }) || [];
        });
      },
      [EFunctionSelectType.CITY_MANAGER_USERS]: (keyword: string) => {
        query_city_manager_users({
          name: keyword,
          page_num: 1,
          page_size: 1000,
        }).then(res => {
          options.value =
            res.data?.data?.data?.map((item: any) => {
              return { label: item.username, value: item.id };
            }) || [];
        });
      },
      [EFunctionSelectType.BRAND_CITY]: () => {
        get_service_cities().then(res => {
          options.value =
            res.data?.data?.map((item: any) => {
              return { label: item.city_name, value: item.city_id };
            }) || [];
        });
      },
    };

    const propsConfig = props.config;
    const oldSearch = ref('');
    const RemoteMethod = (value: string = '') => {
      oldSearch.value = value;
      if (propsConfig !== undefined && propsConfig.request) {
        propsConfig.request(value).then(data => (options.value = data));
      } else {
        search(value);
      }
    };
    const search = (value: any) => {
      RemoteMethodMap[props.modeType](value);
    };
    /** 搜索前处理函数, 调用外部函数 */
    // const searchBefore = async (value: any): Promise<any> => {
    //   if (props.searchBefore) return await props.searchBefore(value);
    //   return value;
    // };
    /** 初始话请求*/
    watch(
      () => props.defaultValue,
      (newVal: any) => {
        options.value = newVal;
        selectModels.value = newVal || [];
      },
    );
    /** 其他条件修改 */
    watch(
      () => props.otherParams,
      (newVal: Object, oldVal: Object) => {
        if (JSON.stringify(newVal) === JSON.stringify(oldVal)) return;
      },
      {
        deep: true,
      },
    );
    const getOptions = (value?: any) => {
      if (value === undefined) return options.value;
      return options.value.find(item => {
        return item.value === value;
      });
    };
    const onChange = (val: any) => {
      ctx.emit('change', val);
      if (props.onModelChange) {
        if (props.multiple) {
          const filterOptions = options.value.filter(el => val.includes(el.value));
          filterOptions.forEach(el => {
            const finder = selectModels.value.find(subEl => subEl.value === el.value);
            if (!finder) {
              selectModels.value.push(el);
            }
          });
          selectModels.value = selectModels.value.filter(el => val.includes(el.value));
        } else {
          selectModels.value = options.value.filter(el => el.value === val) || [];
        }
        // ctx.emit('modelChange', val, props.multiple ? selectModels.value : selectModels.value[0]);
        props.onModelChange(val, props.multiple ? selectModels.value : selectModels.value[0]);
      }
    };

    if (props.remote === false) {
      (RemoteMethodMap[props.modeType] as any)();
    }
    return () => {
      const selectProps = {
        ...(ctx.attrs || {}),
        ...(props || {}),
        options: options.value,
        onChange: onChange as Function,
        'remote-method': RemoteMethod,
        clearable: true,
      } as any;
      return <Select {...selectProps}></Select>;
    };
  },
});
