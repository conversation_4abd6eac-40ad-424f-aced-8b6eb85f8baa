<script src="./index.tsx"></script>
<style lang="scss" scoped>
.header-line-div {
  padding: 0;
  flex: 1;
  background-color: #fff;
  border-top-right-radius: 12px;
  border-top-left-radius: 12px;
  display: flex;
  justify-content: space-between;

  .header-nav {
    display: flex;
    align-items: center;

    .title-div {
      height: 28px;
      line-height: 28px;
      border: 0;
      font-size: 14px;
      font-weight: 600;
      width: fit-content;
    }

    .desc_title-div {
      height: 28px;
      line-height: 28px;
      color: var(--text-des-color);
      font-size: 12px;
      margin-left: 12px;
      width: fit-content;
    }
  }

  .right-div {
    display: flex;
    align-items: center;
  }
}

.tabs-content {
}
</style>
