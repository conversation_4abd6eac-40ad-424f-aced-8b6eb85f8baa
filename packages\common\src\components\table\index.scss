.el-table-container {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .el-table {
    flex: 1;
    overflow: auto;
    font-size: var(--font-size-default);
  }

  .el-pagination {
    height: 48px;
    position: relative;
    display: flex;
    --pagination-height: 30px;
    background-color: #fff;
    margin: unset;
    border-radius: 0 0 12px 12px;
    padding: 16px 16px 4px;
    justify-content: center;
    align-items: center;
    --el-pagination-button-width: 30px;
    --el-pagination-button-height: 30px;
    --el-pagination-font-size: var(--font-size-default);

    .el-input__wrapper {
      box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) !important;
    }

    .el-pagination__total {
      position: unset;
      margin-right: auto;
      margin-left: 20px;
      color: var(--text-color);
    }

    span.el-pagination__jump {
      margin-right: 20px;
      border: 1px solid #d7dbe7;
      overflow: hidden;
      border-radius: 6px;
      color: var(--text-color);
      padding: 0 10px;
      display: flex;
      line-height: var(--pagination-height);
      height: var(--pagination-height);
      margin-left: auto;

      &.is-last {
        border: none !important;
      }
    }

    &.is-background .btn-next.is-active,
    &.is-background .btn-prev.is-active,
    &.is-background .el-pager li.is-active {
      background: #e9edfd;
      border: none;
      border-radius: 6px;
      padding: 0 8px;
      color: #2c4fec;
    }

    .el-input {
      min-height: var(--default-height);
    }

    .el-input__inner {
      --el-input-inner-height: calc(var(--default-height) - 2px);
    }

    .el-select {
      --el-select-input-font-size: var(--font-size-default);

      .el-select__selected-item {
        font-size: var(--font-size-default);
      }

      .el-select__wrapper {
        min-height: 30px;
      }
    }

    /*
        .el-pagination > .is-last {
          position: absolute;
          right: 12px
        }*/
  }
}
