// vite.config.ts
import { defineConfig, loadEnv } from "file:///D:/job/browser/node_modules/.pnpm/vite@5.4.8_@types+node@22.7.5_sass-embedded@1.79.4_terser@5.36.0/node_modules/vite/dist/node/index.js";
import { fileURLToPath, URL } from "node:url";
import vue from "file:///D:/job/browser/node_modules/.pnpm/@vitejs+plugin-vue@5.1.4_vite@5.4.8_@types+node@22.7.5_sass-embedded@1.79.4_terser@5.36.0__vue@3.5.11_typescript@5.6.3_/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///D:/job/browser/node_modules/.pnpm/@vitejs+plugin-vue-jsx@4.0.1_vite@5.4.8_@types+node@22.7.5_sass-embedded@1.79.4_terser@5.36.0_as2bs6pgpha2udprt5n7yazixi/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import { PluginVueModuleManager } from "file:///D:/job/browser/packages/plugins/plugins-vue-module-manager/index.js";
import { PluginsRequestManager } from "file:///D:/job/browser/packages/plugins/plugins-request-manager/index.js";
var __vite_injected_original_import_meta_url = "file:///D:/job/browser/packages/brand/vite.config.ts";
var vite_config_default = ({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  return defineConfig({
    server: {
      host: "127.0.0.1",
      //自定义主机名
      port: env.VITE_SERVER_PORT ? Number(env.VITE_SERVER_PORT) : 80,
      //自定义端口
      proxy: {
        "/api": {
          target: env.VITE_BASE_URL,
          changeOrigin: true
          // rewrite: (path) => path.replace(/^\/api/, ""),
        }
      }
    },
    plugins: [vue(), vueJsx(), PluginVueModuleManager(), PluginsRequestManager()],
    resolve: {
      extensions: [".vue", ".tsx", ".ts", ".js"],
      alias: {
        "@": fileURLToPath(new URL("./src", __vite_injected_original_import_meta_url)),
        "~@": fileURLToPath(new URL("./src", __vite_injected_original_import_meta_url)),
        "@common": fileURLToPath(new URL("../../packages/common/src", __vite_injected_original_import_meta_url))
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          silenceDeprecations: ["legacy-js-api"]
        }
      }
    }
  });
};
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
