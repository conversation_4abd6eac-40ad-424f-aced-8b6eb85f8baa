import { App } from 'vue';
import EmptyCommon from './empty/emptyCommon.vue';
import Table from './table';
import header from './header';
import sidebar from './sidebar';
import Upload from './Upload/index';
import UploadFileList from './UploadFileList';
import breadcrumbs from './breadcrumbs';
import emptyContainer from './empty/emptyContainer';
import HeaderLine from './HeaderLine';
import Input from './Input';
import MjPopoverSelect from './MjPopoverSelect';
import { Select } from './select/Select';
export { default as GMSelect } from './GMSelect';
export { default as MJFormList } from './templates/form/theme-mj';
export { default as ListMJTemplate } from './templates/list/theme-mj';
export { default as MJFuzzySelect } from './MJFuzzySelect';
export { default as ButtonGroup } from './ButtonGroup';

export default (app: App) => {
  app.component(EmptyCommon.name as string, EmptyCommon);
  app.component(Table.name as string, Table);
  app.component(Input.name as string, Input);
  app.component(header.name as string, header);
  app.component(sidebar.name as string, sidebar);
  app.component(Upload.name as string, Upload);
  app.component(UploadFileList.name as string, UploadFileList);
  app.component(breadcrumbs.name as string, breadcrumbs);
  app.component(emptyContainer.name as string, emptyContainer);
  app.component(HeaderLine.name as string, HeaderLine);
  app.component(MjPopoverSelect.name, MjPopoverSelect);
  app.component('mj-select', Select as any);
};
