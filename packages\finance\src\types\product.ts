import { ProductIsRecommendEnum, ProductStatusEnum } from '@/enums/product';

export interface ProductInfo {
  active_status?: ProductStatusEnum;
  used_city_count?: number;
  add_by?: number;
  detail_img_list?: string[];
  used_city_list?: [];
  discounted_price?: number | string;
  list_price?: number | string;
  id?: number;
  img_list?: string[];
  is_recommend?: ProductIsRecommendEnum;
  modified_by?: number;
  name?: string;
  original_price?: number | string;
  recommend_img?: string | undefined;
  douyin_goods_id?: string;
  goods_code?: string;
  source?: string;
  is_times_card?: boolean;
  /** 是否实物商品 0-否，1-是 */
  is_physical?: number;
}
