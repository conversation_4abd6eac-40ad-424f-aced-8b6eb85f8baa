import { defineStore } from 'pinia';
import { ICity } from '@common/types/city';
interface GlobalCityState {
  /** 城市信息 */
  cityInfo: ICity;
}
/** 获取用户信息 */
export function getNewCityInfo() {
  return {
    id: undefined,
    name: undefined,
  };
}

export const useGlobalCityStore = defineStore('global-city-store', {
  state: (): GlobalCityState => ({
    cityInfo: getNewCityInfo(),
  }),
  getters: {
    cityId: state => state.cityInfo.id,
  },
  actions: {
    /** 重置auth状态 */
    resetCityStore() {
      this.$reset();
    },
    /** 设置用户 */
    setCityInfo(cityInfo: ICity) {
      this.cityInfo = cityInfo;
    },
  },
  persist: true,
});
