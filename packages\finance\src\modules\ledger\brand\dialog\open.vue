<script src="./open.tsx"></script>
<style lang="scss" scoped>
  .btn-line-footer {
    text-align: center;
    padding-top: 5px;
  }

  .mc-meijia-agent-apply-page-container {
    margin-top: -20px;
    box-sizing: border-box;
    text-align: center;
    background-color: white;
    font-size: 14px;
    color: #1a1a1a;
    height: 100%;
    overflow: hidden;
    .img {
      width: 40px;
      height: 40px;
    }
    .line_1 {
      font-family: PingFangSC-Semibold;
      font-weight: 600;
      font-size: 18px;
      color: #000000;
      margin-top: 4px;
    }
    .line_2 {
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 15px;
      color: #000000;
      text-align: center;
      margin-top: 4px;
      margin-bottom: 12px;
    }
  }
</style>
