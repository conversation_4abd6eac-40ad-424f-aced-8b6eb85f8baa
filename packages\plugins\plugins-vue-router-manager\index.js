import path from 'path';
import { spawn } from 'child_process';
import fs from 'fs';
export const PluginVueRouterManager = () => {
  const _check_vue_code_file = async file => {
    let result = file;
    if (file.substring(file.length - 4) !== '.vue') return result;
    let sourceFile = file.substring(0, file.length - 4);

    const check = async (file, extension) => {
      // result = path.join(process.cwd(), `${file}${extension}`);
      result = `${file}${extension}`;
      try {
        fs.accessSync(result);
        return true;
      } catch (ex) {
        return null;
      }
    };
    if (await check(sourceFile, '.tsx')) return result;
    if (await check(sourceFile, '.ts')) return result;
    if (await check(sourceFile, '.vue')) return result;
    if (await check(sourceFile, '/index.tsx')) return result;
    if (await check(sourceFile, '/index.ts')) return result;
    if (await check(sourceFile, '/index.vue')) return result;
    return file;
  };

  let viteConfig = {};

  return {
    name: 'mj-router-manager',
    configResolved(config) {
      viteConfig = config;
    },
    configureServer(server) {
      server.middlewares.use(async (req, res, next) => {
        if (req.url === '/plugins/generate/open') {
          const { file } = req.headers;
          const { VITE_APP_EDITOR_TYPE = 'code' } = viteConfig.env;
          if (file && VITE_APP_EDITOR_TYPE) {
            const targetFile = await _check_vue_code_file(file);
            res.setHeader('content-type', 'text/plain;charset=utf-8');
            res.setHeader('Cache-Control', ' no-cache');
            res.write(`正在启动\n${VITE_APP_EDITOR_TYPE}\n`);
            res.write(targetFile);
            const ls = spawn(VITE_APP_EDITOR_TYPE, [targetFile], {
              cwd: process.cwd(),
              detached: true,
              stdio: [0, 1, 2],
              shell: VITE_APP_EDITOR_TYPE === 'code' ? true : undefined,
            });
            ls.on('error', e => {
              console.error('运行软件报错了', e.message);
              res.write(`打开失败:${e.message}`);
            });
            ls.on('exit', code => {
              res.write(`\n运行结束:${code}`);
              res.end();
            });
          }
          return;
        }
        await next();
      });
    },
  };
};
