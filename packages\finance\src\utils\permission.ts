import { useAuthStore } from '@/store';
import { RightCodeMap } from '@mjGenerate';
type PartialPermission<T> = {
  [p in keyof T]: T[p];
};
export const usePermission = (): PartialPermission<typeof RightCodeMap> => {
  return new Proxy({} as any, {
    get(target, name: string) {
      const store = useAuthStore();
      const right_codes = store.userInfo.right_codes || ([] as any[]);
      const code = RightCodeMap[name];
      return right_codes.includes(code);
    },
  });
};
