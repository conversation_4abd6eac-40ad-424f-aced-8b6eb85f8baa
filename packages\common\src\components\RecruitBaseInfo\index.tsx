import { defineComponent, ref, onMounted } from 'vue';
import iconImage from '../../assets/icon-image.png';
import iconPdf from '../../assets/icon-pdf.png';
import iconFemale from '../../assets/icon-female.png';
import iconMan from '../../assets/icon-man.png';
import iconPhone from '../../assets/icon-phone.png';
export default defineComponent({
  name: 'BaseInfo',
  props: ['baseInfo'],
  setup(props) {
    const storeNameRef = ref<HTMLElement>();
    const isOverflowed = ref(false);

    onMounted(() => {
      if (storeNameRef.value) {
        isOverflowed.value = storeNameRef.value.scrollWidth > storeNameRef.value.clientWidth;
      }
    });
    return () => (
      <div class="base-info">
        <div class="others">
          <div class="left">
            <span class="name">
              {props.baseInfo.value.name}
              <img src={props.baseInfo.value.gender === 1 ? iconMan : iconFemale} />
            </span>
            <span class="age">{props.baseInfo.value.age}岁</span>
            <span class="phone">
              <img src={iconPhone} />
              {props.baseInfo.value.phone}
            </span>
            <span>从业年限：{props.baseInfo.value.work_years}年</span>
            <span>工作经历：{props.baseInfo.value.company}</span>
          </div>
          <div>
            <el-tooltip placement="top" disabled={!isOverflowed.value}>
              {{
                content: () => {
                  return <span>投递门店：{props.baseInfo.value.stores?.join('、')}</span>;
                },
                default: () => {
                  return (
                    <span ref={storeNameRef} class="store-name">
                      投递门店：{props.baseInfo.value.stores?.join('、') || '--'}
                    </span>
                  );
                },
              }}
            </el-tooltip>
          </div>
        </div>
        <div class="file-wrapper">
          <span>相关简历或证书：</span>
          {props.baseInfo.value.attachments.map(item => {
            return (
              <mj-upload-file-list
                style={{
                  'max-width': `${80 / props.baseInfo.value.attachments.length}%`,
                  'margin-right': '24px',
                }}
                iconStyle={{
                  width: '16px',
                  height: '16px',
                }}
                iconUrl={item[0].includes('pdf') ? iconPdf : iconImage}
                delete={false}
                download
                showPreview
                v-model={item}
              />
            );
          })}
        </div>
      </div>
    );
  },
});
