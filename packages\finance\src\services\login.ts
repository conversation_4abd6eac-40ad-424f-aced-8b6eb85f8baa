import { Get, ObjectFilterEmpty, Post } from 'common';
import { HttpResponse } from 'common/src/types/base/http';
import { VerifyCodeResponse, LoginParams } from '@/types/auth';

export const get_login_sms = async (mobile: string, payload: any): Promise<HttpResponse<any>> =>
  Post(
    '/api/auth/login_sms',
    ObjectFilterEmpty({
      mobile,
      ...ObjectFilterEmpty(payload || {}),
    }),
  );

export const get_verify_code = async (): Promise<HttpResponse<VerifyCodeResponse>> =>
  Get('/api/auth/verify_code');

/**
 * 登录
 * <AUTHOR> <<EMAIL>>
 * @since   2021-01-04 13:21:41
 */
export const save_login = async (payload: LoginParams): Promise<HttpResponse<any>> =>
  Post('/api/auth/login', {
    ...ObjectFilterEmpty(payload),
  });
