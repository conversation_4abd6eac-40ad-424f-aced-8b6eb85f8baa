import { Get, ObjectFilterEmpty } from 'common';
import { HttpResponse } from 'common/src/types/base/http';

/** 获取每日经营数据 */
export const get_statistic_daily = async (payload: {
  date: string;
  city_id?: number;
  area_id?: number;
}): Promise<HttpResponse<any>> =>
  Get('/api/statistic/daily', {
    params: {
      ...ObjectFilterEmpty(payload),
    },
  });
/** 获取每月经营数据 */
export const get_statistic_monthly = async (payload: {
  date: string;
  city_id?: number;
  area_id?: number;
}): Promise<HttpResponse<any>> =>
  Get('/api/statistic/monthly', {
    params: {
      ...ObjectFilterEmpty(payload),
    },
  });
/** 获取其他经营数据 */
export const get_statistic_other = async (payload: {
  city_id?: number;
  area_id?: number;
}): Promise<HttpResponse<any>> =>
  Get('/api/statistic/other', {
    params: {
      ...ObjectFilterEmpty(payload),
    },
  });
