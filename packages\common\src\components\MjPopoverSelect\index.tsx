import { computed, defineComponent, PropType, ref, useModel } from 'vue';
import { ElPopover } from 'element-plus';

export default defineComponent({
  name: 'MjPopoverSelect',
  props: {
    modelValue: {
      type: [Number, String],
    },
    visible: {
      type: <PERSON>olean,
    },
    options: {
      type: Array as PropType<Record<string, any>[]>,
      default: () => [],
    },
    optionsProps: {
      type: Object as PropType<{
        label: string;
        value: any;
      }>,
      default: () => ({
        label: 'label',
        value: 'value',
      }),
    },
    inputProps: {},
  },
  emits: ['update:modelValue', 'update:visible', 'change'],
  setup(props, ctx) {
    const keyword = ref('');
    const modelValue = useModel(props, 'modelValue');
    const visible = useModel(props, 'visible');
    const labelKey = props.optionsProps.label;
    const valueKey = props.optionsProps.value;
    const filterOptions = computed(() => {
      return keyword.value
        ? props.options.filter(el => el[labelKey]?.includes(keyword.value))
        : props.options;
    });
    return () => (
      <div class="gm-popover-select-container">
        <ElPopover
          v-model:visible={visible.value}
          teleported={false}
          trigger="hover"
          show-after={300}
          popper-class="gm-popover-select-popover"
          {...ctx.attrs}
        >
          {{
            default: () => (
              <div class="popover-select__popover__inner">
                <el-input
                  clearable
                  class="popover-select__popover__inner-filter-input"
                  vModel_trim={keyword.value}
                  placeholder="请输入"
                  {...(props.inputProps || {})}
                ></el-input>
                <div class="popover-select__popover-list">
                  {filterOptions.value.map(item => (
                    <div
                      class={{
                        'popover-select__popover-list-item': true,
                        'is-active': modelValue.value === item[valueKey],
                      }}
                      key={item[valueKey]}
                      onClick={() => {
                        visible.value = false;
                        keyword.value = '';
                        modelValue.value = item[valueKey];
                        ctx.emit('change', item[valueKey], item);
                      }}
                    >
                      {item[labelKey]}
                    </div>
                  ))}
                </div>
              </div>
            ),
            reference: () => ctx.slots.default?.(),
          }}
        </ElPopover>
      </div>
    );
  },
});
