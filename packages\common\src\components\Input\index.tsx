import { defineComponent, ref } from 'vue';
import InputLimit from '@common/utils/inputLimit';

export default defineComponent({
  name: 'MjInput',
  props: {
    dataType: {
      type: String,
      default: 'text',
    },
    modelValue: {},
    decimalLimit: {
      type: Number,
    },
  },
  emits: ['update:modelValue'],
  setup(props, ctx) {
    const inputRef = ref<any>();
    ctx.expose(
      new Proxy(
        {},
        {
          get(target: {}, p: string | symbol, receiver: any): any {
            if (!inputRef.value) return undefined;
            return Reflect.get(inputRef.value as any, p, receiver);
          },
        },
      ),
    );
    const onUpdateModelValue = (value: string) => {
      let newValue = value;
      switch (props.dataType) {
        case 'number':
          newValue = InputLimit.DigitalLimit(value, {
            decimalLimit: props.decimalLimit,
          });
          break;
        case 'Integer':
          newValue = InputLimit.Interger(value);
          break;
        default:
          break;
      }
      ctx.emit('update:modelValue', newValue);
    };
    return () => {
      const { attrs } = ctx;
      const transfer = { ...attrs };
      return (
        <el-input
          {...transfer}
          modelValue={props.modelValue}
          onUpdate:modelValue={onUpdateModelValue}
        />
      );
    };
  },
});
