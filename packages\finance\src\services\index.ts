import { Get, Post } from 'common';
import { HttpResponse } from 'common/src/types/base/http';

// 上传文件
export const UploadFile = (
  data: Record<string, any>,
): Promise<HttpResponse<{ size: number; source: string }>> => {
  data.append('type', 'any');
  data.append('storage', 1);
  return Post('/api/resources/upload_file', data);
};

/** 获取用户信息 */
export const get_user_info = async (): Promise<HttpResponse<any>> => Get('/api/auth/info');

/** 获取所有门店区域 */
export const get_area_list = async (): Promise<HttpResponse<any>> => Get('/api/store/all_area');
