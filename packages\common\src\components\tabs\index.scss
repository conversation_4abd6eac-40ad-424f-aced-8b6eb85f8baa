.mj-tabs {
  background-color: #ffffff;
  color: #666666;
  border-top-right-radius: 12px;
  border-top-left-radius: 12px;
  border-bottom-left-radius: var(--bm-formlist-semiangle, 12px);
  border-bottom-right-radius: var(--bm-formlist-semiangle, 12px);

  .mj-tabs-header {
    margin: 0 16px;
    display: flex;

    align-items: center;
    border-bottom: 1px solid #d7dbe7;
  }

  &.semi-angle {
    --bm-formlist-semiangle: 0;
  }

  .semi-top {
    border-top-left-radius: var(--bm-formlist-semiangle, 12px);
    border-top-right-radius: var(--bm-formlist-semiangle, 12px);
  }

  .mj-tabs-item {
    min-width: 100px;
    height: 40px;
    padding: 4px 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    cursor: pointer;
    position: relative;

    $radius: 6px;

    &.active {
      cursor: default;
      color: #000;
      font-weight: 600;

      &:after {
        content: '';
        position: absolute;
        left: 10px;
        right: 10px;
        bottom: -1px;
        z-index: 1;
        height: 2px;
        background: #000;
        border-radius: 1px;
      }
    }
  }
}
