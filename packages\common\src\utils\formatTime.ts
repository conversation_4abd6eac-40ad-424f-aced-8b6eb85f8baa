// 格式化日期时间范围格式
// 2025-04-04 17:00~2025-04-04 00:00 转化 2025-04-04 17:00~00:00
// 2025-04-03 17:00~2025-04-04 00:00 转化 2025-04-03 17:00~2025-04-04 00:00
export const formatTime = (() => {
  const format = (dateStr: string) => {
    if (!dateStr) return { date: '', time: '' };
    const [datePart, timePart] = dateStr.split(' ');
    const [hours, minutes] = timePart?.split(':').slice(0, 2) || [];
    return {
      date: datePart,
      time: `${hours || '00'}:${minutes || '00'}`,
    };
  };

  return (start: string, end: string) => {
    if (!start || !end) return '--';

    const startObj = format(start);
    const endObj = format(end);
    const isSameDay = startObj.date === endObj.date;

    return isSameDay
      ? `${startObj.date} ${startObj.time}~${endObj.time}`
      : `${start.replace(/:\d+$/, '')}~${end.replace(/:\d+$/, '')}`;
  };
})();
