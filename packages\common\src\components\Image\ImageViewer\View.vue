<script src="./View.tsx"></script>
<style lang="scss" scoped>
.el-image-viewer__img {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-height: 95%;
  max-width: 95%;
  height: auto;
  width: auto;
  position: relative;
  z-index: 100;

  :deep(.el-image__inner) {
    background-color: rgba(var(--theme-rgb-color), 0.03);
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: 36px 36px;
    transform: scale(1) rotate(0deg);
    margin-left: 0;
    margin-top: 0;
    max-height: 83vh;
    max-width: 90vw;
    object-fit: contain;
  }
}

.pdf-preview {
  svg {
    width: 100%;
    height: 100%;
  }
}

.ml-pagination {
  position: absolute;
  bottom: 34px;
  left: 50%;
  color: white;
  width: 200px;
  height: 30px;
  display: flex;
  align-items: center;
  line-height: 30px;
  z-index: 101;

  .el-pagination-item-list {
    display: flex;
    align-items: center;
    position: absolute;
    left: -50%;
    top: 0;
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 7px;
    letter-spacing: 6px;
  }

  .icon,
  i {
    font-size: 14px;
    // margin-right: 12px;
    color: white !important;
    cursor: pointer;
    &:not(:last-of-type) {
      margin-right: 12px;
    }
  }

  i {
    width: 1em;
  }
}
</style>
