import { update_bank_info } from '@/services/bank';
import { useRequest } from 'common';
import { ElForm, ElMessage, FormInstance } from 'element-plus';
import { defineComponent, ref } from 'vue';

export default defineComponent({
  setup(_, ctx) {
    const uploadRef = ref();
    const formDataRef = ref<FormInstance>();
    const logoList = ref<any>([]);
    const formData = ref({
      id: 0,
      code: '',
      full_name: '',
      logo: '',
      name: '',
      sort_code: 0,
    });
    const rules = {
      name: [{ required: true, message: '请输入银行简称', trigger: 'blur' }],
      full_name: [{ required: true, message: '请输入银行全称', trigger: 'blur' }],
    };

    const reqEditSave = useRequest(update_bank_info, {
      manual: true,
      onSuccess(_, oData) {
        ElMessage.success((oData as any).message);
        ctx.emit('submit');
        close();
      },
    });

    const close = () => {
      ctx.emit('close');
    };

    const show = (row?: any) => {
      formData.value = {
        id: row.id,
        code: row.code,
        full_name: row.full_name,
        logo: row.logo,
        name: row.name,
        sort_code: row.sort_code,
      };
      logoList.value = [row.logo];
    };

    const onSaveBtnClick = () => {
      formDataRef.value?.validate(async valid => {
        if (valid) {
          formData.value.logo = logoList.value.length ? logoList.value[0] : '';
          reqEditSave.runAsync(formData.value.id, formData.value);

          // 提交表单数据
        }
      });
    };

    // 核心
    ctx.expose({ show, onSaveBtnClick });

    return () => (
      <div class="edit-bank-dialog-container">
        <ElForm
          ref={formDataRef}
          style="width: 340px"
          model={formData.value}
          status-icon={false}
          rules={rules}
          label-width="auto"
          class="demo-formData"
        >
          <el-form-item label="银行简称" prop="name">
            <el-input
              maxlength={15}
              v-model={formData.value.name}
              autocomplete="off"
              placeholder="请输入银行简称"
            />
          </el-form-item>
          <el-form-item label="银行全称" prop="full_name">
            <el-input
              maxlength={15}
              v-model={formData.value.full_name}
              autocomplete="off"
              placeholder="请输入银行全称"
            />
          </el-form-item>
          <el-form-item label="接入行行号">
            <el-input
              maxlength={15}
              v-model={formData.value.code}
              autocomplete="off"
              placeholder="请输入接入行行号"
            />
          </el-form-item>
          <el-form-item label="排序">
            <el-input
              maxlength={15}
              v-model={formData.value.sort_code}
              autocomplete="off"
              placeholder="请输入排序"
            />
          </el-form-item>
          <el-form-item label="银行LOGO">
            <div>
              <mj-upload
                ref={uploadRef}
                show-file-list={false}
                action="/api/resources/upload_file"
                data={{ type: 'any' }}
                success={(res: any) => {
                  if (res.success !== true) {
                    ElMessage.error(res.message ?? '上传失败');
                  } else {
                    formData.value.logo = res.data.source;
                    logoList.value = [res.data.source];
                    ElMessage.success(res.message ?? '上传成功');
                  }
                }}
              >
                <el-button loading={uploadRef.value?.loading}>上传</el-button>
              </mj-upload>
              {!!formData.value.logo && (
                <mj-upload-file-list v-model={logoList.value}></mj-upload-file-list>
              )}
            </div>
          </el-form-item>
        </ElForm>
      </div>
    );
  },
});
