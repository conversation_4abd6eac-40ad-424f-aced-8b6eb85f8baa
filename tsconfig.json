{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "composite": false,
    "skipLibCheck": true,
    "baseUrl": ".",

    /* Bundler mode */
    "moduleResolution": "bundler",
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "preserve",
    "noImplicitAny": false,
    /* Linting */
    "strict": true,
    "paths": {
      "@common/*": ["./packages/common/src/*"]
    }
  },
  "include": ["*.ts"]
}
