import { fileURLToPath } from 'node:url';
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue(), vueJsx()],
  resolve: {
    extensions: ['.vue', '.tsx', '.ts', '.js'],
    alias: {
      '@common': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
});
