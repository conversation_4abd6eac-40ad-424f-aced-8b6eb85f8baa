<script src="./index.tsx"></script>
<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;

  .filter-section {
    display: flex;
    background: #ffffff;
    border-radius: 12px;
    align-items: center;
    padding: 12px 16px 0;
    margin-bottom: 12px;

    :deep(.el-form) {
      display: flex;
      flex-wrap: wrap;
      width: 100%;

      .el-form-item {
        margin-bottom: 12px;
        margin-right: 10px;
      }
    }

    .form-content {
      display: grid;
      grid-template-columns: auto 140px 350px;
    }

    .form-item-list {
      display: flex;
      flex-wrap: wrap;
    }
  }

  .table-section {
    flex: 1;
    overflow: hidden;
    background: #ffffff;
    border-radius: 12px;
    padding: 12px 16px;

    :deep(.el-table) {
      .header-container {
        display: flex;
        align-items: center;
        justify-content: flex-end;

        .tips-icon {
          font-size: 14px;
        }
      }

      el-checkbox {
        z-index: 10000;
      }
    }
  }
}
</style>
