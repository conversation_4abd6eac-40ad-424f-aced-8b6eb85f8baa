import { defineComponent } from 'vue';

export default defineComponent({
  props: ['title', 'bodyClass', 'hideHeader'],
  setup(props, ctx) {
    return () => {
      return (
        <div class="detail-card">
          {props.hideHeader !== true && <div class="detail-header">{props.title}</div>}
          <div class={`detail-body ${props.bodyClass ?? ''}`}>{ctx.slots?.default?.()}</div>
        </div>
      );
    };
  },
});
