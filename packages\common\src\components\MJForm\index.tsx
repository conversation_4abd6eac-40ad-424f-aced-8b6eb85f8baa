import { FormInstance } from 'element-plus';
import { defineComponent, getCurrentInstance, ref, watch, Fragment } from 'vue';
import './index.scss';

const MJForm = defineComponent({
  name: 'MJForm',
  setup(props, ctx) {
    const formRef = ref<FormInstance>();

    ctx.expose(
      new Proxy(
        {},
        {
          get(target: {}, p: string | symbol, receiver: any): any {
            if (!formRef.value) return undefined;
            return Reflect.get(formRef.value as any, p, receiver);
          },
        },
      ),
    );

    // watch(
    //   () => formRef.value,
    //   newValue => {
    //     if (!newValue) return;
    //     Object.entries(newValue).forEach(([key, value]) => {
    //       if (typeof value === 'function') {
    //         instance && ((instance.proxy as any)[key] = value.bind(newValue));
    //       }
    //     });
    //   },
    // );
    return () => {
      return (
        <el-form
          class="mj-form"
          ref={formRef.value}
          {...props}
          {...ctx.attrs}
          onSubmit={(e: Event) => {
            e.preventDefault();
            e.stopPropagation();
          }}
        >
          {Object.entries(ctx.slots?.default?.() || {}).map(([key, value]) => {
            return value ? <Fragment slot={key}>{value}</Fragment> : null;
          })}
        </el-form>
      );
    };
  },
});

export default MJForm;
