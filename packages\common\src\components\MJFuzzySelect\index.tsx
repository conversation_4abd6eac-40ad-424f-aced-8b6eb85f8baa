import { computed, defineComponent, PropType, ref } from 'vue';
import './index.scss';
import { ElOption, ElSelect } from 'element-plus';
import { IGPageQuery, ListResponse } from '@common/types/base/http';
import { usePagination } from '@common/utils/requestHooks';

interface IOptionItem {
  key: string | number;
  value: string | number;
  label: string | number;
}

const GMFuzzySelect = defineComponent({
  name: 'MJFuzzySelect',
  props: {
    api: {
      type: Function as PropType<(pager: IGPageQuery, payload: any) => Promise<ListResponse<any>>>,
      required: true,
    },
    params: {
      type: Object as PropType<any>,
    },
    extendsOption: {
      type: Array as PropType<IOptionItem[]>,
    },
    defaultOption: {
      type: Array as PropType<IOptionItem[]>,
    },
    fuzzyName: {
      type: String as PropType<string>,
      default: 'fuzzyName',
    },
    convertLabel: {
      type: String as PropType<string>,
      default: 'name',
    },
    convertValue: {
      type: String as PropType<string>,
      default: 'id',
    },
  },
  emits: ['changeItem'],
  setup(props, { attrs, emit }) {
    const options = ref<IOptionItem[]>(
      (props.defaultOption || []).filter(el => el.value && el.label),
    );
    const getList = usePagination(props.api, {
      manual: true,
      onSuccess(data) {
        options.value = data.map(item => {
          return {
            key: item[props.convertValue],
            value: item[props.convertValue],
            label: item[props.convertLabel],
          };
        });
      },
    });

    const onQueryOption = (fuzzyName: string) => {
      getList.runAsync(
        {
          page_num: 1,
          page_size: 1000,
        },
        {
          [props.fuzzyName]: fuzzyName,
          ...(props.params || {}),
        },
      );
    };

    const onChange = (val: any) => {
      const item = optionList.value?.find(item => item.value === val);
      emit('changeItem', val, item);
    };

    const optionList = computed(() => {
      return [
        ...(attrs.modelValue ? props.extendsOption || [] : []).filter(el => el.label && el.value),
        ...(options.value || []),
      ];
    });

    return () => (
      <ElSelect
        {...attrs}
        loading={getList.loading}
        filterable
        remote
        reserve-keyword
        remoteMethod={onQueryOption}
        onChange={onChange}
      >
        {optionList.value?.map(item => {
          return (
            <ElOption key={item.key} value={item.value} label={item.label}>
              {item.label}
            </ElOption>
          );
        })}
      </ElSelect>
    );
  },
});

export default GMFuzzySelect;
