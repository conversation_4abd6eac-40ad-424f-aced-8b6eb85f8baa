const FDEmptyText = '--';
const FDThousandsSep = ',';
const FDDefaultDiv = 1;
const FDDefaultFixed = 2;
type InputTextType = string | number;
type InputTextTypePartial = InputTextType | null | undefined;
type FormatOptionType = {
  prefix?: string;
  surfix?: string;
  toFixed?: number;
  div?: number;
  emptyText?: string;
  thousandsSep?: string;
};
const numberFormatOption = {
  prefix: '',
  toFixed: FDDefaultFixed,
  surfix: '',
  div: FDDefaultDiv,
  emptyText: FDEmptyText,
  thousandsSep: FDThousandsSep,
};

const CnNumberMap = new Map([
  [0, '零'],
  [1, '一'],
  [2, '二'],
  [3, '三'],
  [4, '四'],
  [5, '五'],
  [6, '六'],
  [7, '七'],
  [8, '八'],
  [9, '九'],
]);
export const FD = {
  /** 默认显示的空文本 */
  emptyText: FDEmptyText,
  FDThousandsSep: FDThousandsSep,
  /** 判断val是否为null 或 undefined */
  isEmpty(val: any) {
    if (val === null || val === undefined || val === '') return true;
    return false;
  },
  /**
   * 格式化文本
   */
  formatText(val: InputTextTypePartial, emptyText = FDEmptyText) {
    return FD.isEmpty(val) ? emptyText : val;
  },
  /** 添加千分符 */
  addThousandsSep(amount: InputTextTypePartial, thousandsSep = FDThousandsSep) {
    if (FD.isEmpty(amount)) return '';
    const str = String(amount || 0);
    const parts = str.split('.');
    const integerPart = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSep);
    const decimalPart = parts[1] ? `.${parts[1]}` : '';
    return integerPart + decimalPart;
  },
  /** 格式化基础方法
   */
  baseNumberFormat(val: InputTextTypePartial, option: FormatOptionType = numberFormatOption) {
    option = { ...numberFormatOption, ...(option || {}) };
    const { prefix, surfix, toFixed, div, emptyText, thousandsSep } = option || {};
    if (FD.isEmpty(val)) {
      return emptyText;
    }
    const newVal = +(val || 0) / (div || FDDefaultDiv);
    const basicPow = Math.pow(10, toFixed || FDDefaultFixed);
    const amountStr = (Math.round(newVal * basicPow) / basicPow).toFixed(toFixed);
    const amountCommasStr = FD.addThousandsSep(amountStr, thousandsSep);
    return prefix + amountCommasStr + surfix;
  },
  /**
   * 格式化数字，参数配置参考 baseNumberFormat
   */
  formatNumber(val: InputTextTypePartial, option: FormatOptionType = numberFormatOption) {
    return FD.baseNumberFormat(val, option);
  },
  /**
   * 格式化比例值，参数配置参考 baseNumberFormat
   */
  formatRatio(val: InputTextTypePartial, option: FormatOptionType = { surfix: '%' }) {
    return FD.baseNumberFormat(val, option);
  },
  /**
   * 格式化金额，参数配置参考 baseNumberFormat
   */
  formatAmount(
    val: InputTextTypePartial,
    option: FormatOptionType = {
      prefix: '￥',
      div: 100,
    },
  ) {
    return FD.baseNumberFormat(val, option);
  },
  /**
   * 替换字符串 - 为 .
   *  **/
  formatTime(value: string): string {
    try {
      if (value === null || value === undefined || value === '') return '--';
      return value.replace(/-/g, '.');
    } catch (val: any) {
      return value;
    }
  },
  numberToChineseNumber: number => {
    const nums = Number(number).toString().split('');
    return nums.map(value => {
      return CnNumberMap.get(Number(value));
    });
  },
  /**格式化金额，分割小数 */
  splitDecimal: number => {
    const parts = Number(number).toFixed(2).split('.');
    return {
      integer: FD.addThousandsSep(parseInt(parts[0])),
      decimal: '.' + parts[1],
    };
  },
};
