import { defineComponent, Fragment, onMounted, ref } from 'vue';
import { MJFormList } from '@common/components';
import { Select } from '@common/components/select/Select';
import { clearingDetailsStatus } from '@/enums/settlement';
import { MjTableColumn } from '@common/types/vendor/column';
import { FD, usePaginationV2, useRequest } from 'common';
import { query_clear_detail_list, retry, review } from '@/services/settlement';
import { ElMessage } from 'element-plus';
import Decimal from 'decimal.js';

export default defineComponent({
  setup() {
    const initFormData = () => {
      return {
        settlement_number: '',
        return_serial_number: '',
        status: '',
        entry_account_name: '',
        exit_account_name: '',
        tradingTime: [],
      };
    };

    const formData = ref(initFormData());

    const columns = ref<MjTableColumn<any>[]>([
      {
        type: 'selection', // 添加选择列
        minWidth: 50,
        align: 'center',
        selectable: row => row.status == clearingDetailsStatus.fail.value, // 添加选择条件
      },
      {
        label: '清分记录编号',
        minWidth: 120,
        align: 'center',
        prop: 'settlement_number',
        showOverflowTooltip: true,
      },
      {
        label: '流水号',
        minWidth: 180,
        align: 'center',
        prop: 'return_serial_number',
        showOverflowTooltip: true,
      },
      {
        label: '金额',
        minWidth: 180,
        align: 'center',
        prop: 'amount__yuan',
        showOverflowTooltip: true,
        formatter: row => {
          return FD.addThousandsSep(row.amount__yuan);
        },
      },
      {
        label: '转出账户',
        minWidth: 180,
        align: 'center',
        prop: 'exit_account_name',
        showOverflowTooltip: true,
      },
      {
        label: '转入账户',
        minWidth: 180,
        align: 'center',
        prop: 'entry_account_name',
        showOverflowTooltip: true,
      },
      {
        label: '交易时间',
        minWidth: 180,
        align: 'center',
        prop: 'operator_datetime',
        showOverflowTooltip: true,
      },
      {
        label: '交易状态',
        minWidth: 180,
        align: 'center',
        prop: 'status',
        dataType: {
          type: 'enum',
          enum: clearingDetailsStatus.toMjMaps(),
        },
      },
      {
        label: '错误信息',
        minWidth: 180,
        align: 'center',
        prop: 'error_message',
        showOverflowTooltip: true,
      },

      {
        label: '操作',
        minWidth: 120,
        align: 'center',
        fixed: 'right',
        formatter: row => {
          return (
            <Fragment>
              {row.status === clearingDetailsStatus.fail.value ? (
                <el-button
                  link
                  type="primary"
                  onClick={() => {
                    multiSelectRetryReq.runAsync({
                      detail_ids: [row.id],
                    });
                  }}
                >
                  重试
                </el-button>
              ) : (
                '--'
              )}
            </Fragment>
          );
        },
      },
    ]);

    const statistics = ref();
    const summaryPropList = ['amount__yuan'];
    const getSummaries = ({ columns }: any) => {
      return columns.map((column, colIdx) => {
        const key = column.property;
        if (colIdx === 0) return '合计';

        if (summaryPropList.includes(key)) {
          return FD.addThousandsSep(statistics.value?.[key]);
        }
        return '';
      });
    };

    const reqList = usePaginationV2(query_clear_detail_list, {
      manual: true,
      onSuccess(_, oData: any) {
        statistics.value = oData.data.statics || {};
      },
    });

    // 多选重试
    const multiSelectRetryReq = useRequest(retry, {
      manual: true,
      onSuccess(_, oData) {
        ElMessage.success((oData as any).message);
        reqList.reload();
      },
    });

    const multipleSelection = ref<any[]>([]);

    const getParams = () => {
      const params = { ...formData.value };
      const start_operator_datetime = params.tradingTime?.[0] ? params.tradingTime[0] : undefined;
      const end_operator_datetime = params.tradingTime?.[1] ? params.tradingTime[1] : undefined;
      return {
        ...params,
        start_operator_datetime,
        end_operator_datetime,
      };
    };

    const onQuery = () => {
      reqList.runAsync(getParams());
    };

    const reset = () => {
      formData.value = initFormData();
      onQuery();
    };

    const handleSelectionChange = selectList => {
      multipleSelection.value = selectList;
    };

    onMounted(async () => {
      await onQuery();
    });

    return () => (
      <div class="page-container">
        <MJFormList onSearch={onQuery} onReset={reset}>
          {{
            default: () => [
              <el-form-item style="width: 144px">
                <el-input
                  clearable
                  placeholder="清分记录编号"
                  vModel_trim={formData.value.settlement_number}
                />
              </el-form-item>,
              <el-form-item style="width: 144px">
                <el-input
                  clearable
                  placeholder="流水号"
                  vModel_trim={formData.value.return_serial_number}
                />
              </el-form-item>,
              <el-form-item style="width: 120px">
                <el-input
                  clearable
                  placeholder="转出账户"
                  vModel_trim={formData.value.exit_account_name}
                />
              </el-form-item>,
              <el-form-item style="width: 120px">
                <el-input
                  clearable
                  placeholder="转入账户"
                  vModel_trim={formData.value.entry_account_name}
                />
              </el-form-item>,
              <el-form-item style="width: 252px">
                <el-date-picker
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  v-model={formData.value.tradingTime}
                  type="daterange"
                  range-separator="~"
                  start-placeholder="交易时间"
                  end-placeholder="交易时间"
                />
              </el-form-item>,

              <el-form-item style="width: 120px">
                <Select
                  v-model={formData.value.status}
                  placeholder="交易状态"
                  options={clearingDetailsStatus.toMjOptions()}
                ></Select>
              </el-form-item>,
            ],
            otherBtns: () => (
              <Fragment>
                <el-button
                  class="mgl-12"
                  // icon="download"
                  type="primary"
                  v-loading={false}
                  onClick={() => {
                    if (!multipleSelection.value.length) {
                      return ElMessage.error('请选择要重试的清分明细');
                    } else {
                      multiSelectRetryReq.runAsync({
                        detail_ids: multipleSelection.value.map(item => item.id),
                      });
                    }
                  }}
                >
                  重试
                </el-button>
              </Fragment>
            ),
          }}
        </MJFormList>
        <section class="table-section">
          <mj-table
            v-loading={reqList?.loading}
            height="100%"
            data={reqList.data}
            columns={columns.value}
            show-summary
            summary-method={getSummaries}
            pagination={{
              ...reqList.pagination,
            }}
            onSelectionChange={handleSelectionChange}
          ></mj-table>
        </section>
      </div>
    );
  },
});
