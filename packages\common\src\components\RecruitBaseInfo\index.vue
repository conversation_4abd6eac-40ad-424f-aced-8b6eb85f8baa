<script src="./index.tsx"></script>
<style lang="scss" scoped>
.base-info {
  height: 76px;
  min-height: 76px;
  max-height: 76px;
  overflow: hidden;
  background: #ffffff;
  padding: 15px 16px;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  justify-content: space-between;

  .others {
    display: flex;
    align-items: center;

    .left {
      min-width: 450px;
      display: flex;
      align-items: flex-end;
    }

    span {
      margin-right: 20px;
      font-size: 12px;
    }

    .name {
      font-family: PingFangSC-Medium;
      font-weight: 500;
      font-size: 16px;
      color: #000000;
      font-weight: 700;
      display: flex;
      align-items: center;

      img {
        width: 18px;
        height: 18px;
        margin-left: 4px;
      }
    }

    .age {
      font-family: PingFangSC-Medium;
      font-weight: 700;
      font-size: 14px;
      color: #000000;
    }

    .phone {
      display: flex;
      align-items: center;

      img {
        width: 14px;
        height: 14px;
      }
    }

    img {
      margin-right: 4px;
    }

    .store-name {
      display: inline-block;
      max-width: 1200px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      vertical-align: bottom;
    }
  }

  .file-wrapper {
    display: flex;
    align-items: center;
    font-size: 12px;

    >* {
      display: inline-block;
    }

    :deep(.file-name) {
      font-size: 12px !important;
    }

    .wrapper {
      max-width: calc(100% - 200px);
      margin-top: 5px;
    }
  }
}
</style>
