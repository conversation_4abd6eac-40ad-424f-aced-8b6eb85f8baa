import { defineStore } from 'pinia';
import { getToken, removeToken, removeUserInfo, getUserInfo } from '@/utils/token';

export interface UserInfo {
  /** 用户手机号 */
  mobile?: string;
  username?: string;
  right_codes?: number[];
  service_cities?: any[];
  role_id?: number | string;
  role_name?: number;
}

interface AuthState {
  /** 用户信息 */
  userInfo: UserInfo;
  /** 用户token */
  token: string;
}

/** 用户信息 */

/*
使用案例
import { useThemeStore } from '@/store';
const themeStore = useThemeStore();
const theme = computed(() => themeStore.theme);
const themeVars = computed(() => themeStore.themeVars);

const themeStore = useThemeStore();
function handleTheme() {
  const mode = themeStore.getTheme === 'light' ? 'dark' : 'light';
  themeStore.setTheme(mode);
}
function handleThemeColor() {
  const color = `#${Math.random().toString(16).substr(2, 6)}`;
  themeStore.setThemeVars({ primaryColor: color, primaryColorEnd: color });
}*/
/** 获取用户信息 */
export function getNewUserInfo() {
  return (
    getUserInfo() || {
      username: '',
      mobile: '',
      right_codes: [],
      service_cities: [],
      role_id: '',
      role_name: '',
    }
  );
}

/** 去除用户相关缓存 */
export function clearAuthStorage() {
  removeToken();
  removeUserInfo();
}

export const useAuthStore = defineStore('auth-store', {
  state: (): AuthState => ({
    userInfo: getNewUserInfo(),
    token: getToken() || '',
  }),
  getters: {
    /** 是否登录 */
    isLogin: state => Boolean(state.token),
    serviceCities: state => state.userInfo.service_cities || [],
  },
  actions: {
    /** 重置auth状态 */
    resetAuthStore() {
      clearAuthStorage();
      this.$reset();
    },
    /** 设置用户 */
    setUseInfo(userInfo: UserInfo) {
      this.userInfo = userInfo;
    },
    /** 设置Token */
    setToken(token: string) {
      this.token = token;
    },
  },
});
