import { Component, computed, defineComponent, PropType } from 'vue';
import { ElPopover } from 'element-plus';
import { RouterLink } from 'vue-router';
import navBg from '../../assets/icon-nav-bg.png';
import headImg from '../../assets/icon-head-img.png';
import { useHeaderMenus } from '@common/use/header';

export type HeaderMenuType = {
  label: string; // 没有设置name，不会被设置在面包屑上
  name?: string /** 在面包屑上显示的名称，优先级高于label */;
  breadcrumbName?: string;
  children?: HeaderMenuType[];
  hidden?: boolean;
  rights?: number[];
  parent?: HeaderMenuType;
  path?: string;
  icon?: Component;
};
export default defineComponent({
  name: 'MjHeader',
  components: {
    RouterLink,
  },
  props: {
    menus: {
      type: Array as PropType<HeaderMenuType[]>,
      required: true,
    },
    rightCodes: {
      type: Array as PropType<number[]>,
    },
    ignoreRight: {
      type: Boolean,
      default: () => true,
    },
  },
  setup(props, ctx) {
    const { transformHeaderMenus, findActiveMenu } = useHeaderMenus();

    const menuList = computed(() => {
      return transformHeaderMenus(props.menus || [], props.rightCodes || [], props.ignoreRight);
    });

    const activeMenu = computed(() => {
      const value = findActiveMenu(menuList.value);
      // console.log("value", value, route.path, router.currentRoute.value.path);
      return value;
    });
    const activeSubMenu = computed(() => {
      return findActiveMenu(activeMenu.value?.children);
    });

    function getDisplayMenus(menus?: any[]) {
      return menus?.filter(el => !el.hidden && el.hasAuth);
    }

    return () => (
      <div class="mj-header-page-container">
        <section class="logo-section">
          <div class="logo">
            <img src={navBg} alt="image" />
          </div>
        </section>
        <section class="menu-section">
          {getDisplayMenus(menuList.value)?.map(menu => {
            const linkBtn = (
              <router-link
                key={menu.label}
                to={menu.path}
                class={{
                  'router-link-exact-active': activeMenu.value === menu,
                  'menu-item': true,
                }}
              >
                {menu.label}
              </router-link>
            );
            if (getDisplayMenus(menu.children)?.length) {
              return (
                <ElPopover
                  popper-class="header-menu-popover"
                  trigger="hover"
                  teleported={false}
                  width="auto"
                >
                  {{
                    reference: () => (
                      <span
                        class={{
                          'router-link-exact-active': activeMenu.value === menu,
                          'menu-item': true,
                        }}
                      >
                        {menu.label}
                      </span>
                    ),
                    default: () => (
                      <div style="display: flex;flex-direction: column; ">
                        {getDisplayMenus(menu.children)?.map(subMenu => (
                          <router-link
                            key={subMenu.label}
                            to={subMenu.path}
                            class={{
                              'router-link-exact-active':
                                activeSubMenu.value?.path === subMenu?.path,
                              'menu-item': true,
                              'in-popover': true,
                            }}
                          >
                            {subMenu.label}
                          </router-link>
                        ))}
                      </div>
                    ),
                  }}
                </ElPopover>
              );
            }
            return linkBtn;
          })}
        </section>
        <section class="user-section">
          <div class="user-div">
            {ctx.slots.message?.()}
            <div class="head-img">
              <img src={headImg} alt="image" />
            </div>
            {ctx.slots.user?.()}
          </div>
        </section>
      </div>
    );
  },
});
