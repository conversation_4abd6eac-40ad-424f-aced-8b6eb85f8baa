import { onBeforeUnmount, onMounted, Ref } from 'vue';

export function useResizeObserver(target: Ref<any>, change: Ref<any>) {
  let resizeObserver: ResizeObserver;
  onMounted(() => {
    if (!target.value) return;
    change.value = target.value.clientWidth;
    resizeObserver = new ResizeObserver(entries => {
      for (const entrin of entries) {
        setTimeout(() => {
          if (entrin.contentBoxSize !== undefined) {
            change.value = entrin.contentBoxSize[0].inlineSize;
          } else {
            change.value = entrin.contentRect.width;
          }
        });
      }
    });
    resizeObserver.observe(target.value);
  });
  onBeforeUnmount(() => {
    if (resizeObserver) {
      resizeObserver.disconnect();
    }
  });
}
