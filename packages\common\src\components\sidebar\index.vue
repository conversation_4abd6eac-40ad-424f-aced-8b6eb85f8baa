<script src="./index.tsx"></script>

<style lang="scss" scoped>
.layout-left-menu-box {
  height: 100%;
  display: flex;
  flex-direction: column;
  z-index: 100;

  .flex-column-100 {
    display: flex;
    flex-direction: column;
    flex: 1 1 100%; // flex-grow, flex-shrink, flex-basis
    min-height: 0; // 防止flex项目溢出容器
    overflow: hidden; // 根据需要添加
  }

  :deep(.el-scrollbar__view) {
    height: 100% !important;

  }

  .layout-left-menu-scroll {
    width: 100%;
    height: 100%;



    .layout-left-menu {
      height: 100%;
      overflow-y: auto;
      // transition: all 0.3s ease;



      .logo-section {
        margin-bottom: 16px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .main-title {
          display: flex;
          vertical-align: bottom;
          transition: all 0.3s ease;


          .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 22px;
            margin-right: 22px;
            transition: all 0.3s ease;

            cursor: pointer;

            img {
              width: 29px;
              height: 22px;
            }
          }

          >div {
            transition: all 0.3s ease;

            opacity: 1;
            width: auto;
            overflow: hidden;
            white-space: nowrap;
          }

          .clear-margin {
            margin: 0;
          }
        }

        .subtitle {
          font-size: 10px;
          color: #AFB0BD;
          margin-top: 4px;
          overflow: hidden;
          white-space: nowrap;
          transition: all 0.3s ease;
        }


      }



      &.el-menu {
        border-right: none;
        padding: 20px;
        box-sizing: border-box;
        background: #FFFFFF;
        box-shadow: 0 2px 12px 0 #1e146a0d;
        border-radius: 10px;




        .el-menu-item {
          border-radius: 4px;
          position: relative;
          z-index: 1;
          margin-bottom: 8px;
          height: 36px;
          // width: 156px;
          // padding-left: 48px;


          &.is-active {
            color: var(--theme-color);
            background-color: #e9edfd;
            height: 36px;

          }


        }

        .el-menu-item:hover {
          height: 36px;

          background-color: #e9edfd;

        }


        .el-menu-item:hover {
          outline: 0 !important;
          background-color: #e9edfd;
          border-radius: 4px;
        }


        &.el-menu--collapse {
          width: 64px; // 收起宽度

          :deep(.el-sub-menu__icon-arrow) {
            display: none;
          }

          .el-sub-menu {
            display: flex;
            justify-content: center;

            .el-sub-menu__title {
              padding: 0 !important;
              justify-content: center;

              .el-icon {
                margin-right: 0 !important;
              }
            }




          }


          .el-menu-item {
            justify-content: center;
            padding: 0 !important;
            width: 100% !important; // 确保宽度填满

            .el-icon {
              margin-right: 0 !important; // 移除图标右边的间距 
            }

            &.is-active {
              color: var(--theme-color);
              background-color: transparent;

            }
          }


        }


      }
    }
  }


  :deep(.el-sub-menu__title:hover) {
    background-color: transparent !important;
  }





}
</style>