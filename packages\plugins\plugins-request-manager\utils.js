const PythonTypeToJsMap = new Map([
  ['int', 'number'],
  ['string', 'string'],
  ['integer', 'number'],
  ['boolean', 'boolean'],
  ['object', 'any'],
  ['path', 'string'],
  ['number', 'number'],
]);
export const getJavascriptType = type => {
  if (PythonTypeToJsMap.has(type)) return PythonTypeToJsMap.get(type);
  console.error(`位置的Python类型:${type} 请补充`);
  return 'any';
};

export const getObjectDeepValue = (obj, name) => {
  if (name === undefined) return undefined;
  return name.split('.').reduce((a, b) => a?.[b], obj);
};
export const findSchemaRefValue = (obj, name) => {
  if (name === undefined) return undefined;
  return name.split('/').reduce((a, b) => {
    if (b === '#') return a;
    return a?.[b];
  }, obj);
};

//生成必填字段的map函数
const isRequire = arr => {
  const requireMap = (arr || []).reduce((current, item) => {
    current[item] = true;
    return current;
  }, {});
  return key => {
    return requireMap[key] ? '' : '?';
  };
};

//对象生成类型
export const genType = data => {
  const { properties, required, type } = data;
  const isRequireMap = isRequire(required);
  if (properties) {
    return `{${Object.entries(properties)
      .map(([key, value]) => {
        if (value.type === 'array') {
          if (value.items.properties) {
            return `
            ${key}${isRequireMap(key)}: ${genType(value.items)}[]`;
          } else {
            return `
            ${key}${isRequireMap(key)}: ${value.items.type}[]`;
          }
        }
        return `
            ${key}${isRequireMap(key)}: ${genType(value)}`;
      })
      .join('')}
        }`;
  } else {
    if (!type) {
      return 'any';
    }
    if (type === 'array') {
      return genType(data.items);
    }
    return getJavascriptType(type) || 'any';
  }
};
