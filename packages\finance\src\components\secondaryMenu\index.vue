<script src="./index.tsx"></script>
<style scoped lang="scss">
  .manager-item {
    border: 1px solid #f0f0f0;
    padding: 16px 14px 16px 14px;
    box-sizing: border-box;
    margin-right: 16px;
    margin-top: 4px;
    cursor: pointer;
    display: flex;
    min-width: 200px;
    height: 68px;
    border-radius: 8px;

    .manager-img-item-content {
      width: 36px;
      height: 36px;
      display: flex;
      margin-right: 12px;
      justify-content: center;
      align-items: center;

      .manager-img-item-icon {
        width: 36px;
        height: 36px;
        background: rgba(#f2b3e1, 0.3);
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
      }
      .manager-img-item {
        width: 22px;
        height: 22px;
      }
    }

    .manager-item-content {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .manager-item-title {
      font-weight: 700;
      font-size: 15px;
    }

    .manager-item-desc {
      font-size: 12px;
      color: #979797;
      margin-top: 2px;
      word-break: normal;
    }
  }
</style>
