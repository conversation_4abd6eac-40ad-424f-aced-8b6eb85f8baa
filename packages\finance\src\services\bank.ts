import { Delete, Get, ObjectFilterEmpty, Post } from 'common';
import { HttpResponse, IGPageQuery, ListResponse } from 'common/src/types/base/http';

/**
 * 查询银行列表
 */

export const query_bank_list = async (
  pager: IGPageQuery,
  payload: any,
): Promise<ListResponse<any>> =>
  Get('/api/clearing/bank/', {
    params: {
      ...ObjectFilterEmpty({
        ...pager,
        ...(payload || {}),
      }),
    },
  });

/**
 * 删除银行
 */

export const delete_bank = async (id: number | undefined): Promise<HttpResponse<undefined>> =>
  Delete(`/api/clearing/bank/${id}`);

/**
 * 编辑银行
 */

export const update_bank_info = async (
  id: number,
  payload: any,
): Promise<HttpResponse<undefined>> => {
  return Post(`/api/clearing/bank/${id}`, {
    ...ObjectFilterEmpty(payload),
  });
};
