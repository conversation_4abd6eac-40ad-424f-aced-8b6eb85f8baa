.bmt-formlist-search-bar {
  background: #ffffff;
  //box-shadow: 0 2px 20px 0 #1e146a0d;
  padding: 12px 16px 12px 16px;
  border-radius: 12px;
  border-top-left-radius: var(--bm-formlist-semiangle, 12px);
  border-top-right-radius: var(--bm-formlist-semiangle, 12px);
  &.semi-angle {
    --bm-formlist-semiangle: 0;
    box-shadow: none;
  }
  .bmt-formlist-form-item-container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    > div {
      margin-top: 12px;
      &:first-child {
        margin-top: unset;
      }
    }
    .el-form-item {
      margin: 0;
      .el-select {
        .el-input--medium .el-input__inner {
          height: var(--el-element-medium-height) !important;
        }
        .el-tag {
          max-width: 130px;
        }
        &.long {
          .el-tag {
            max-width: 180px;
          }
        }

      }
    }
    .bmt-formlist-default-line {
      display: grid;
      grid-template-columns: 1fr;
      grid-gap: 12px;
      align-items: center;
      &.two {
        grid-template-columns: 1fr auto;
      }
    }
    .bmt-formlist-action-area {
      padding-left: 10px;
      .bmt-formlist-form-item-search-operation {
        display: flex;
        align-items: center;
        .bmt-formlist-reset {
          min-width: 36px;
          padding: 0;
          .bm-btn-content {
            display: flex;
            align-items: center;
            justify-content: center;
            column-gap: unset;
            svg {
              font-size: 14px;
            }
          }
        }
      }
    }
    .bmt-formlist-action-fill {
      flex: 1 1 0;
    }
    .bmt-formlist-filter-area {
      flex: 1 1 0;
      overflow: hidden;
      .bmt-formlist-filter-content {
        flex: 0 0 auto;
        overflow: hidden;
        width: initial;
      }
    }
    .bmt-formlist-filter-content,
    .bmt-formlist-extend_line {
      width: 100%;
      display: grid;
      grid-gap: 10px;
      .el-form-item__content {
        & > .el-input,
        & > .el-select {
          width: 100% !important;
        }
      }
    }

    .bmt-formlist-action-area,
    .bmt-formlist-filter-area,
    .bmt-formlist-expansion-zone {
      display: flex;
      align-items: center;
    }
  }
}
