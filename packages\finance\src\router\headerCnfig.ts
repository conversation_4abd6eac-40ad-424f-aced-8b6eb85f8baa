import { HeaderMenuType } from 'common/src/components/header/index';
import {
  RouterNameHome,
  RouterNameSettlement,
  RouterNameLedger,
  RouterNameSyetem,
  RouterNameCommon,
} from './types';
import { DataAnalysis, User, Setting, Tickets } from '@element-plus/icons-vue';

const sourceMenu = [
  // { label: '首页', name: RouterNameHome.home, path: '/home', icon: DataAnalysis },

  {
    label: '结算清分',
    breadcrumbName: '结算清分',
    path: '/settlement',
    icon: Tickets,
    children: [
      {
        label: '清分管理',
        name: RouterNameSettlement.manage,
        breadcrumbName: '清分管理',
        path: '/settlement/manage',
      },
      {
        label: '清分明细',
        name: RouterNameSettlement.details,
        breadcrumbName: '清分明细',
        path: '/settlement/details',
      },
    ],
  },
  {
    label: '台账管理',
    breadcrumbName: '台账管理',
    path: '/ledger',
    icon: User,
    children: [
      {
        label: '品牌台账',
        name: RouterNameLedger.brand,
        breadcrumbName: '品牌台账',
        path: '/ledger/brand',
      },
    ],
  },
  {
    label: '系统管理',
    breadcrumbName: '系统管理',
    path: '/system',
    icon: Setting,
    children: [
      {
        label: '银行管理',
        name: RouterNameSyetem.bank,
        breadcrumbName: '银行管理',
        path: '/system/bank',
      },
      {
        label: '白名单管理',
        name: RouterNameSyetem.whiteList,
        breadcrumbName: '白名单管理',
        path: '/system/whiteList',
        hidden: true,
      },
    ],
  },
];
export const useHeaderConfig = (): HeaderMenuType[] => {
  return sourceMenu;
};
