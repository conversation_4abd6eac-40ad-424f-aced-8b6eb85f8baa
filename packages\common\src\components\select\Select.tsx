import { FComponent, OptionType } from '../../types/base';
import { ElSelect } from 'element-plus';

export interface ISelectProps {
  // 下拉选项
  options?: OptionType<unknown>[];
  // 是否显示全部
  showAll?: boolean;
  showAllText?: string;
  placeholder?: string;
  clearable?: boolean;
  filterable?: boolean;
  size?: 'mini' | string;
  class?: string;
  remote?: boolean;
  multiple?: boolean;
  collapseTags?: boolean;
  disabled?: boolean;
  // 禁用选项
  disabledValues?: any;
  onfocus?: () => void;
  onChange?: (val?: string | number | string[] | number[]) => void;
  onClear?: () => void;
  remoteMethod?: (val: string) => any;
  style?: string;
  disabledOption?: (val: any) => boolean;
}

export const Select: FComponent<ISelectProps, any> = (props, context) => {
  const {
    showAll = false,
    showAllText = '全部',
    clearable = true,
    size,
    multiple,
    collapseTags,
    disabledValues,
    ...rest
  } = props;
  const childProps = {
    size,
    clearable,
    multiple,
    collapseTags,
    ...rest,
    ...context.attrs,
  };

  return (
    <ElSelect {...childProps}>
      {showAll && <el-option label={showAllText} value={undefined} />}
      {props.options?.map((item: any, key: number) => {
        let disabled: boolean = item.disabled;
        if (props.disabledOption) {
          disabled = props.disabledOption(item);
        } else if (disabledValues) {
          disabled = disabledValues.includes(item.value);
        }
        return (
          <el-option
            disabled={disabled}
            key={key + '____' + item.value}
            label={item.label}
            value={item.value}
          >
            {context.slots.option?.(item)}
          </el-option>
        );
      })}
    </ElSelect>
  );
};
Select.props = [
  'options',
  'showAll',
  'showAllText',
  'clearable',
  'multiple',
  'size',
  'collapseTags',
  'disabledValues',
  'onChange',
  'remoteMethod',
  'disabledOption',
  'onClear',
];
