{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "skipLibCheck": true,
    "baseUrl": ".",

    /* Bundler mode */
    "moduleResolution": "bundler",
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "preserve",
    "noImplicitAny": false,
    "paths": {
      "@/*": ["./src/*"],
      "@common/*": ["./node_modules/common/src/*"]
    },
    "types": [],
    /* Linting */
    "strict": true
  },
  "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", ".generate/*.ts"]
}
