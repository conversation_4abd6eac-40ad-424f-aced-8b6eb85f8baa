import { defineConfig, loadEnv } from 'vite';
import { fileURLToPath, URL } from 'node:url';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import { PluginVueModuleManager } from 'plugins/plugins-vue-module-manager';
import { PluginsRequestManager } from 'plugins/plugins-request-manager';
import { PluginVueRouterManager } from 'plugins/plugins-vue-router-manager';
// 1
export default ({ mode }: any) => {
  const env = loadEnv(mode, process.cwd());
  return defineConfig({
    server: {
      host: '127.0.0.1', //自定义主机名
      allowedHosts: true,
      port: env.VITE_SERVER_PORT ? Number(env.VITE_SERVER_PORT) : 80, //自定义端口
      proxy: {
        '/api': {
          target: env.VITE_BASE_URL,
          changeOrigin: true, // rewrite: (path) => path.replace(/^\/api/, ""),
        },
      },
    },
    plugins: [
      vue(),
      vueJsx(),
      PluginVueModuleManager(),
      PluginsRequestManager(),
      PluginVueRouterManager(),
    ],
    resolve: {
      extensions: ['.vue', '.tsx', '.ts', '.js'],
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '~@': fileURLToPath(new URL('./src', import.meta.url)),
        '@common': fileURLToPath(new URL('../../packages/common/src', import.meta.url)),
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          silenceDeprecations: ['legacy-js-api'],
        },
      },
    },
  });
};
