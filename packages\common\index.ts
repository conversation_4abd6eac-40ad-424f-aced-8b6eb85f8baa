import { App } from 'vue';
import MjComponents from './src/components';
import './src/styles/global.scss';
import './src/styles/alias.scss';
import './src/styles/label.scss';
import './src/styles/tailwindcss.scss';

const CommonUI = (app: App) => {
  app.use(MjComponents);
};
export * from './src/utils/request';
export * from './src/utils/func';
export * from './src/utils/requestEventHandler';
export * from './src/utils/eventEmitter';
export * from './src/utils/formatData';
export * from './src/utils/requestHooks';
export * from './src/utils/enum';
export * from './src/service/index';
export * from './src/utils/inputLimit';
export * from './src/utils/location';
export * from './src/utils/FormValidation';
export * from './src/utils/calculation';
export * from './src/utils/formatTime';

export { setupCommonOption } from './src/config';

export * from './src/use/dialog';

export { CommonUI };
