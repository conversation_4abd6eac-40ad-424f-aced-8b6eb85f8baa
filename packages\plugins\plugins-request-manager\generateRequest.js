import axios from 'axios';
import { join } from 'path';
import { pathExists, ensureDir } from 'fs-extra';
import { readFile, writeFile } from 'fs/promises';

const start = async () => {
  console.log('===============================');
  console.log('开始同步原始数据');
  const requestConfig = join(process.cwd(), 'project.config.json');
  console.log('读取配置文件');
  await pathExists(requestConfig);
  const configStr = await readFile(requestConfig, { encoding: 'utf-8' });
  const config = JSON.parse(configStr);
  await ensureDir(join(process.cwd(), config.projectDir));
  console.log('开始请求');
  await axios.get(config.request.api).then(res => {
    console.log('开始写入');
    return writeFile(
      join(process.cwd(), config.projectDir, config.request.sourceName),
      JSON.stringify(res.data, null, '  '),
    );
  });
};
start()
  .then(() => {
    console.log('接口数据源同步完成');
    console.log('===============================');
  })
  .catch(ex => {
    console.error('同步失败:', ex.message);
    console.log('===============================');
    throw new ex();
  });
