const TokenKey = 'Mj-Finance-Auth-Token';
const UserInfoKey = 'Mj-Finance-UserInfo';
const ActcodeKey = 'Mj-Finance-Actcode';
import { UserInfo } from '@/store/modules/auth/index';

export const getToken = () => localStorage.getItem(TokenKey) || '';

export const setToken = (token: string) => {
  if (token) {
    localStorage.setItem(TokenKey, `jwt ${token}`);
  } else {
    localStorage.setItem(TokenKey, '');
  }
};

export const removeToken = () => {
  localStorage.removeItem(TokenKey);
};

export const getUserInfo = () => {
  const user_info = localStorage.getItem(UserInfoKey) || '';
  if (user_info) {
    return JSON.parse(user_info);
  }
  return '';
};
export const setUserInfo = (user_info?: UserInfo) => {
  if (user_info) {
    localStorage.setItem(UserInfoKey, JSON.stringify(user_info));
  } else {
    localStorage.setItem(UserInfoKey, '');
  }
};

export const removeUserInfo = () => {
  localStorage.removeItem(UserInfoKey);
};
export const getCode = () => localStorage.getItem(ActcodeKey) || '';

export const setCode = (code: string) => {
  localStorage.setItem(ActcodeKey, code);
};

export const urlAppendToken = (url: string) => {
  if (url === null || url === undefined) return url;
  // 这里也可以做成, 如果有则替换里面的token
  if (/Authorization=/.test(url)) return url;
  const append = `Authorization=${getToken()}`;
  const searchSymbol = url.includes('?') ? '&' : '?';
  return `${url}${searchSymbol}${append}`;
};
