import { review } from '@/services/settlement';
import { useRequest } from 'common';
import { ElMessage } from 'element-plus';
import { defineComponent, ref } from 'vue';

export default defineComponent({
  setup(_, ctx) {
    const id = ref<number>(0);
    const eventType = ref(false);
    const close = () => {
      ctx.emit('close');
    };

    const dismissal = () => {
      reviewReq.runAsync(id.value, {
        is_pass: false,
      });
      eventType.value = false;
    };

    const pass = () => {
      reviewReq.runAsync(id.value, {
        is_pass: true,
      });
      eventType.value = true;
    };
    const show = (row: any) => {
      id.value = row.id;
    };

    // 复核
    const reviewReq = useRequest(review, {
      manual: true,
      onSuccess(_, oData) {
        ElMessage.success((oData as any).message);
        eventType.value ? ctx.emit('dismissal') : ctx.emit('pass');
        close();
      },
    });

    ctx.expose({ show });

    return () => (
      <div class="review-dialog">
        <span>请审核确认，确认后系统将自动清分</span>
        <div class="btn-group">
          <el-button
            onClick={() => {
              close();
            }}
          >
            取消
          </el-button>
          <el-button
            type="primary"
            plain
            onClick={() => {
              dismissal();
            }}
          >
            驳回
          </el-button>
          <el-button
            type="primary"
            onClick={() => {
              pass();
            }}
          >
            通过
          </el-button>
        </div>
      </div>
    );
  },
});
