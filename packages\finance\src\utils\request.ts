import { requestEventEmitter, setupCommonOption } from 'common';
import { getToken, getCode, removeToken } from '@/utils/token';
import { ElMessage as Message } from 'element-plus';
import { RouterNameHome } from '@/router/types';
import router from '@/router/index';

// 请求拦截
requestEventEmitter.on('RequestFullfilled', config => {
  // 设置header
  config.headers = {
    ...config.headers,
    Authorization: getToken(),
    Actcode: getCode(),
  };
  return config;
});

requestEventEmitter.on('ResponseFullfilled', response => {
  // 处理各种code
  if ([100, 101, 102, 103, 104, 105].includes(response.data.error_code)) {
    if (window.location.pathname !== '/login') {
      Message.error({
        message: '会话超时，请重新登录',
        duration: 1500,
        onClose: async () => {
          const useAuthStore = await import('@/store');
          const authStore = useAuthStore.useAuthStore();
          authStore.setToken('');
          removeToken();
          router.push({
            name: RouterNameHome.login,
            query: {
              redirect: window.location.pathname + window.location.search,
            },
          });
        },
      });
      throw new Error('会话超时，请重新登录');
    }
  }
});
requestEventEmitter.on('ResponseRejected', error => {
  let msg = '';
  if (error.message.includes('timeout')) {
    msg = '请求超时了,请重新尝试';
  } else {
    msg = error.message ?? '服务器出错了';
  }

  // Message.error(msg);
  console.log('error', error, msg);
});

setupCommonOption({
  tokenFn: getToken,
  uploadHeader: () => {
    return {
      Authorization: getToken(),
    };
  },
});
