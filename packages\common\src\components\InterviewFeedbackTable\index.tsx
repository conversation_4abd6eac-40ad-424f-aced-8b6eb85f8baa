import { defineComponent, PropType, reactive, ref, toRefs, Ref, computed, inject } from 'vue';
import { ElTable, ElInputNumber, ElSelect } from 'element-plus';
import { columnsList, mergeCells, formatColumnsList } from './config/index';
import { Select } from '../select/Select';
import {FD} from '../../utils/formatData.js';
export default defineComponent({
  props: {
    list: {
      type: Array as unknown as PropType<Ref<any[]>>,
      default: () => [],
    },
    isEdit: {
      type: Boolean as PropType<Boolean>,
      default: true,
    },
    tableProps: {
      type: Object as PropType<Record<string, any>>,
    },
    scores: {
      type: Array as PropType<Record<string, any>[]>,
    },
    dynamicHead: {
      type: Array as PropType<Record<string, any>[]>,
    },
  },
  components: {
    ElTable,
    ElInputNumber,
    Select: Select as any,
  },
  setup(props, ctx) {
    const indexMap = computed(() => {
      let idx = 0;
      return props.list.value.map((item, index) => {
        if (item.category === props.list.value?.[index - 1]?.category) {
          idx += 1;
        } else {
          idx = 1;
        }
        return idx;
      });
    });
    const { isEdit } = toRefs(props);
    return () => (
      <div class="mj-interview-feedback-table">
        {props.list.value.map((item, index) => {
          return (
            <div class="item">
              {item.category !== props.list.value?.[index - 1]?.category && (
                <div class="category">{item.category}：</div>
              )}
              <div class="title">{`${indexMap.value[index]}. ${item.item}`}</div>
              <el-radio-group v-model={item.score} disabled={!isEdit.value}>
                {item.items.map(i => {
                  return <el-radio label={i.value}>{`${i.name}，${i.label}`}</el-radio>;
                })}
              </el-radio-group>
            </div>
          );
        })}
      </div>
    );
  },
});
