import { defineComponent, ref, computed, nextTick, onMounted } from 'vue';
import { ElTable, ElTableColumn, ElCheckbox, ElCheckboxGroup, ElButton } from 'element-plus';
import { useRequest } from 'common';
import { get_service_cities_tree } from '@common/services';

// 定义 Province 类型
type Province = {
  label: string;
  value: number;
  checked: boolean;
  indeterminate: boolean;
  children: {
    label: string;
    value: number;
    checked: boolean;
  }[];
};

const CitySelector = defineComponent({
  setup(props, ctx) {
    const loading = ref(false);
    const computedProvinceList = computed(() => provinceList.value || []);
    const provinceList = ref();
    const defaultProvinceList = ref([]); //默认
    const cityValue = ref<string>(); //搜索城市

    const checkedProvince = ref<number[]>([]); //选择的省份

    //城市数据
    const cityListShow = ref<any>([]);

    //总数
    const total = computed(() => {
      if (!provinceList.value?.length) return 0; // 添加空值检查
      return provinceList.value.map(item => item.children.length).reduce((a, b) => a + b);
    });
    //全选值
    const checkAllProvince = computed(() => {
      if (!total.value) return false; // 添加空值检查
      return checkedCity.value.length === total.value;
    });
    //全选
    const isAllProvince = computed(() => {
      if (!total.value) return false; // 添加空值检查
      return checkedCity.value.length === 0
        ? false
        : checkedCity.value.length < total.value
        ? true
        : false;
    });
    const checkedCity = computed(() => {
      if (!provinceList.value?.length) return []; // 添加空值检查
      return provinceList.value
        .map(item => item.children.filter(city => city.checked))
        .flat()
        .map(item => item.value);
    });
    const defaultCheckedCity = computed(() => {
      if (!defaultProvinceList.value?.length) return []; // 添加空值检查
      return defaultProvinceList.value
        .map(item => item.children.filter(city => city.checked))
        .flat()
        .map(item => item.value);
    });

    //省全选
    const setAllChecked = async (isChecked: boolean) => {
      if (!provinceList.value?.length) return; // 添加空值检查
      const newProvinceList = provinceList.value.map(province => ({
        ...province,
        checked: isChecked,
        indeterminate: false,
        children: province.children.map(city => ({
          ...city,
          checked: isChecked,
        })),
      }));
      provinceList.value = newProvinceList;

      if (cityListShow.value.length === 0) {
        cityListShow.value = newProvinceList[0].children;
      }

      await nextTick();
    };

    //全选
    const handleCheckAllChange = val => {
      if (!provinceList.value?.length) return; // 添加空值检查
      defaultProvinceList.value = defaultProvinceList.value.map(province => ({
        ...province,
        checked: val,
        indeterminate: false,
        children: province.children.map(city => ({
          ...city,
          checked: val,
        })),
      }));
      checkedProvince.value = val ? provinceList.value.map(item => item.value) : [];
      setAllChecked(val);
    };

    //省份选择 设置城市列表
    const proChangeBox = (isChecked, item, index) => {
      if (!provinceList.value?.length) return; // 添加空值检查
      cityListShow.value = provinceList.value[index].children;

      provinceList.value[index].checked = isChecked;
      provinceList.value[index].indeterminate = false;
      if (isChecked) {
        provinceList.value[index].children.forEach(city => {
          city.checked = true;
        });
      } else {
        provinceList.value[index].children.forEach(city => {
          city.checked = false;
        });
      }
      // 强制更新视图
      provinceList.value = [...provinceList.value];
    };
    //
    const proChangeDiv = (e, val, index) => {
      e.stopPropagation();
      cityListShow.value = provinceList.value[index].children;
    };

    //城市选择
    const CityChange = (value: number[]) => {
      if (!provinceList.value?.length) return;

      // 找到当前显示的城市列表所属的省份
      const currentProvince = provinceList.value.find(province =>
        province.children.some(city => city.value === cityListShow.value[0].value),
      );

      if (!currentProvince) return;

      // 更新当前省份下所有城市的选中状态（包括未显示的）
      const allChildren = currentProvince.allChildren || currentProvince.children;
      currentProvince.children = currentProvince.children.map(city => ({
        ...city,
        checked: value.includes(city.value),
      }));

      if (currentProvince.allChildren) {
        currentProvince.allChildren = currentProvince.allChildren.map(city => ({
          ...city,
          checked:
            value.includes(city.value) ||
            (city.checked && !currentProvince.children.some(c => c.value === city.value)),
        }));
      }

      // 同步更新 defaultProvinceList
      const defaultProvinceIndex = defaultProvinceList.value.findIndex(
        province => province.value === currentProvince.value,
      );

      if (defaultProvinceIndex !== -1) {
        defaultProvinceList.value[defaultProvinceIndex].children = defaultProvinceList.value[
          defaultProvinceIndex
        ].children.map(city => ({
          ...city,
          checked: currentProvince.allChildren
            ? currentProvince.allChildren.find(c => c.value === city.value)?.checked || false
            : value.includes(city.value),
        }));
      }

      // 计算当前省份下选中的城市数量
      const totalCities = currentProvince.children.length;
      const checkedCitiesCount = currentProvince.children.filter(city =>
        value.includes(city.value),
      ).length;

      // 更新省份的选中和半选状态
      if (checkedCitiesCount === 0) {
        // 没有选中任何城市
        currentProvince.checked = false;
        currentProvince.indeterminate = false;
        // 同步更新 defaultProvinceList
        if (defaultProvinceIndex !== -1) {
          defaultProvinceList.value[defaultProvinceIndex].checked = false;
          defaultProvinceList.value[defaultProvinceIndex].indeterminate = false;
        }
        // 从选中的省份列表中移除
        checkedProvince.value = checkedProvince.value.filter(id => id !== currentProvince.value);
      } else if (checkedCitiesCount === totalCities) {
        // 选中了所有城市
        currentProvince.checked = true;
        currentProvince.indeterminate = false;
        // 同步更新 defaultProvinceList
        if (defaultProvinceIndex !== -1) {
          defaultProvinceList.value[defaultProvinceIndex].checked = true;
          defaultProvinceList.value[defaultProvinceIndex].indeterminate = false;
        }
        // 确保省份被选中
        if (!checkedProvince.value.includes(currentProvince.value)) {
          checkedProvince.value.push(currentProvince.value);
        }
      } else {
        // 部分城市被选中
        currentProvince.checked = false;
        currentProvince.indeterminate = true;
        // 同步更新 defaultProvinceList
        if (defaultProvinceIndex !== -1) {
          defaultProvinceList.value[defaultProvinceIndex].checked = false;
          defaultProvinceList.value[defaultProvinceIndex].indeterminate = true;
        }
        // 从选中的省份列表中移除
        checkedProvince.value = checkedProvince.value.filter(id => id !== currentProvince.value);
      }

      // 在更新完城市状态后，确保调用更新省份状态的方法
      updateProvinceCheckStatus();

      // 强制更新视图
      provinceList.value = [...provinceList.value];
      defaultProvinceList.value = [...defaultProvinceList.value];
    };
    // const handleCityItemChange = (checked: boolean, cityValue: number) => {
    //   if (!provinceList.value?.length) return;

    //   // 找到当前显示的城市列表所属的省份
    //   const currentProvince = provinceList.value.find(province =>
    //     province.children.some(city => city.value === cityListShow.value[0].value),
    //   );

    //   if (!currentProvince) return;

    //   // 获取当前选中值列表
    //   const newCheckedValues = checked
    //     ? [...checkedCity.value, cityValue]
    //     : checkedCity.value.filter(v => v !== cityValue);

    //   // 更新当前省份下所有城市的选中状态
    //   currentProvince.children = currentProvince.children.map(city => ({
    //     ...city,
    //     checked: newCheckedValues.includes(city.value),
    //   }));

    //   // 同步更新 defaultProvinceList 中对应省份的城市选中状态
    //   const defaultProvinceIndex = defaultProvinceList.value.findIndex(
    //     province => province.value === currentProvince.value,
    //   );

    //   if (defaultProvinceIndex !== -1) {
    //     defaultProvinceList.value[defaultProvinceIndex].children = defaultProvinceList.value[
    //       defaultProvinceIndex
    //     ].children.map(city => ({
    //       ...city,
    //       checked: newCheckedValues.includes(city.value),
    //     }));
    //   }

    //   // 计算当前省份下选中的城市数量
    //   const totalCities = currentProvince.children.length;
    //   const checkedCitiesCount = currentProvince.children.filter(city =>
    //     newCheckedValues.includes(city.value),
    //   ).length;

    //   // 更新省份的选中和半选状态
    //   if (checkedCitiesCount === 0) {
    //     // 没有选中任何城市
    //     currentProvince.checked = false;
    //     currentProvince.indeterminate = false;
    //     // 同步更新 defaultProvinceList
    //     if (defaultProvinceIndex !== -1) {
    //       defaultProvinceList.value[defaultProvinceIndex].checked = false;
    //       defaultProvinceList.value[defaultProvinceIndex].indeterminate = false;
    //     }
    //     // 从选中的省份列表中移除
    //     checkedProvince.value = checkedProvince.value.filter(id => id !== currentProvince.value);
    //   } else if (checkedCitiesCount === totalCities) {
    //     // 选中了所有城市
    //     currentProvince.checked = true;
    //     currentProvince.indeterminate = false;
    //     // 同步更新 defaultProvinceList
    //     if (defaultProvinceIndex !== -1) {
    //       defaultProvinceList.value[defaultProvinceIndex].checked = true;
    //       defaultProvinceList.value[defaultProvinceIndex].indeterminate = false;
    //     }
    //     // 确保省份被选中
    //     if (!checkedProvince.value.includes(currentProvince.value)) {
    //       checkedProvince.value.push(currentProvince.value);
    //     }
    //   } else {
    //     // 部分城市被选中
    //     currentProvince.checked = false;
    //     currentProvince.indeterminate = true;
    //     // 同步更新 defaultProvinceList
    //     if (defaultProvinceIndex !== -1) {
    //       defaultProvinceList.value[defaultProvinceIndex].checked = false;
    //       defaultProvinceList.value[defaultProvinceIndex].indeterminate = true;
    //     }
    //     // 从选中的省份列表中移除
    //     checkedProvince.value = checkedProvince.value.filter(id => id !== currentProvince.value);
    //   }

    //   // 强制更新视图
    //   provinceList.value = [...provinceList.value];
    //   defaultProvinceList.value = [...defaultProvinceList.value];
    // };
    //搜索城市
    const searchCities = e => {
      if (!e && e !== '') return;

      // 如果搜索词为空，直接调用重置方法
      if (e === '') {
        reloadCities();
        return;
      }

      const searchText = String(e).toLowerCase();

      // 保存当前的选中状态
      const currentCheckedCities = new Set([...checkedCity.value]);

      // 创建一个新的过滤后的省份列表
      const filteredProvinceList = defaultProvinceList.value
        .map(province => {
          // 过滤符合条件的城市
          const matchedCities = province.children.filter(city =>
            city.label.toLowerCase().includes(searchText),
          );

          if (matchedCities.length > 0) {
            // 获取原始省份的所有选中状态
            const originalProvince = provinceList.value.find(p => p.value === province.value);

            // 保留所有城市但设置可见性
            const allCities = province.children.map(city => {
              const originalCity = originalProvince?.children.find(c => c.value === city.value);
              return {
                ...city,
                checked: originalCity?.checked || currentCheckedCities.has(city.value),
                visible: matchedCities.some(matchedCity => matchedCity.value === city.value),
              };
            });

            return {
              ...province,
              checked: originalProvince?.checked || false,
              indeterminate: originalProvince?.indeterminate || false,
              // 保存所有城市，但只显示匹配的
              children: allCities.filter(city => city.visible),
              // 保存完整的城市列表用于状态维护
              allChildren: allCities,
            };
          }
          return null;
        })
        .filter(Boolean) as Province[];

      // 更新省份列表
      provinceList.value = filteredProvinceList;

      // 如果有搜索结果，显示第一个省份的城市列表
      if (filteredProvinceList.length > 0) {
        cityListShow.value = filteredProvinceList[0].children;
      } else {
        cityListShow.value = [];
      }

      // 更新省份选中状态
      updateProvinceCheckStatus();
    };

    //重置方法
    const reloadCities = () => {
      // 清空搜索输入
      cityValue.value = '';

      // 从 defaultProvinceList 恢复列表，保持所有选中状态
      provinceList.value = defaultProvinceList.value.map(province => {
        // 获取该省份下所有选中的城市数量
        const children = province.children.map(city => ({
          ...city,
          checked: city.checked,
        }));

        // 计算省份的选中状态
        const checkedCount = children.filter(city => city.checked).length;
        const totalCount = children.length;

        return {
          ...province,
          checked: checkedCount === totalCount,
          indeterminate: checkedCount > 0 && checkedCount < totalCount,
          children,
        };
      });

      // 显示第一个省份的城市列表，优先显示有选中城市的省份
      const firstProvinceWithSelection = provinceList.value.find(province =>
        province.children.some(city => city.checked),
      );

      cityListShow.value = firstProvinceWithSelection
        ? firstProvinceWithSelection.children
        : provinceList.value[0].children;

      // 更新省份的选中状态
      checkedProvince.value = provinceList.value
        .filter(province => province.checked || province.indeterminate)
        .map(province => province.value);

      // 同步更新 defaultProvinceList 的状态
      defaultProvinceList.value = defaultProvinceList.value.map(province => {
        const provinceInList = provinceList.value.find(p => p.value === province.value);
        return {
          ...province,
          checked: provinceInList?.checked || false,
          indeterminate: provinceInList?.indeterminate || false,
        };
      });

      // 强制更新视图
      provinceList.value = [...provinceList.value];
      defaultProvinceList.value = [...defaultProvinceList.value];
    };

    // 更新省份选中状态的辅助方法
    const updateProvinceCheckStatus = () => {
      checkedProvince.value = provinceList.value
        .filter(province => province.checked || province.indeterminate)
        .map(province => province.value);
    };

    const onSaveBtnClick = () => {
      if (!defaultProvinceList.value?.length) {
        return;
      }
      const checked = [...new Set([...checkedCity.value, ...defaultCheckedCity.value])];
      // 从所有省份中获取选中的城市信息
      const selectedCities = checked
        .map(cityId => {
          for (const province of defaultProvinceList.value) {
            const city = province.children.find(city => city.value === cityId);
            if (city) return city;
          }
          return null;
        })
        .filter(Boolean);
      ctx.emit('submit', checked, selectedCities);
      ctx.emit('close');
    };

    // 初始化选中状态
    const getVal = val => {
      if (!provinceList.value?.length) return;

      // 重置所有省份选中状态
      provinceList.value.forEach(province => {
        if (!province?.children) return;

        const selectedCities = province.children.filter(city => val.includes(city.value)).length;

        // 更新省份选中状态
        province.checked = selectedCities === province.children.length;
        province.indeterminate = selectedCities > 0 && selectedCities < province.children.length;

        // 同步城市选中状态
        province.children.forEach(city => {
          city.checked = val.includes(city.value);
        });
      });

      // 更新选中的省份
      checkedProvince.value = provinceList.value
        .filter(province => province.checked || province.indeterminate)
        .map(province => province.value);

      // 显示第一个有选中城市的省份列表
      const firstValidProvince = provinceList.value.find(province =>
        province.children.some(city => val.includes(city.value)),
      );

      if (firstValidProvince) {
        cityListShow.value = firstValidProvince.children;
      } else if (provinceList.value.length > 0) {
        cityListShow.value = provinceList.value[0].children;
      }

      // 只在必要时调用 CityChange
      if (provinceList.value.length > 0) {
        CityChange(val);
      }
    };

    // 添加一个初始化完成的标志
    const initPromise = ref<Promise<void>>();
    const show = async val => {
      await initPromise.value;
      getVal(val || []);
    };

    const getTree = useRequest(get_service_cities_tree, {
      manual: true,
      onSuccess(_, oData) {},
    });

    onMounted(async () => {
      // 将初始化过程包装在 Promise 中
      initPromise.value = (async () => {
        loading.value = true;
        const { data } = await getTree.runAsync({ level: 2 });
        const cityTree = data.data || [];
        defaultProvinceList.value = cityTree.map(item => {
          if (!item) return null;
          return {
            label: item.name || '',
            value: item.id || 0,
            checked: false,
            indeterminate: false,
            children: Array.isArray(item.children)
              ? item.children
                  .map(child => ({
                    label: child?.name || '',
                    value: child?.id || 0,
                    checked: false,
                  }))
                  .filter(Boolean)
              : [],
          };
        });
        provinceList.value = [...defaultProvinceList.value];
        loading.value = false;
        await nextTick();
      })();
    });

    ctx.expose({ show, onSaveBtnClick });

    return () => (
      <div>
        <el-Form>
          <el-form-item label="城市：">
            <el-col span={11}>
              <mj-input
                v-model={cityValue.value}
                maxlength={10}
                onInput={val => searchCities(val)}
              />
            </el-col>
            <ElButton class="formbtn" type="primary" onClick={() => searchCities(cityValue.value)}>
              搜索
            </ElButton>
            <ElButton class="formbtn" onClick={reloadCities}>
              重置
            </ElButton>
          </el-form-item>
        </el-Form>

        <el-divider />
        <div class="disfelx" v-loading={loading.value}>
          <div class="pro_div">
            <el-checkbox
              v-model={checkAllProvince.value}
              indeterminate={isAllProvince.value}
              onChange={handleCheckAllChange}
            >
              全部
            </el-checkbox>
            <el-checkbox-group class="flex_col" v-model={checkedProvince.value}>
              {(computedProvinceList.value || []).map((item, index) => {
                return (
                  <div class="disfelxcenter">
                    <el-checkbox
                      key={item.value}
                      value={item.value}
                      indeterminate={item.indeterminate}
                      onChange={e => proChangeBox(e, item, index)}
                    ></el-checkbox>
                    <div class="itemdiv" onClick={e => proChangeDiv(e, item.value, index)}>
                      {item.label}
                    </div>
                  </div>
                );
              })}
            </el-checkbox-group>
          </div>

          <div class="city_div">
            <el-checkbox-group v-model={checkedCity.value} onChange={CityChange}>
              {(cityListShow.value || []).map(item => {
                return (
                  <el-checkbox key={item.value} label={item.value}>
                    {item.label}
                  </el-checkbox>
                );
              })}
            </el-checkbox-group>
          </div>
        </div>
        <el-divider />

        <div class="footer">
          <span>
            共 {total.value || 0} 个城市，已选 {checkedCity.value.length || 0} 个
          </span>
        </div>
      </div>
    );
  },
});

export default CitySelector;
