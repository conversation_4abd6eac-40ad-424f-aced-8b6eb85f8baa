import { RouterNameSettlement } from '@/router/types';
import { RightCodeMap } from '@/const/rightCodes';


export default [
  {
    path: '/settlement',
    // component: () => import('@/modules/settlement/manage'),
    children: [
      {
        path: '/settlement/manage',
        name: RouterNameSettlement.manage,
        component: () => import('@/modules/settlement/manage'),
        meta: {
          name: '清分管理',
          isKeepLive: true,
          rights: [RightCodeMap.view_manage],
        },
      },
      {
        path: '/settlement/details',
        name: RouterNameSettlement.details,
        component: () => import('@/modules/settlement/details'),
        meta: {
          name: '清分明细',
          isKeepLive: true,
          rights: [RightCodeMap.view_detail],
        },
      },
    ],
  },
];
