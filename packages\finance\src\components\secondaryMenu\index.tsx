import { ElImage } from 'element-plus';
import { defineComponent, PropType } from 'vue';

export default defineComponent({
  props: {
    items: {
      default: () => [],
      type: Array as PropType<
        {
          linkName: string;
          img: string;
          title: string;
          desc: string;
          auth: string;
        }[]
      >,
    },
  },
  emits: ['link'],
  setup(props, ctx) {
    const onLink = item => ctx.emit('link', item);
    return () => {
      return (
        <>
          {props.items.map(item => (
            <div class="manager-item" onClick={() => onLink(item)}>
              <div class="manager-img-item-content">
                <div class="manager-img-item-icon">
                  <ElImage class="manager-img-item" src={item.img} />
                </div>
              </div>
              <div class="manager-item-content">
                <div class="manager-item-title">{item.title}</div>
                <div class="manager-item-desc">{item.desc}</div>
              </div>
            </div>
          ))}
        </>
      );
    };
  },
});
