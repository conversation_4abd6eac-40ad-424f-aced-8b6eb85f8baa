import { onMounted, onUnmounted, ref } from 'vue';
import { onBeforeRouteLeave } from 'vue-router';

export interface IJumpPrompt {
  formData: any;
  oldData: any;
}

const confirmText = '您确定要离开当前页面吗？未保存的数据将丢失！';

/**
 * 从详情页跳转时检测页面是否有变更
 * @param param0
 * @returns
 */
export const useJumpPrompt = ({ formData, oldData }: IJumpPrompt) => {
  const isSaveBtn = ref<Boolean>(false);

  const handlerBeforeUnload = event => {
    console.log('handlerBeforeUnload');
    const isChange = JSON.stringify(formData) === JSON.stringify(oldData.value);
    if (!isChange) {
      event.returnValue = confirmText;
      return confirmText;
    }
  };

  onMounted(() => {
    window.addEventListener('beforeunload', handlerBeforeUnload);
  });

  onUnmounted(() => {
    window.removeEventListener('beforeunload', handlerBeforeUnload);
  });

  onBeforeRouteLeave((toString, data, next) => {
    const isChange = JSON.stringify(formData) === JSON.stringify(oldData.value);
    if (!isChange && !isSaveBtn.value) {
      const leave = window.confirm(confirmText);
      if (!leave) {
        return next(false);
      }
    }
    next();
  });

  const setSaveBtn = (val: boolean) => {
    isSaveBtn.value = val;
  };

  return { setSaveBtn };
};
