import './index.scss';
import { defineComponent, h, nextTick, PropType, ref, VNode } from 'vue';
import { ITemplateConfig } from '../theme-mj';
import MJFormList from '../../form/theme-mj';
import { MjTableColumn } from '@common/types/vendor/column';
import { usePagination } from '@common/utils/requestHooks';
// import { FormInstance } from 'element-plus';
export type { ITemplateConfig } from '../theme-mj';

export default defineComponent({
  components: {
    MJFormList,
  },
  props: {
    /** 绑定表格的列 **/
    columns: {
      type: Array as PropType<MjTableColumn<unknown>[]>,
      default: () => [],
    },
    /** 绑定表格的数据**/
    value: {
      type: Object as any,
    },
    /** 顶部面包屑的配置 **/
    routes: {},
    service: {
      required: true,
      type: Function as unknown as PropType<ReturnType<typeof usePagination>>,
    },
    config: {
      type: Object as PropType<ITemplateConfig>,
      default: () => ({}),
    },
  },
  setup(props, ctx) {
    // const formRef = ref<FormInstance>();
    const { reset } = props.config || {};
    const reqData = props.service;
    const search = () => {
      let value = props.value;
      if (props.config.searchBefore) {
        value = props.config.searchBefore();
      }
      return reqData.pagination.reQuery(value);
    };
    const onReset = () => {
      if (reset) reset();
      nextTick(search);
    };
    const onOtherBtn = () => {
      if (props.config.onOtherBtn) {
        props.config.onOtherBtn();
        nextTick(search);
      }
    };
    if (props.config?.auto) {
      search();
    }

    const tableRef = ref<any>();

    return () => {
      let bodyContainer: VNode | VNode[] | undefined = ctx.slots.bodyContainer as any;
      if (!bodyContainer) {
        bodyContainer = (
          <div class="bodyContainer">
            {ctx.slots.btnLine && <div class="btnLine">{ctx.slots.btnLine}</div>}
            <div class="tableContainer">
              <tg-table
                ref={tableRef}
                stripe
                border={props.config?.table?.border}
                v-loading={reqData?.loading}
                columns={props.columns}
                height={'100%'}
                data={reqData?.data}
                pagination={reqData?.pagination}
                onSelection-change={props.config?.table?.selectionChange}
                show-summary={props.config?.table?.showSummary}
                summary-method={props.config?.table?.summaryMethod}
                onrow-click={props.config?.table?.rowClick}
              >
                <div class="tg-page-empty" slot="empty">
                  <empty-common detail-text={props.config?.emptyText || '暂无数据'} />
                </div>
              </tg-table>
            </div>
          </div>
        );
      }

      return (
        <article class={['tg-page-container', 'template']}>
          {props.routes && <tg-breadcrumbs routes={props.routes} />}
          {ctx.slots.searchBefore}
          {ctx.slots.default &&
            h(
              'form-list',
              {
                props: {
                  showExport: props.config.showExport,
                  search,
                  reset: onReset,
                  other: onOtherBtn,
                },
              } as any,
              [
                ...(ctx.slots.default as any),
                ((ctx.slots.searchBtn || []) as any).map((data: any) => {
                  return h(data.tag, data.data, data.children);
                }),
                <div slot="otherBtns">{ctx.slots.otherBtns}</div>,
              ],
            )}
          {ctx.slots.middle}
          {bodyContainer}
          {ctx.slots.footer}
        </article>
      );
    };
  },
});
