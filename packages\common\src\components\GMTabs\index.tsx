import { ElButton } from 'element-plus';
import { defineComponent, cloneVNode, ref } from 'vue';

export default defineComponent({
  name: 'GMTabs',
  props: {
    activePane: {
      type: String || Number,
    },
  },
  setup(props, { slots }) {
    const currentKey = ref<String | Number>(props.activePane || 1);

    const changeActive = (key: String | Number) => {
      currentKey.value = key;
    };

    return () => {
      const slotContent = slots.default?.() ?? [];
      return (
        <div>
          <div class="tabs-header-line">
            <div class="tabs-header-nav">
              {slotContent.map((item: any) => (
                <ElButton
                  style={{
                    color:
                      currentKey.value === item?.props?.key
                        ? 'rgb(68, 111, 242)'
                        : 'rgb(102,102,102)',
                  }}
                  onClick={() => changeActive(item.props?.key || 1)}
                >
                  {item?.props?.label}
                </ElButton>
              ))}
            </div>
          </div>
          <div class="tabs-content">
            {cloneVNode(
              props.activePane
                ? slotContent.find(item => item?.key === currentKey.value)
                : (slotContent[0] as any),
            )}
          </div>
        </div>
      );
    };
  },
});
