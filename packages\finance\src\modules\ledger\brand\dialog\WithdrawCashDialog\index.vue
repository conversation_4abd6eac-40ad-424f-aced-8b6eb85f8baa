<script src="./index.tsx"></script>
<style lang="scss" scoped>
.withdrawal-dialog {
  .form {
    max-width: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .form-item-width {
    width: 260px;
  }

  .balance-tips {
    width: 260px;
    display: flex;

    .balance {
      margin-right: 5px;
    }

  }

  .vcode-btn {
    color: var(--theme-color);
    cursor: pointer;

    &.disabled {
      color: var(--el-text-color-disabled);
    }
  }

  .code-tip {
    margin-bottom: -10px;
    // color: var(--theme-color);
  }

}
</style>