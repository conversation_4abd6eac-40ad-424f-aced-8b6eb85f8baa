.mj-home-container {
  padding: 26px 24px !important;
  .tips-icon__default {
    font-size: 16px;
    color: #999 !important;
  }
  .data-container {
    .line-header {
      display: flex;
      align-content: center;
      align-items: center;
      margin-bottom: 16px;
      font-weight: 500;
      font-size: 18px;
      color: #000000;

      .month.el-date-editor.el-input {
        width: 100px;
      }

      .el-date-editor.el-input {
        width: 120px;
        border: none;
        cursor: pointer;

        .el-input__wrapper {
          border: none;
          box-shadow: none;
          background: transparent;

          .el-input__inner {
            text-align: center;
            font-size: 18px;
            line-height: 28px;
            color: #000000;
            height: 28px;
            cursor: pointer;
          }

          .el-input__prefix {
            display: none;
          }
        }
      }

      .icon-histogram {
        color: var(--warning-color);
        width: 18px;
        height: 18px;
      }

      .icon-img {
        height: 28px;
        width: 28px;
      }

      > span {
        margin: 0 6px;
        line-height: 28px;
        height: 28px;
        margin-right: 24px;
      }

      .circle {
        cursor: pointer;
        //border: 2px solid var(--border-line-color);
        border-radius: 50%;
        height: 28px;
        width: 28px;
        font-size: 13px;
        margin: 0 6px;
        background: #ffffff;
      }
      .circle.disabled {
        cursor: default;
        pointer-events: none;
        color: #ccc;
      }
    }

    .order-body {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 12px;
      margin-left: -12px;
      .value {
        display: flex;
        align-items: center;
        font-weight: 700;
        margin: 0 0 6px 2px;
        font-size: 24px;
        color: var(--text-color);
        height: 28px;
      }
      .order-item {
        flex: 1;
        max-width: 340px;
        min-width: 340px;
        margin-left: 12px;
        margin-bottom: 12px;
        background: white;
        border-radius: 12px;
        padding: 12px 16px;
        display: flex;
        flex-direction: column;

        &:last-child {
          margin-right: 0;
        }

        .header-div {
          display: flex;
          align-items: center;
          font-weight: 400;
          font-size: 16px;
          color: var(--text-color);

          .icon-img {
            height: 32px;
            width: 32px;
            border-radius: 50%;
            padding: 8px;
            background: rgba(242, 179, 225, 0.3);
            margin-right: 10px;

            &.month {
              background: #00835d2b;
            }
          }
        }
        .content-div {
          display: flex;
          align-items: center;
          margin-top: 12px;
          .rol-div {
            display: flex;
            align-items: center;
            font-size: 14px;

            .rol-name {
              color: var(--text-color);
            }

            &.red {
              color: var(--error-color);
            }

            &.green {
              color: var(--success-color);
            }
          }
        }

        .footer-div {
          .prev-current,
          .prev-full {
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 14px;
            color: var(--text-color);
            height: 28px;
            line-height: 28px;
          }
          .value {
            font-weight: 700;
            font-size: 16px;
            margin: 0 12px 0 0;
          }
        }
      }
    }
  }

  .other-data {
    .order-body {
      .order-item {
        flex-direction: row;

        .order-item-left {
          width: 93px;
          height: 93px;
          background: rgba(242, 179, 225, 0.3);
          border-radius: 10px;
          margin-right: 20px;
          padding: 15px;
        }

        .order-item-right {
          flex: 1;

          .name {
            margin-top: 8px;
            font-weight: 400;
            font-size: 18px;
            color: var(--text-color);
            letter-spacing: 0;
            line-height: 26px;
            margin-bottom: 12px;
          }

          .dec {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .value {
              margin: 0;
            }

            .look-div {
              display: flex;
              color: #ffffff;
              width: 76px;
              height: 24px;
              background: #000000;
              border-radius: 12px;
              font-weight: 400;
              font-size: 13px;
              align-items: center;
              justify-content: center;
              line-height: 24px;
              cursor: pointer;

              .arrow-icon {
                color: #ffffff;
                width: 14px;
                height: 14px;
              }
            }
          }
        }
      }
    }

    .value {
      text-align: center;
    }
  }
}
