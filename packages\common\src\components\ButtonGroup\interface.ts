type ButtonType = 'primary' | 'default' | 'link';

export interface IBtnItemProps {
  type?: ButtonType;
  isAuth?: boolean;
  key: string;
  service?: any;
  custom?: {
    label?: string;
    icon?: string;
    type?: ButtonType;
  };
}

export interface IButtonGroupProps {
  btnList: IBtnItemProps[];
}

export interface IButtonGroupEmits {
  onButtonGroupClick?: (key: string) => void;
}

export interface IButtonKeyGroupItem {
  label: string;
  key: string;
  icon: string;
}

export type IButtonKeyGroup = {
  [key: string]: IButtonKeyGroupItem;
};
