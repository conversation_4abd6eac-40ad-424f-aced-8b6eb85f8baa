import { defineComponent, ref, computed, Transition, onMounted, onBeforeUnmount } from 'vue';
// import FlvVideo from '@/modules/datacenter/ctr/components/flvVideo/index.vue';
import { useScale } from './scale';
import {
  ArrowLeftBold,
  ArrowRightBold,
  CloseBold,
  FullScreen,
  RefreshLeft,
  RefreshRight,
  ZoomIn,
  ZoomOut,
} from '@element-plus/icons-vue';
import { ElIcon, ElImage } from 'element-plus';

export default defineComponent({
  // components: {
  //   FlvVideo,
  // },
  props: {
    /** 图片地址列表 */
    'url-list': Array,
  },
  setup(_, ctx) {
    const imgRef = ref<HTMLElement>();
    const scrollFuncRef = ref<HTMLElement>();
    const { dragImg, scrollFunc, resetScale, scaleUp, scaleDown, rotateLeft, rotateRight } =
      useScale(imgRef);
    const list = ref<any[]>([]);
    const index = ref(0);
    /** 展示图片 */
    const show = (urls: string[], start: number) => {
      list.value = urls.map(url => {
        const hasPDF = /\.pdf\?.+$|\.pdf\??$/.test(url);
        const hasVideo = /\.mp4\?.+$|\.mp4\??$/.test(url);
        return {
          hasPDF,
          hasVideo,
          url,
        };
      });
      index.value = start;
    };
    /** 当前图片地址 */
    const current = computed(() => {
      if (list.value.length === 0) return {};
      return list.value[index.value];
    });
    /** 上一张按钮事件处理 */
    const prevHandler = () => {
      if (index.value === 0) {
        index.value = list.value.length - 1;
      } else {
        index.value--;
      }
    };
    /** 下一张按钮事件处理 */
    const nextHandler = () => {
      if (index.value === list.value.length - 1) {
        index.value = 0;
      } else {
        index.value++;
      }
    };
    const close = () => {
      ctx.emit('close');
    };
    const stopEvent = (e: MouseEvent) => {
      e.stopPropagation();
      e.preventDefault();
    };

    onMounted(() => {
      scrollFuncRef.value?.addEventListener('wheel', scrollFunc, { passive: true });
    });

    onBeforeUnmount(() => {
      scrollFuncRef.value?.removeEventListener('wheel', scrollFunc);
    });

    return {
      show,
      list,
      current,
      index,
      nextHandler,
      close,
      stopEvent,
      prevHandler,
      imgRef,
      dragImg,
      scrollFunc,
      scaleDown,
      scaleUp,
      resetScale,
      rotateLeft,
      rotateRight,
      scrollFuncRef,
    };
  },
  render() {
    let currentShow: any;
    // console.log('----this.current.url', this.current.url);

    if (this.current.hasPDF) {
      const _url = this.list[this.index].url;
      const _name = _url.split('?')[0].split('/');
      currentShow = (
        <div class="pdf-preview" style="width:150px;height:150px;text-align:center">
          <tg-icon
            name="ico-pdf"
            style="position:relative;z-index:100;cursor:pointer"
            onClick={() => {
              window.open(_url);
            }}
          />
          <span style="color:white">{_name[_name.length - 1]}</span>
        </div>
      );
    } else if (this.current.hasVideo) {
      console.log('this.', this.current.url);
      currentShow = (
        <flv-video
          src={this.current.url}
          autoplay={true}
          type="mp4"
          isLive={false}
          playMode={'click'}
        />
      );
    } else {
      // console.log('加载图片', this.current.url);
      currentShow = (
        <ElImage
          ref="imgRef"
          src={this.current.url}
          onClick={this.stopEvent}
          class="el-image-viewer__img"
          type="canvas"
          onerror={e => {
            console.error('加载失败', e);
          }}
        />
      );
    }
    return (
      <Transition>
        <div
          tabindex="-1"
          class="el-image-viewer__wrapper"
          style="z-index: 20000;"
          ref="wrapperRef"
        >
          <span
            class="el-image-viewer__btn el-image-viewer__close"
            style="z-index: 20000;"
            onClick={this.close}
          >
            <ElIcon>
              <CloseBold />
            </ElIcon>
          </span>
          {this.list.length > 1 && (
            <div>
              <span
                style="z-index:102;"
                class="el-image-viewer__btn el-image-viewer__prev"
                onclick={this.prevHandler}
              >
                <ElIcon>
                  <ArrowLeftBold />
                </ElIcon>
              </span>
              <span
                style="z-index:102;"
                class="el-image-viewer__btn el-image-viewer__next"
                onclick={this.nextHandler}
              >
                <ElIcon>
                  <ArrowRightBold />
                </ElIcon>
              </span>
            </div>
          )}
          <div
            class="el-image-viewer__canvas"
            style="position:relative;z-index:100;"
            // onClick={this.close}
            ref="scrollFuncRef"
            onMousedown={this.dragImg}
            // onWheel={this.scrollFunc}
          >
            {currentShow}
            {/* {this.list.length > 1 && ( */}
            <div class="ml-pagination">
              <div class="el-pagination-item-list">
                {/* <tg-icon
                  name="ico-common-suoxiao"
                  onClick={this.scaleDown}
                ></tg-icon> */}
                <ElIcon onClick={this.scaleDown}>
                  <ZoomOut />
                </ElIcon>
                {/* <tg-icon
                  name="ico-common-fangda"
                  onclick={this.scaleUp}
                ></tg-icon> */}
                <ElIcon onclick={this.scaleUp}>
                  <ZoomIn />
                </ElIcon>
                {/* <i
                  class="el-icon-full-screen"
                  onclick={this.resetScale}
                  style={this.list.length <= 1 && "margin-right: 0"}
                ></i> */}
                <ElIcon onclick={this.resetScale}>
                  <FullScreen />
                </ElIcon>
                <ElIcon onclick={this.rotateLeft}>
                  <RefreshLeft />
                </ElIcon>
                <ElIcon onclick={this.rotateRight}>
                  <RefreshRight />
                </ElIcon>
                {this.list.length > 1 && (
                  <span class="mgl-12">
                    {this.index + 1}/{this.list.length}
                  </span>
                )}
              </div>
            </div>
            {/* )} */}
          </div>
          <div class="el-image-viewer__mask" />
        </div>
      </Transition>
    );
  },
});
