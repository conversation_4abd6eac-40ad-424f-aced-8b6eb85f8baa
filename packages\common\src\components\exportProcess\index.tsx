import { MjTableColumn } from '@common/types/vendor/column';
import { usePagination } from '@common/utils/requestHooks';
import { defineComponent, ref, toRefs } from 'vue';
import MjTable from '@common/components/table';
import { downloadFileFromLink } from '@common/utils/func';
import { ExportTaskQueueStatus } from '@common/enums';

export default defineComponent({
  setup(_props, ctx) {
    const reqList = ref();
    const columns = ref<MjTableColumn<any>[]>([
      {
        label: '文件名称',
        minWidth: 180,
        prop: 'export_file_name',
        showOverflowTooltip: true,
      },
      {
        label: '提交时间',
        minWidth: 220,
        prop: 'gmt_create',
        align: 'center',
      },
      {
        label: '导出状态',
        width: 100,
        prop: 'status',
        align: 'center',
        dataType: {
          type: 'enum',
          enum: ExportTaskQueueStatus.toMjMaps(),
        },
      },
      {
        label: '操作人',
        width: 100,
        align: 'center',
        prop: 'add_by_name',
      },
      {
        label: '操作',
        width: 100,
        align: 'center',
        formatter: row => {
          if (row.status === ExportTaskQueueStatus.end.value) {
            return (
              <el-button
                link
                type="primary"
                onClick={() => {
                  downloadFileFromLink(row.export_file_url);
                }}
              >
                下载
              </el-button>
            );
          }
        },
      },
    ]);
    const show = (service: any) => {
      if (service) reqList.value = toRefs(usePagination(service));
    };
    ctx.expose({
      show,
    });
    return () => {
      return (
        <div class="mj-export-process-page-container">
          <MjTable
            border
            height="100%"
            data={reqList.value?.data}
            columns={columns.value}
            pagination={reqList.value?.pagination}
          ></MjTable>
        </div>
      );
    };
  },
});
