<script src="./dialogStore.tsx"></script>
<style lang="scss" scoped>
  .dialog-store {
    display: grid;
    grid-template-columns: 1fr 300px;
    grid-column-gap: 20px;
    height: 600px;
    overflow: hidden;
    .area {
      border-radius: 6px;
      :deep(.el-form) {
        display: grid;
        grid-template-columns: repeat(5, minmax(100px, min-content)) 1fr;
        grid-column-gap: 6px;
        padding-bottom: 12px;
      }
      .table-box {
        overflow: hidden;
      }
    }
    .operating {
      justify-self: flex-end;
      width: 200px;
    }
    .area-data {
      display: grid;
      grid-template-rows: auto 1fr;
      overflow: hidden;
    }
    .selected-box {
      border-radius: 6px;
      border: solid 1px #d7dbe7;
      display: flex;
      flex-direction: column;
      padding: 10px;
      overflow: hidden;
      .header {
        display: flex;
        justify-content: space-between;
        a {
          cursor: pointer;
        }
      }
      .selected-body {
        display: flex;
        align-content: flex-start;
        padding-top: 10px;
        overflow: auto;
        flex-wrap: wrap;
        .selected-item {
          display: flex;
          align-items: center;
          white-space: nowrap;
          background-color: #446ff21a;
          color: #1a1a1a;
          padding: 4px 10px;
          font-size: 12px;
          border-radius: 2px;
          margin: 0 10px 10px 0;
          &:hover {
            color: #446ff2;
            cursor: pointer;
          }
        }
      }
    }
  }
</style>
