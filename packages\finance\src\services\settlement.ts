import { Delete, Get, ObjectFilterEmpty, Patch, Post } from 'common';
import { HttpResponse, IGPageQuery, ListResponse } from 'common/src/types/base/http';

/**
 * 查询清分列表
 */

export const query_clear_division_list = async (
  pager: IGPageQuery,
  payload: any,
): Promise<ListResponse<any>> =>
  Get('/api/clearing/settlement/page', {
    params: {
      ...ObjectFilterEmpty({
        ...pager,
        ...(payload || {}),
      }),
    },
  });

/**
 * 查询清分列表
 */

export const query_clear_detail_list = async (
  pager: IGPageQuery,
  payload: any,
): Promise<ListResponse<any>> =>
  Get('/api/clearing/settlement/detail/page', {
    params: {
      ...ObjectFilterEmpty({
        ...pager,
        ...(payload || {}),
      }),
    },
  });

/**
 * 清分管理复核
 */
export const review = async (id: number, payload: any): Promise<HttpResponse<undefined>> =>
  Post(`/api/clearing/settlement/${id}`, {
    ...ObjectFilterEmpty(payload),
  });

/**
 * 清分明细重试
 */
export const retry = async (payload: any): Promise<HttpResponse<undefined>> =>
  Post(`/api/clearing/settlement/detail/retry`, {
    ...ObjectFilterEmpty(payload),
  });

/**
 * 查询清分结算导入列表
 */

export const query_import_list = async (
  pager: IGPageQuery,
  payload: any,
): Promise<ListResponse<any>> =>
  Get('/api/clearing/settlement/import/page', {
    params: {
      ...ObjectFilterEmpty({
        ...pager,
        ...(payload || {}),
      }),
    },
  });

/**  清分管理导入 */
export const ImportOrderRecordFile = (payload: {
  import_file_url: string;
}): Promise<HttpResponse<any>> =>
  Post('/api/clearing/settlement/import', {
    ...ObjectFilterEmpty(payload),
  });
