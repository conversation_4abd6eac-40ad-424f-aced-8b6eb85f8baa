import { IButtonKeyGroup } from './interface';

export const ButtonKeys = {
  ADD: 'ADD', // 新增
  DELETE: 'DELETE', // 删除
  EDIT: 'EDIT', // 修改
  IMPORT: 'IMPORT', // 导入
  IMPORT_PROGRESS: 'IMPORT_PROGRESS', // 导入进度
  EXPORT: 'EXPORT', // 导出
  EXPORT_PROGRESS: 'EXPORT_PROGRESS', // 导出进度
  SEARCH: 'SEARCH', // 查询
  RESET: 'RESET', // 重置
  EXPAND: 'EXPAND', // 展开
  RETRACT: 'RETRACT', // 收起
  PREVIEW: 'PREVIEW', // 预览
  UP: 'UP', // 启用
  DOWN: 'DOWN', // 停用
  SHELVES: 'SHELVES', // 上架
  TAKEN: 'TAKEN', // 下架,
  BATCH_UP_DOWN: 'BATCH_UP_DOWN', // 批量上下架
  BATCH_UP_DOWN_city: 'BATCH_UP_DOWN_city', // 批量上下架
  PRODUCT_SORT: 'PRODUCT_SORT', // 产品排序
  SHARE_SETTING: 'SHARE_SETTING', // 分享设置
  PROMOTION: 'PROMOTION', // 推广
};

export const ButtonKeyGroup: IButtonKeyGroup = {
  [ButtonKeys.ADD]: {
    label: '新增',
    key: ButtonKeys.ADD,
    icon: 'plus',
  },
  [ButtonKeys.DELETE]: {
    label: '删除',
    key: ButtonKeys.DELETE,
    icon: '',
  },
  [ButtonKeys.EDIT]: {
    label: '编辑',
    key: ButtonKeys.DELETE,
    icon: '',
  },
  [ButtonKeys.IMPORT]: {
    label: '导入',
    key: ButtonKeys.IMPORT,
    icon: 'plus',
  },
  [ButtonKeys.IMPORT_PROGRESS]: {
    label: '导入进度',
    key: ButtonKeys.IMPORT_PROGRESS,
    icon: '',
  },
  [ButtonKeys.EXPORT]: {
    label: '导出',
    key: ButtonKeys.EXPORT,
    icon: 'download',
  },
  [ButtonKeys.EXPORT_PROGRESS]: {
    label: '导出进度',
    key: ButtonKeys.EXPORT_PROGRESS,
    icon: '',
  },
  [ButtonKeys.SEARCH]: {
    label: '查询',
    key: ButtonKeys.SEARCH,
    icon: '',
  },
  [ButtonKeys.RESET]: {
    label: '重置',
    key: ButtonKeys.RESET,
    icon: '',
  },
  [ButtonKeys.EXPAND]: {
    label: '展开',
    key: ButtonKeys.EXPAND,
    icon: '',
  },
  [ButtonKeys.RETRACT]: {
    label: '收起',
    key: ButtonKeys.RETRACT,
    icon: '',
  },
  [ButtonKeys.PREVIEW]: {
    label: '预览',
    key: ButtonKeys.PREVIEW,
    icon: '',
  },
  [ButtonKeys.UP]: {
    label: '启用',
    key: ButtonKeys.UP,
    icon: '',
  },
  [ButtonKeys.DOWN]: {
    label: '停用',
    key: ButtonKeys.DOWN,
    icon: '',
  },
  [ButtonKeys.SHELVES]: {
    label: '上架',
    key: ButtonKeys.SHELVES,
    icon: '',
  },
  [ButtonKeys.TAKEN]: {
    label: '下架',
    key: ButtonKeys.TAKEN,
    icon: '',
  },
  [ButtonKeys.BATCH_UP_DOWN]: {
    label: '批量启/停用',
    key: ButtonKeys.BATCH_UP_DOWN,
    icon: '',
  },
  [ButtonKeys.BATCH_UP_DOWN_city]: {
    label: '批量上下架',
    key: ButtonKeys.BATCH_UP_DOWN_city,
    icon: '',
  },
  [ButtonKeys.PRODUCT_SORT]: {
    label: '服务商品排序',
    key: ButtonKeys.PRODUCT_SORT,
    icon: '',
  },
  [ButtonKeys.SHARE_SETTING]: {
    label: '分享设置',
    key: ButtonKeys.SHARE_SETTING,
    icon: '',
  },
  [ButtonKeys.PROMOTION]: {
    label: '推广',
    key: ButtonKeys.PROMOTION,
    icon: '',
  },
};
