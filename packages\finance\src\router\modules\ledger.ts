import { RouterNameLedger } from '@/router/types';
import { RightCodeMap } from '@/const/rightCodes';

export default [
  {
    path: '/ledger',
    // component: () => import('@/modules/settlement/manage'),
    children: [
      {
        path: '/ledger/brand',
        name: RouterNameLedger.brand,
        component: () => import('@/modules/ledger/brand'),
        meta: {
          name: '品牌台账',
          isKeepLive: true,
          rights: [RightCodeMap.view_ledger],
        },
      },
    ],
  },
];
