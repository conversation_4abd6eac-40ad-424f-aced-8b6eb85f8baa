import { defineComponent, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { downloadFileFromLink } from '@common/utils/func';
import { WarningFilled } from '@element-plus/icons-vue';

export default defineComponent({
  name: 'MjImportFile',
  setup(_, ctx) {
    const file_path = ref<string[]>([]);
    const error_msg = ref('');
    const import_name = ref('导入文件：');
    const down_url = ref('');
    const down_url_str = ref('下载模板');
    const file_extensions = ref(['.xls', '.xlsx']);
    const tipsMessage = ref<string>(''); // 顶部提示信息
    const successMessage = ref<string>(''); // 自定义成功提示
    let onSubmit = (str: string) => {
      console.log('-----', str);
    };
    const show = (
      submit_fun: (file_path: string) => { success: boolean; message: string; data: any },
      params: {
        down_url?: string;
        down_url_str?: string;
        extensions?: string[];
        import_name_str?: string;
        is_no_return_data?: boolean; //是否有还回数据
        tipsMessage?: string;
        successMessage?: string;
      },
    ) => {
      file_extensions.value = params.extensions || ['.xls', '.xlsx'];
      down_url.value = params.down_url || '';
      down_url_str.value = params.down_url_str || '下载模板';
      import_name.value = params.import_name_str || import_name.value;
      tipsMessage.value = params.tipsMessage || '';
      successMessage.value = params.successMessage || '';
      onSubmit = async (str: string) => {
        const response = await submit_fun(str);
        ctx.emit('updateDialog', { loading: false });
        if (
          response.success &&
          ((response.data?.data || []).length > 0 || params.is_no_return_data)
        ) {
          error_msg.value = '';
          ElMessage.success(successMessage.value || response.message || '导入成功');
          ctx.emit('submit', response.data?.data || []);
          ctx.emit('close');
        } else {
          error_msg.value =
            (response.data?.errors || []).length > 0
              ? (response.data?.errors || []).join(', ')
              : response.message || '导入失败！';
        }
      };
    };
    const onSaveBtnClick = async () => {
      if ((file_path.value || []).length < 1) {
        ElMessage.warning('请选择文件！');
        return;
      }
      ctx.emit('updateDialog', { loading: true });
      onSubmit(file_path.value[0]);
    };

    // 核心
    ctx.expose({ show, onSaveBtnClick });
    const downloadClick = () => {
      if (down_url.value) downloadFileFromLink(down_url.value, true);
    };
    return () => (
      <div>
        <div class="mj-import-approval">
          {tipsMessage.value && (
            <el-alert
              title={tipsMessage.value}
              type="warning"
              style="margin-bottom: 12px"
              closable={false}
            />
          )}
          <div class="filter-section">
            <el-form
              style="max-width: 600px"
              status-icon={false}
              label-width="auto"
              class="demo-formData"
            >
              <el-form-item label="导入文件：">
                <mj-upload
                  show-file-list={false}
                  action="/api/resources/upload_file"
                  data={{ type: 'any' }}
                  success={(res: any) => {
                    if (res.success !== true) {
                      ElMessage.error(res.message ?? '上传失败');
                    } else if (res.data?.source) {
                      file_path.value = [res.data?.source];
                    }
                  }}
                >
                  <el-button>上传文件</el-button>
                </mj-upload>
                {/*<span style="margin-left:12px;color:var(--text-second-color)">
                  支持png、jpg、xls、xlsx、docx、pdf
                </span>*/}
              </el-form-item>
              {(file_path.value || []).length > 0 && (
                <mj-upload-file-list v-model={file_path.value}></mj-upload-file-list>
              )}
              {down_url.value && (
                <el-form-item label="导入模板：">
                  <el-button link type="primary" onClick={downloadClick}>
                    {down_url_str.value}
                  </el-button>
                </el-form-item>
              )}
            </el-form>
            {error_msg.value && (
              <div class="error-div">
                <div class="tips-div">
                  <el-icon class="error-icon">
                    <WarningFilled />
                  </el-icon>
                  出错了
                </div>
                <div class="msg-div">{error_msg.value}</div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  },
});
