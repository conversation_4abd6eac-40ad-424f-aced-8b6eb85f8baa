import { defineComponent, Fragment, onMounted, ref } from 'vue';
import { MJFormList } from '@common/components';
import { Select } from '@common/components/select/Select';
import { incomeExpenditureType, tradingAccount, transactionType } from '@/enums/ledger';
import { MjTableColumn } from '@common/types/vendor/column';
import { QuestionFilled } from '@element-plus/icons-vue';
import { FD, usePaginationV2, useRequest, useDialog } from 'common';
import { brand_ledger_balance_query, query_brand_ledger_list, account } from '@/services/ledger';
import Decimal from 'decimal.js';
import open from './dialog/open.vue';
import WithdrawCashDialog from './dialog/WithdrawCashDialog';

export default defineComponent({
  setup() {
    const initFormData = () => {
      return {
        account_type: '',
        return_serial_number: '',
        operator_type: '',
        loan_type: '',
        status: '',
        tradingTime: [],
      };
    };
    const is_bank_info = ref(false); //是否绑卡
    const account_info = ref(); //开户信息 是否开户
    const formData = ref(initFormData());

    const columns = ref<MjTableColumn<any>[]>([
      {
        label: '流水号',
        minWidth: 180,
        align: 'center',
        prop: 'return_serial_number',
        showOverflowTooltip: true,
      },
      {
        label: '入账金额',
        minWidth: 180,
        align: 'center',
        prop: 'entry_amount__yuan',
        showOverflowTooltip: true,
        formatter: row => {
          // console.log(transactionType.entry);
          return row.operator_type === transactionType.entry.value
            ? FD.addThousandsSep(row.entry_amount__yuan)
            : '';
        },
      },
      {
        label: '出账金额',
        minWidth: 180,
        align: 'center',
        prop: 'exit_amount__yuan',
        showOverflowTooltip: true,
        formatter: row => {
          // console.log(row);
          return row.operator_type === transactionType.outAccount.value
            ? '-' + FD.addThousandsSep(row.exit_amount__yuan)
            : '';
        },
      },
      {
        label: '交易类型',
        minWidth: 180,
        align: 'center',
        prop: 'operator_type',
        showOverflowTooltip: true,
        dataType: {
          type: 'enum',
          enum: transactionType.toMjMaps(),
        },
      },
      {
        label: '收支类型',
        minWidth: 180,
        align: 'center',
        prop: 'operator_type',
        showOverflowTooltip: true,
        dataType: {
          type: 'enum',
          enum: incomeExpenditureType.toMjMaps(),
        },
      },
      {
        label: '交易账户',
        minWidth: 180,
        align: 'center',
        prop: 'account_name',
        showOverflowTooltip: true,
      },
      {
        label: '对方账户',
        minWidth: 180,
        align: 'center',
        prop: 'opposite_account_name',
        showOverflowTooltip: true,
      },
      // {
      //   label: '交易备注',
      //   minWidth: 180,
      //   align: 'center',
      //   prop: 'main_order_number',
      //   showOverflowTooltip: true,
      // },
      {
        label: '交易时间',
        minWidth: 180,
        align: 'center',
        prop: 'transaction_datetime',
        showOverflowTooltip: true,
      },
      // {
      //   label: '交易状态',
      //   minWidth: 180,
      //   align: 'center',
      //   prop: 'main_order_number',
      //   showOverflowTooltip: true,
      // },
    ]);

    const accountData = ref([
      {
        title: '资金汇总账户',
        tips: '所有子账户的资金总和，包含品牌、代理商、门店、美甲师、功能型子账户（营销/挂账/利息/…）等所有子账户的余额。',
        price: 0,
        ...FD.splitDecimal(0),
      },
      {
        title: '待清分账户',
        tips: '抖音、美团、小程序提现的资金先归集至该账户，用于月度出账后给品牌、代理商、门店、美甲师分账。',
        price: 0,

        ...FD.splitDecimal(0),
      },
      {
        title: 'PJ佣金账户',
        tips: '每月品牌抽佣扣除返佣后的资金归集于该账户。',
        price: 0,
        ...FD.splitDecimal(0),
      },
    ]);
    const accountList = ref([]);
    const defaultAccount = ref('');
    const statistics = ref();
    const summaryPropList = ['entry_amount__yuan', 'exit_amount__yuan'];
    const getSummaries = ({ columns }: any) => {
      return columns.map((column, colIdx) => {
        const key = column.property;
        if (colIdx === 0) return '合计';

        if (summaryPropList.includes(key)) {
          let total = FD.addThousandsSep(statistics.value?.[key]);
          if (key === 'exit_amount__yuan' && Number(statistics.value?.[key]) > 0) {
            total = '-' + total;
          }
          return total;
        }

        return '';
      });
    };

    const reqList = usePaginationV2(query_brand_ledger_list, {
      manual: true,
      onSuccess(_, oData) {
        statistics.value = oData.data.statistics || {};
      },
    });

    const balanceQueryReq = useRequest(brand_ledger_balance_query, {
      manual: true,
      onSuccess(_, oData) {
        const {
          balance_amount__yuan,
          pending_clearing_balance_amount__yuan,
          commission_balance_amount__yuan,
        } = oData.data as any;
        const accountConfigs = [
          {
            title: '资金汇总账户',
            tips: '所有子账户的资金总和，包含品牌、代理商、门店、美甲师、功能型子账户（营销/挂账/利息/…）等所有子账户的余额。',
            amount: balance_amount__yuan,
            intAmount: balance_amount__yuan,
          },
          {
            title: '待清分账户',
            tips: '抖音、美团、小程序提现的资金先归集至该账户，用于月度出账后给品牌、代理商、门店、美甲师分账。',
            amount: pending_clearing_balance_amount__yuan,
            intAmount: pending_clearing_balance_amount__yuan,
          },
          {
            title: 'PJ佣金账户',
            tips: '每月品牌抽佣扣除返佣后的资金归集于该账户。',
            amount: commission_balance_amount__yuan,
            intAmount: commission_balance_amount__yuan,
          },
        ];

        accountData.value = accountConfigs.map(config => ({
          title: config.title,
          tips: config.tips,
          price: config.amount,
          ...FD.splitDecimal(config.amount || 0),
          intAmount: config.intAmount,
        }));
      },
    });

    const getParams = () => {
      const params = { ...formData.value };
      const start_transaction_datetime = params.tradingTime?.[0]
        ? params.tradingTime[0]
        : undefined;
      const end_transaction_datetime = params.tradingTime?.[1] ? params.tradingTime[1] : undefined;
      return {
        ...params,
        start_transaction_datetime,
        end_transaction_datetime,
      };
    };

    const onQuery = async () => {
      reqList.runAsync(getParams());
      await balanceQueryReq.runAsync();
    };

    const reset = () => {
      formData.value = initFormData();
      onQuery();
    };

    const openAccount = async () => {
      openDialog.show({
        is_account_info: is_bank_info.value,
        account_info: account_info.value,
        tabIndex: account_info.value ? 2 : 1,
      });
    };

    const openDialog = useDialog({
      component: open,
      title: 'PJ佣金账户开通',
      width: 450,
      footer: false,
      onClose: () => {
        getAccount();
      },
      on: {
        submit() {
          onQuery();
        },
        close() {
          getAccount();
        },
        changeTab(e) {
          switch (e) {
            case 1:
              openDialog.update({ title: 'PJ佣金账户开通' });
              break;
            case 2:
              openDialog.update({ title: '绑定银行账户' });
              break;
            case 3:
              openDialog.update({ title: '账户验证' });
              break;
            case 4:
              openDialog.update({ title: 'PJ佣金账户开通' });
              break;
          }
        },
      },
    });

    //获取开户信息
    const getAccount = async () => {
      const { data } = await useRequest(account, { manual: true }).runAsync();
      if (Object.keys(data?.data?.account_info).length) {
        account_info.value = data?.data?.account_info;
      }
      if (data?.data?.bank_info.length) {
        is_bank_info.value = true;
      }
      if (data.bank_info && data.bank_info.length) {
        accountList.value = data.bank_info.map(item => ({
          label: `${item.bank_name}(${item.bank_card_no.slice(-4)})`,
          value: item.bank_card_no,
        }));
        defaultAccount.value = data.bank_info.find(item => item.is_default);
        // formData.value.bank_card_no = defaultAccount?.bank_card_no || '';
      }
    };

    // 提现弹窗
    const withdrawCashDialog = useDialog({
      component: WithdrawCashDialog,
      title: '提现',
      width: 400,
      okText: '确定',
      on: {
        submit() {
          onQuery();
          console.log('submit');
        },
      },
    });

    onMounted(async () => {
      getAccount();
      await onQuery();
    });
    return () => (
      <div class="page-container">
        <div class="data-section">
          {accountData.value.map((item, index) => {
            return (
              <div class="data-item" key={index}>
                <div class="title">
                  <div class="text">{item.title}</div>
                  <el-popover
                    popper-style={{
                      width: 'auto',
                      whiteSpace: 'pre-wrap',
                    }}
                    effect="dark"
                    placement="top"
                    trigger="hover"
                    content={item.tips}
                  >
                    {{
                      reference: () => (
                        <el-icon color="#AFB0BD">
                          <QuestionFilled />
                        </el-icon>
                      ),
                    }}
                  </el-popover>
                </div>
                {item.title.includes('佣金账户') ? (
                  <>
                    {account_info.value && is_bank_info.value ? (
                      <>
                        <div class="amount D-DIN">
                          <div class="symbol">¥</div>
                          <div class="integer">{item.integer}</div>
                          <div class="decimal">{item.decimal}</div>
                          <a
                            class="oper_btn"
                            onClick={() => {
                              withdrawCashDialog.show(item);
                            }}
                          >
                            提现
                          </a>
                        </div>
                      </>
                    ) : (
                      <div class="amount" onClick={() => openAccount()}>
                        <a class="integer opcity">0</a>
                        <a class="oper_btn">
                          {!is_bank_info.value && account_info.value ? '绑卡' : '去开户'}
                        </a>
                      </div>
                    )}
                  </>
                ) : (
                  <div class="amount D-DIN">
                    <div class="symbol">¥</div>
                    <div class="integer">{item.integer}</div>
                    <div class="decimal">{item.decimal}</div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
        <MJFormList onSearch={onQuery} onReset={reset}>
          {{
            default: () => [
              <el-form-item style="width: 120px">
                <Select
                  v-model={formData.value.account_type}
                  placeholder="交易账户"
                  options={tradingAccount.toMjOptions()}
                ></Select>
              </el-form-item>,
              <el-form-item style="width: 120px">
                <el-input
                  clearable
                  placeholder="流水号"
                  vModel_trim={formData.value.return_serial_number}
                />
              </el-form-item>,
              <el-form-item style="width: 120px">
                <Select
                  v-model={formData.value.loan_type}
                  placeholder="交易类型"
                  options={transactionType.toMjOptions()}
                ></Select>
              </el-form-item>,
              <el-form-item style="width: 120px">
                <Select
                  v-model={formData.value.operator_type}
                  placeholder="收支类型"
                  options={incomeExpenditureType.toMjOptions()}
                ></Select>
              </el-form-item>,
              <el-form-item style="width: 252px">
                <el-date-picker
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  v-model={formData.value.tradingTime}
                  type="daterange"
                  range-separator="~"
                  start-placeholder="交易时间"
                  end-placeholder="交易时间"
                />
              </el-form-item>,

              // <el-form-item style="width: 120px">
              //   <Select
              //     v-model={formData.value.status}
              //     placeholder="交易状态"
              //     options={transactionType.toMjOptions()}
              //   ></Select>
              // </el-form-item>,
            ],
          }}
        </MJFormList>
        <section class="table-section">
          <mj-table
            v-loading={reqList.loading}
            height="100%"
            data={reqList.data}
            columns={columns.value}
            show-summary
            summary-method={getSummaries}
            pagination={{
              ...reqList.pagination,
            }}
          ></mj-table>
        </section>
      </div>
    );
  },
});
