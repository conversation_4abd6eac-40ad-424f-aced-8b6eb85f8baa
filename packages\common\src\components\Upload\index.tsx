import { defineComponent, PropType, ref } from 'vue';
// import { getCode, getToken } from '@/utils/token';
import { ElMessage, ElMessage as Message, ElUpload } from 'element-plus';
import { getUploadHeader } from '../../config';
// import { ElUpload } from 'element-ui/types/upload';
// let headerConfig: () => Object;
/**
 * 天鸽上传组件, 基本只需要传递,成功回调和上传前校验就行
 */
export default defineComponent({
  name: 'MjUpload',
  props: {
    showFileList: {},
    data: {},
    action: {},
    /** 成功时回调 **/
    success: {
      type: Function as PropType<(...args: any) => void>,
    },
    error: {
      type: Function as PropType<() => void>,
    } /** 是否隐藏上传时loading **/,
    hideLoading: {
      type: Boolean,
    } /** 上传前校验 传递file对象 **/,
    beforeUpload: {},
    showList: {},
    modelValue: {},
    /** 是否多选 **/
    multiple: {
      type: Boolean,
      default: false,
    },
    /** 上传文件数量 **/
    limit: {
      type: Number,
      default: 99,
    },
    isClearFile: {
      type: Boolean,
      default: true,
    },
  },
  components: {
    ElUpload,
  },
  setup() {
    const loading = ref<boolean>(false);
    const timer = ref<any>(null);
    const uploader = ref();
    return {
      timer,
      uploader,
      loading,
    };
  },
  render() {
    const $props = this.$props as any;
    const { success, ...restProps } = $props;
    const { class: className, ...resetAttr } = this.$attrs;
    const options = {
      // attrs: {
      headers: getUploadHeader(),
      'on-success': async (...args: any) => {
        if (!this.hideLoading) {
          this.loading = false;
        }
        if (this.timer !== null) {
          clearTimeout(this.timer);
          this.timer = null;
        }
        this.timer = setTimeout(() => {
          if (this.isClearFile) {
            this.uploader?.clearFiles(); //上传成功之后清除历史记录
          }
        }, 1000);
        const [res] = args;
        if (res.success !== true) {
          ElMessage.error(res.message ?? '上传失败');
        } else {
          if (this.multiple) {
            this.$emit('update:modelValue', [...(this.modelValue || []), res.data.source]);
          } else {
            this.$emit('update:modelValue', res.data.source);
          }
        }
        success && success(...args);
      },
      'on-progress': async (event: any, file: any, fileList: any) => {
        if (!this.hideLoading) {
          this.loading = true;
        }
      },
      'on-exceed': () => {
        Message.error('超出最大数量限制');
      },
      'on-error': () => {
        const error: any = this.error;
        this.loading = false;
        Message.error('上传失败');
        error && error();
      },
      ...resetAttr, // },
      ...restProps,
      'before-upload': this.beforeUpload,
    };
    let result = (
      <div class="flex">
        <el-upload class="dialog-upload" ref="uploader" {...options} v-loading={this.loading}>
          {this.$slots.default?.() ?? <el-button>上传文件</el-button>}
          {this.$slots.tips?.()}
        </el-upload>
        {this.$slots.sideContent?.()}
      </div>
    );
    if (this.showList && this.modelValue) {
      result = [
        result,
        <mj-upload-file-list
          class="mgt-6"
          modelValue={this.modelValue}
          onUpdate_ModelValue={$event => this.$emit('update:modelValue', $event)}
          onDelete={() => this.$emit('update:modelValue', null)}
        />,
      ];
    }
    return result;
  },
});

// export function SetupUploadHeaderConfig(config: () => Object) {
//   headerConfig = config;
// }
