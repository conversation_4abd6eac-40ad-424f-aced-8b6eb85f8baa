import { PropType, ref, nextTick, defineComponent } from 'vue';
import './index.less';
import { VNode } from 'vue';
import { usePagination, useRequest } from '../../../utils/requestHooks';
// import { ExportList } from '@/modules/datacenter/competitor/use';
import MjFormList from '../form/index';
import { emptyFunc } from '../../../utils/func';
import { TableColumn } from '../../../types/vendor/column';
import MjTable from '../../../components/table';
interface IAnyFunc {
  (...args: any): any;
}
interface anyFunc {
  (...args: any): any;
}
export interface ITemplateConfig {
  /** 初始时,是否自动调用 接口拉取数据 **/
  auto?: boolean;
  /** 点击重置按钮调用 **/
  reset?: IAnyFunc;
  /** 禁用重置后重新请求 **/
  disabledResetRequest?: boolean;
  /** 传递给内部table组件的参数 */
  table?: Record<string, unknown> & {
    rowClick?: anyFunc;
    cellDblclick?: anyFunc;
    summaryMethod?: anyFunc;
    showSummary?: boolean;
    selectionChange?: anyFunc;
    select?: anyFunc;
    selectAll?: anyFunc;
    hidePagination?: boolean;
    cellStyle?: anyFunc;
    spanMethod?: anyFunc;
    key?: string;
  };
  /** 导出附加的参数*/
  exportParams?: Record<string, any>;
  /** 是否显示导出按钮*/
  showExport?: boolean;
  /** 导出的URL */
  exportURL?: string | anyFunc;
  export?: anyFunc;
  emptyText?: string | undefined;
  /** 开始搜索前调用的函数,传递搜索参数,需要返回修改后的参数 */
  searchBefore?: anyFunc;
  /** 自定义搜索函数, 调用前还是会调用 searchBefor */
  search?: anyFunc;
  layoutOnResize?: boolean;
  [key: string]: any;
}

type ServiceType = PropType<ReturnType<typeof usePagination> | ReturnType<typeof useRequest>>;
export default defineComponent({
  name: 'ListBmTemplate',
  components: {
    FormList: MjFormList,
    MjTable,
  },
  props: {
    /** 绑定表格的列 **/
    columns: {
      type: Array as PropType<TableColumn<any>[]>,
      default: () => [],
    },
    /** 绑定表格的数据**/
    value: {
      type: Object as any,
    },
    /** 顶部面包屑的配置 **/
    routes: {},
    service: {
      type: Object as ServiceType,
    },
    config: {
      type: Object as PropType<ITemplateConfig>,
      default: () => ({}),
    },
    semiAngle: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, ctx) {
    const formRef = ref<any>();
    const { reset } = props.config || {};
    const reqData = props.service as any;
    const search = () => {
      let value = props.value;
      if (props.config.searchBefore) {
        value = props.config.searchBefore(value);
      }
      if (props.config.search) {
        return props.config.search(value);
      } else if (reqData.name === 'useRequest') {
        return reqData.runAsync(value);
      } else {
        return reqData?.pagination.reQuery(value);
      }
    };
    const onReset = () => {
      if (reset) reset();
      if (!props.config.disabledResetRequest) nextTick(search);
    };
    const onExport = () => {
      if (props.config.exportURL) {
        if (props.config.exportURL instanceof Function) {
          return props.config.exportURL();
        }
        const { exportParams = {} } = props.config;
        let params = props.value;
        if (props.config.searchBefore) {
          params = props.config.searchBefore(params);
        }
        if (exportParams) {
          params = {
            ...params,
            ...exportParams,
          };
        }

        // ExportList(params, props.config.exportURL);
      }
    };
    const onOtherBtn = () => {
      if (props.config.onOtherBtn) {
        props.config.onOtherBtn();
        nextTick(search);
      }
    };
    if (props.config?.auto) {
      search();
    }

    const tableRef = ref<any>();

    return {
      formRef,
      onReset,
      reqData,
      search,
      onExport,
      onOtherBtn,
      tableRef,
    };
  },
  render() {
    const bodyBorderStyle =
      this.semiAngle && !this.$slots.default && !this.$slots.otherBtns
        ? 'border-top-left-radius: 0; border-top-right-radius: 0'
        : '';
    let bodyContainer: VNode | VNode[] | undefined = this.$slots.bodyContainer?.();
    if (!bodyContainer) {
      const table = this.$slots.table ? (
        this.$slots.table
      ) : (
        <mj-table
          ref="tableRef"
          layoutOnResize={this.config?.layoutOnResize}
          stripe={this.config?.table?.stripe === undefined ? true : this.config?.table?.stripe}
          key={this.config?.table?.key}
          border={this.config?.table?.border}
          v-loading={this.reqData?.loading}
          columns={this.columns}
          height={'100%'}
          cell-style={(...args: any) => {
            return this.config?.table?.rowClick
              ? {
                  cursor: 'pointer',
                  ...(this.config.table?.cellStyle?.(...args) || {}),
                }
              : { ...(this.config.table?.cellStyle?.(...args) || {}) };
          }}
          data={this.reqData?.data}
          pagination={
            this.config.table?.hidePagination ? undefined : (this.reqData as any)?.pagination
          }
          onSelect={this.config?.table?.select}
          onSelect-all={this.config?.table?.selectAll}
          onSelection-change={this.config?.table?.selectionChange ?? emptyFunc}
          show-summary={this.config?.table?.showSummary}
          summary-method={this.config?.table?.summaryMethod}
          onrow-click={this.config?.table?.rowClick ?? emptyFunc}
          oncell-dblclick={this.config?.table?.cellDblclick ?? emptyFunc}
          span-method={this.config?.table?.spanMethod}
        >
          <div class="tg-page-empty" slot="empty">
            <empty-common detail-text={this.config?.emptyText || '暂无数据'} />
          </div>
        </mj-table>
      );
      bodyContainer = (
        <div class="bmt-list-body-container" style={bodyBorderStyle}>
          {this.$slots.btnLine && <div class="bmt-list-btn-line">{this.$slots.btnLine}</div>}
          <div class="bmt-list-table-container">{table}</div>
        </div>
      );
    } else {
      bodyContainer = (
        <div class="bmt-list-body-container" style={bodyBorderStyle}>
          {this.$slots.btnLine && <div class="bmt-list-btn-line">{this.$slots.btnLine}</div>}
          {this.$slots.bodyContainer}
        </div>
      );
    }

    const formListProps = {
      class: this.$slots.middle ? undefined : 'mgb-12',
      on: {
        search: this.search,
        reset: this.onReset,
        export: this.onExport,
        other: this.onOtherBtn,
      },
      props: {
        showExport: this.config.showExport,
        semiAngle: this.semiAngle,
      },
      scopedSlots: this.$slots,
    };
    return (
      <article class="tg-page-container bmt-list">
        {this.$slots.searchBefore}
        {(this.$slots.default || this.$slots.otherBtns) && (
          <form-list {...formListProps}>
            {Object.keys(this.$slots).map(key => {
              return <fragments slot={key}>{this.$slots[key]}</fragments>;
            })}
          </form-list>
        )}
        {this.$slots.middle}
        {bodyContainer}
        {this.$slots.footer}
      </article>
    );
  },
});
