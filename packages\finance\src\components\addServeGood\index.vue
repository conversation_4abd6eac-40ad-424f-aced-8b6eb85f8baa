<script src="./index.tsx"></script>
<style lang="scss" scoped>
  .agent-select-dialog-content {
    /* margin-bottom: 16px; */
    /* height: 500px;
    overflow: scroll; */

    .form-content {
      flex: auto;
      display: flex;
    }

    form {
      margin-bottom: 11px;
      display: flex;
      width: 100%;
      .el-form-item {
        margin-bottom: 0;
        margin-right: 12px;
      }
    }

    .agent-select-content {
      display: flex;
      justify-content: space-between;

      .agent-select-list {
        width: 69%;
      }

      .agent-select-selected {
        width: 29%;
        border: 1px solid rgba(0, 0, 0, 0.1);
        padding: 10px;
        overflow: hidden;
        border-radius: 10px;

        .agent-select-selected-list {
          height: 380px;
          overflow: scroll;
        }

        .title {
          display: flex;
          justify-content: space-between;
          font-size: 12px;
        }

        .agent-select-tag {
          margin-bottom: 8px;
          margin-right: 8px;
          height: 30px;
          display: inline-block;
          .el-tag {
            height: 30px;
            color: #333;
          }

          :deep(.el-tag) {
            .el-tag__content {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              max-width: 100px;
            }
          }
        }
      }
    }
  }
</style>
