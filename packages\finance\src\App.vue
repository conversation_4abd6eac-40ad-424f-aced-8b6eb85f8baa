<template>

  <div class="app-container" v-if="!isSpecialLayout">
    <template v-if="!isExternal()">
      <sidebar v-if="token" :menus="menus" :rightCodes="userInfo?.right_codes || []" :ignoreRight="false"></sidebar>
    </template>

    <div class="router-view-container">
      <div class="header" v-if="token">
        <div>
          <MjBreadcrumbs class="flex-shrink" :routes="creadCrumbRoutes" />
        </div>
        <div>
          <el-popover :teleported="false" trigger="hover" :show-after="300" popper-class="user-operator-popover">
            <template #reference>
              <div class="user-info-container">
                <div class="head-img">
                  <img src="./assets/icon-head-img.png" alt="image" />
                </div>
                <div class="job-username">
                  <div class="username line-clamp-1">
                    {{ userInfo?.username || userInfo?.mobile || '' }}
                  </div>
                  <div class="username role line-clamp-1" v-if="userInfo?.role_name">
                    ( {{ userInfo?.role_name }})
                  </div>
                </div>
                <el-icon class="arrow-icon">
                  <CaretBottom />
                </el-icon>
              </div>
            </template>
            <div class="popver-item-list">
              <div class="item" @click="logout">退出登录</div>
            </div>
          </el-popover>
        </div>
      </div>
      <div class="body">
        <!-- <router-view v-slot="{ Component }" class="router-view">
          <transition name="fade" mode="out-in" appear>
            <keep-alive>
              <component v-if="$route.meta?.isKeepLive" :is="Component" :key="$route.fullPath"
                :log="log('✅️ keep', $route)" />
            </keep-alive>
          </transition>
          <transition name="fade" mode="out-in" appear>
            <component v-if="!$route.meta?.isKeepLive" :is="Component" />
          </transition>
        </router-view> -->
        <router-view v-slot="{ Component, route }" class="router-view">
          <transition name="fade-transform" mode="out-in">
            <keep-alive :include="cachedViews">
              <component :is="Component" :key="route.meta.usePathKey ? route.fullPath : route.name" />
            </keep-alive>
          </transition>
        </router-view>
      </div>
    </div>
  </div>

  <div v-else>
    <router-view v-slot="{ Component, route }" class="router-view">
      <transition name="fade-transform" mode="out-in">
        <keep-alive :include="cachedViews">
          <component :is="Component" :key="route.meta.usePathKey ? route.fullPath : route.name" />
        </keep-alive>
      </transition>
    </router-view>
  </div>

</template>
<script src="./App.ts"></script>
<style scoped lang="scss">
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.15s cubic-bezier(0.25, 0.1, 0.25, 1); // 更平滑的动画曲线
  position: absolute;
  width: 100%;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(10px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-10px);
}

.router-view {
  position: relative;
  min-height: 100%;
  /* 添加初始状态避免闪屏 */
  animation: fadeIn 0.1s ease-out forwards;
}

.app-container {
  background-color: #e9edfd;

  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: space-evenly;
  padding: 16px;
}



.user-info-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 16px;

  .head-img {
    width: 28px;
    height: 28px;
    border-radius: 4px;

    img {
      width: 28px;
      height: 28px;
      border-radius: 4px;
    }
  }



  .job-username {
    margin-left: 8px;
    display: flex;
    vertical-align: bottom;

    .job {
      font-size: 14px;
    }

    .username {
      max-width: 80px;
      font-weight: 500;
      font-size: 14px;
      color: #1C1F2E;
      margin-right: 10px;
    }

    .role {
      margin-top: 2px;
      font-size: 12px;
      color: #5C5C67;
    }
  }

  .arrow-icon {
    margin-left: 12px;
    color: #AFB0BD;
  }
}


.user-operator-popover {
  .popver-item-list {
    .item {
      cursor: pointer;
    }
  }
}

.router-view-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
  margin-left: 16px;
  overflow: hidden;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 14px;
    flex-shrink: 0;
  }

  .body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: hidden;
    border-radius: 10px;
    overflow: hidden;

    .router-view {
      height: 100%;
      overflow-y: auto;
      overflow: auto;
    }
  }
}
</style>
