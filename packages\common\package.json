{"name": "common", "version": "1.0.0", "private": true, "description": "", "main": "index.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "^4.0.1", "sass-embedded": "^1.79.4", "typescript": "^5.5.3", "vite": "^5.4.8", "vue-tsc": "^2.1.6"}}