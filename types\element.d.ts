import { VueComponent } from './common';
import { InputInstance, ElDatePicker, ElTimePicker, FormInstance } from 'element-plus';
declare global {
  namespace JSX {
    interface IntrinsicElements {
      [E: string]: { slot: string } & any;
      'el-input': VueComponent & InputInstance;
      'el-icon': VueComponent;
      'el-form': VueComponent<{
        'status-icon'?: boolean;
        'label-width'?: string | number;
      }> &
        FormInstance;
      'el-form-item': VueComponent<{
        label?: string;
        prop?: string;
      }>;
      'el-time-picker': VueComponent<any> & ElTimePicker;
      'el-date-picker': VueComponent & InstanceType<ElDatePicker>;
      'mj-table': VueComponent<{
        'row-key'?: string;
        rowKey?: string;
        border?: boolean;
        pagination?: any;
      }>;
      'el-table-column': VueComponent<{
        prop?: string;
        label?: string;
        width?: string | number;
        minWidth?: string | number;
      }>;
      'el-radio': VueComponent<{
        value?: any;
      }>;
      'el-radio-group': VueComponent<{
        onChange?: (v: any) => void;
      }>;
      'el-button': VueComponent<{
        type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text';
        icon?: string;
        plain?: boolean;
        link?: boolean;
      }>;
      'el-image': VueComponent<{
        src: string;
        fit?: '' | 'fill' | 'contain' | 'cover' | 'none' | 'scale-down';
        lazy?: boolean;
      }>;
    }
  }
}
