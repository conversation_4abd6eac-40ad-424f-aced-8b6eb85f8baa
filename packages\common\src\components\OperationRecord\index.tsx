import { defineComponent, toRefs, PropType } from 'vue';
export default defineComponent({
  name: 'OperationRecord',
  props: {
    list: {
      type: Array as PropType<any[]>,
      require: true,
    },
    title: {
      type: String as PropType<String>,
      default: '操作记录',
    },
  },

  setup(props, { slots }) {
    const { title, list } = toRefs(props);
    return () => (
      <div class="operation-record">
        <div class="title">{title.value}</div>
        <div className="setp-wrapper">
          <el-steps direction={'vertical'}>
            {list?.value?.map(item => (
              <el-step title={item.title}>
                {{
                  icon: () => {
                    return slots.icon ? slots.icon?.(item) : <div class="op-icon"></div>;
                  },
                  description: () => slots.description?.(item),
                }}
              </el-step>
            ))}
          </el-steps>
        </div>
      </div>
    );
  },
});
