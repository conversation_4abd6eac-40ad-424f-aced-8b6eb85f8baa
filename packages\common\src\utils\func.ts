import { RouteLocationGeneric, RouteLocationRaw, RouteRecordRaw, useRouter } from 'vue-router';
import { getToken } from '../config';
import ImageViewer from '../components/Image/ImageViewer';
import queryString from 'query-string';

type ElmentInArrayFunction<V = any> = [string, V];
/**
 * ObjectMap 函数处理过程中拿到的键值对对象
 * @type {[ string, any ]}
 */
export type ElmentInObjectMapCallback<V> = ElmentInArrayFunction<V>;
/**
 * ObjectMap 函数处理过程中拿到的键值对对象
 * @type {[ string, any ]}
 */
export type ElmentInObjectFilterCallback<V> = ElmentInArrayFunction<V>;
export const emptyFunc = () => void 0;
/**
 * 类似 Array.prototype.filter 的方式
 * 将一个非数组对象的特定键值对过滤掉
 * @param {object} obj 待转换的对象
 * @param fn 过滤函数
 * @return {object} newObj 新的对象
 */
export const ObjectFilter = <V = any, T = Record<string, V>>(
  obj: T,
  fn: (
    element: ElmentInObjectFilterCallback<V>,
    index: number,
    array: ElmentInObjectFilterCallback<V>[],
  ) => boolean,
  //@ts-ignore
): Record<string, V> => Object.fromEntries(Object.entries(obj).filter(fn));
/**
 * 过滤掉对象中空值
 * 返回新对象
 * @param {any} obj
 * @return {any} newObj
 */
export const ObjectFilterEmpty = <V = any, T = Record<string, V>>(obj: T): Record<string, V> => {
  if (obj === undefined) return {};
  return ObjectFilter<V, T>(
    obj,
    <V>([_, value]: ElmentInObjectMapCallback<V>): boolean => !isEmpty(value),
  );
};
/**
 * 判断是否是空白字符串
 * @param val
 */
export const isEmptyString = (val: string) => {
  return /^\s*$/.test(val);
};
/**
 * 判断是否空数组
 * @param {any} val
 * @return {boolean}
 */
export const isEmptyArray = (val: any): boolean => Array.isArray(val) && val.length === 0;
/**
 * 判断是否对象(非数组)
 * @param {any} val
 * @return {boolean}
 */
export const isObject = (val: any): boolean => typeof val === 'object' && !Array.isArray(val);
/**
 * 判断是否空对象(非数组)
 * @param {any} val
 * @return {boolean}
 */
export const isEmptyObject = (val: any): boolean => isObject(val) && Object.keys(val).length === 0;
/**
 * 判断变量是否为空值
 * 空字符串、空数组、空对象、undefined、null 均判定为空值
 */
export const isEmpty = (val: any): boolean =>
  typeof val === 'number'
    ? false
    : typeof val === 'string'
    ? isEmptyString(val)
    : val instanceof Date
    ? false
    : val === undefined || val === null || isEmptyArray(val) || isEmptyObject(val);

export const ObjectMap = <V = any, T = Record<string, V>, RV = V>(
  obj: T,
  fn: (
    currentValue: ElmentInObjectMapCallback<V>,
    index?: number,
    array?: ElmentInObjectMapCallback<V>[],
  ) => ElmentInObjectMapCallback<RV>,
  // @ts-ignore
): Record<string, RV> => Object.fromEntries(Object.entries(obj).map(fn));

// @ts-ignore
export function deepClone<T extends Object>(source: T): T {
  const targetObj = source.constructor === Array ? [] : {};
  for (const keys in source) {
    // 遍历目标
    if (source.hasOwnProperty(keys)) {
      if (source[keys] && typeof source[keys] === 'object') {
        // 如果值是对象，就递归一下
        // @ts-ignore
        targetObj[keys] = source[keys].constructor === Array ? [] : {};
        // @ts-ignore
        targetObj[keys] = deepClone(source[keys]);
      } else {
        // 如果不是，就直接赋值
        // @ts-ignore
        targetObj[keys] = source[keys];
      }
    }
  }
  return targetObj as T;
}

export const urlAppendToken = (url: string) => {
  if (url === null || url === undefined) return url;
  // 这里也可以做成, 如果有则替换里面的token
  if (/Authorization=/.test(url)) return url;
  const append = `Authorization=${getToken()}`;
  const searchSymbol = url.includes('?') ? '&' : '?';
  return `${url}${searchSymbol}${append}`;
};

/**
 * 从链接下载文件 hasAuth 是否需要授权
 */
const corsExtList = ['.png', '.jpeg', '.jpg', '.gif', '.pdf', '.zip'];

function isSameOrigin(url1: string, url2: string) {
  const parseUrl = (url: string) => {
    const parser = document.createElement('a');
    parser.href = url;
    return {
      protocol: parser.protocol,
      host: parser.host,
      port: parser.port,
    };
  };

  const origin1 = parseUrl(url1);
  const origin2 = parseUrl(url2);

  return (
    origin1.protocol === origin2.protocol &&
    origin1.host === origin2.host &&
    origin1.port === origin2.port
  );
}

// 通过链接下载文件
export const downloadFileFromLink = (originalLink: string, hasAuth = false, name = '') => {
  if (originalLink.indexOf('oss') < 0) {
    hasAuth = true;
  }
  /* 判断条件后续可自行添加,务必加上注释 */
  const judgmentMap: ((arg: string[]) => any)[] = [
    //替换oss转发
    ([path]) => [path.replace(/https?:\/\/tiange-oss.goumee.com/gi, '/oss')],
    //截取文件名
    ([link]) => [link, link.substring(link.lastIndexOf('/') + 1, link.length)],
    ([link, filename]) => [link, name ? name : filename],
    ([link, filename]) => [link, (filename = decodeURIComponent(filename))],
    //token
    ([link, filename]) => [hasAuth ? urlAppendToken(link) : link, filename],
    // 下载
    async ([link, filename]) => {
      if (
        corsExtList.find(el => originalLink.includes(el)) &&
        !isSameOrigin(originalLink, window.location.href)
      ) {
        // pdf转换URL
        await fetch(link).then(async response => {
          console.log(response);
          // const result = response.clone();
          if (response.status === 200) {
            const data = await response.blob();
            link = URL.createObjectURL(data);
          }
        });
      }
      const elink = document.createElement('a');
      elink.style.display = 'none';
      elink.href = link;
      elink.download = filename;
      elink.target = '_blank';
      document.body.appendChild(elink);
      elink.click();
      document.body.removeChild(elink);
      URL.revokeObjectURL(link);
    },
  ];
  judgmentMap.reduce((a: any, b: any) => b(a), [originalLink]) as any;
};

/** 预览文件 */
export const proviewOnFile = (file: string) => {
  const url = `${file}?Authorization=${getToken()}`;
  if (
    file.includes('.png') ||
    file.includes('.jpg') ||
    file.includes('.jpeg') ||
    file.includes('.pdf')
  ) {
    const hasPDF = /\.pdf\?.+$|\.pdf\??$/.test(file);
    if (hasPDF) {
      window.open(url);
    } else {
      ImageViewer.show([url]);
    }
  } else {
    window.open('https://view.officeapps.live.com/op/view.aspx?src=' + encodeURIComponent(url));
  }
};

export function getFileInfo(url: string) {
  if (url) {
    // const fileInfoURL = new URL(url);
    const fileInfoUrl = decodeURIComponent(url);
    const fileName = fileInfoUrl.substring(fileInfoUrl.lastIndexOf('/') + 1);
    const arr = fileName.split('.');
    const extName = arr[arr.length - 1];
    let icoName;
    if (extName) {
      switch (extName.toLowerCase()) {
        case 'docx':
        case 'doc':
          icoName = 'ico-word';
          break;
        case 'pptx':
        case 'ppt':
          icoName = 'ico-ppt';
          break;
        case 'xls':
        case 'xlsx':
          icoName = 'ico-excel';
          break;
        case 'pdf':
          icoName = 'ico-pdf';
          break;
        case 'mp4':
          icoName = 'ico-icon_tongyong_tongyongxingwenjian_mianxing';
          break;
        case 'bmp':
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'webp':
        case 'gif':
          icoName = 'ico-picture';
          break;
        default:
          icoName = 'ico-icon_tongyong_tongyongxingwenjian_mianxing';
      }
    } else {
      icoName = 'ico-icon_tongyong_tongyongxingwenjian_mianxing';
    }
    return { fileName, icoName };
  } else {
    return { fileName: '', icoName: 'ico-word' };
  }
}

/** 获取文件名 **/
export function basename(url: string): string | null {
  const reg = /[^\\/]+$/.exec(url);
  if (reg) {
    const remove_querys = reg[0].replace(/\?.+$/g, '');
    try {
      return decodeURIComponent(remove_querys);
    } catch (e) {
      return remove_querys;
    }
  } else {
    return null;
  }
}

let routerUUid = 1;
type TransRouterName<T extends string[]> = {
  [P in T[number]]: P;
};
type RParams = string[];

export function mj_crn<T extends RParams>(params: T): TransRouterName<T>;
export function mj_crn<T extends RParams>(name: string, params: T): TransRouterName<T>;
export function mj_crn(...args: any[]): any {
  const name = args[0];
  let params = args[1];
  if (Array.isArray(name)) {
    params = name;
  }
  const result = Object.create(null);
  for (const key of params) {
    result[key] = `${key}_uuid_${routerUUid++}`;
  }
  return new Proxy(result, {
    get(target, name) {
      const hasName = Object.hasOwn(target, name);
      if (hasName) return Reflect.get(target, name);
      throw new Error(`读取RouterName.${name as string}的名字不存在`);
    },
  });
}

export const checkRoutes = (routes: Readonly<RouteRecordRaw[]>) => {
  const onceObj: Record<string | symbol, number> = {};

  function keyCount(
    type: 'name' | 'path',
    prev: Record<string | symbol, number>,
    next: RouteRecordRaw,
  ) {
    if (next[type] === undefined) {
      return;
    }
    const nextValue = next[type] as any;
    const count = prev[nextValue] || 0;
    if (count >= 100000) {
      const message = `${type}重复，请通过path检查-path：${next.path}`;
      if (import.meta.env.MODE !== 'production') {
        alert(message);
        throw new Error(message);
      } else {
        console.error(message);
      }
    }
    prev[nextValue] = count + 1;
  }

  function reduceRoutes(children?: Readonly<RouteRecordRaw[]>) {
    if (!children?.length) return;
    children.reduce((prev, next) => {
      // name不能重复
      if (next.name) keyCount('name', prev, next);
      else {
        // path不能重复
        keyCount('path', prev, next);
      }

      reduceRoutes(next.children);
      return prev;
    }, onceObj);
  }

  reduceRoutes(routes);
  // console.log('onceObj', onceObj);
  return routes;
};

// 跳转到子路由
export const redirectFirstChildren = (to: RouteLocationGeneric): RouteLocationRaw => {
  const route = to.matched[0]?.children?.[0];
  if (!route) {
    throw new Error('找不到子路由');
  }
  return { name: route.name };
};

// 创建输入框回车函数
export const createEnterHandle = (fn: any) => {
  return (e: KeyboardEvent) => {
    if (e.key !== 'Enter') return;
    const input = e.target as HTMLInputElement;
    if (input.tagName.toUpperCase() !== 'INPUT') return;
    if (input.className !== 'el-input__inner') return;
    if (!input.parentElement) return;
    let className = input.parentElement?.className || '';
    if (!className.includes('el-input')) return;
    // 这里额外处理, 如果是下拉框中的input也跳过,  后期看情况添加其他表单中的input
    className = input.parentElement?.parentElement?.className || '';
    if (className.includes('el-select')) return;
    fn();
  };
};

export const isMobile = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};
/**
 * 睡眠
 * 等待给定时间
 * <AUTHOR> <<EMAIL>>
 * @since   2020-09-07 17:09:48
 * @param   {number} time 等待时间 单位 ms
 * @return  {boolean} true---正常运行 false---等待时间过小，未运行直接返回
 */
export const sleep = async (time: number): Promise<boolean> =>
  time > 0 ? new Promise(resolve => setTimeout(() => resolve(true), time)) : Promise.resolve(false);

/**
 * 防抖
 * @param func 需要防抖的方法
 * @param wait 延时时间
 * @param immediate 是否立即执行第一次
 * @returns 返回防抖方法
 */
export function debounce(func, wait = 300, immediate = false) {
  let timeout;
  return function (this: unknown, ...args) {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const context = this;
    // 如果immediate 为true，表示立即执行第一次函数
    const later = () => {
      timeout = null;
      if (!immediate) {
        func.apply(context, args);
      }
    };

    const callNow = immediate && !timeout;

    // 清除之前的定时器并重置
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);

    if (callNow) {
      func.apply(context, args);
    }
  };
}

/**
 * 阿里云图片转换处理
 * @param url 图片地址
 * @param resize 尺寸参数
 */
export const transformOssImageURL = (url: any, resize: string = 'w_100') => {
  try {
    if (typeof url === 'string') {
      const hasOss = /oss/.exec(url);
      const hasQuery = url.includes('?');
      if (!hasOss) return url;
      let newURL = `${url}${hasQuery ? '' : '?'}`;
      newURL = `${newURL}&x-oss-process=image/resize,${resize}`;
      return newURL;
    }
    return url;
  } catch (ex) {
    return url;
  }
};
export type McPushType = 'push' | 'new_page';
export type McPushParams = {
  name?: string;
  url?: string;
  type?: McPushType;
  query?: Record<string, any>;
  params?: Record<string, any>;
};
const McPushMap = new Map<McPushType, any>([
  [
    'push',
    (payload: McPushParams) => {
      const { url, ...rest } = payload;
      window.router.push({
        path: payload.url,
        ...rest,
      });
    },
  ],
  [
    'new_page',
    (payload: McPushParams) => {
      const { url, query } = payload;
      const queryStr = queryString.stringify(query || {});
      window.open(`${url}${queryStr ? '?' + queryStr : ''}`, '_blank');
    },
  ],
]);
export const mc_push = (payload: McPushParams) => {
  const { name, url, type = 'push', ...rest } = payload;
  let newUrl;
  if (url) {
    newUrl = url;
  } else if (name) {
    newUrl = window.router.resolve({
      name,
      params: payload.params,
    })?.path;
  }
  McPushMap.get(type)?.({
    url: newUrl,
    ...rest,
  });
};
