import { OptionType } from '@common/types/base';

export class EnumValueClass<T = number> {
  value: T;
  label: string;

  constructor(value: T, label: string) {
    this.value = value;
    this.label = label;
  }
}

export const EnumValue = (value: any, label: string) => {
  return new EnumValueClass(value, label);
};

export class EnumUtil {
  public static toMjOptions<T = number>() {
    // @ts-ignore
    const keys = Object.keys(this) as (keyof typeof this)[];

    // as UnionToArray<ObjectTypeKeys<typeof this, EnumValueClass>>;

    return keys.map(key => {
      return this[key];
    }) as OptionType<T>[];
  }
  public static toMjMaps() {
    // @ts-ignore
    const keys = Object.keys(this) as (keyof typeof this)[];
    return new Map(
      keys.map(key => {
        const enumValue = this[key] as EnumValueClass;
        return [enumValue.value, enumValue.label];
      }),
    );
  }
}
