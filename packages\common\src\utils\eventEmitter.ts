export default class MjEventEmitter<T> {
  private _events: any = {};
  constructor() {
    this._events = {};
  }
  on(event: T, callback) {
    const callbacks = this._events[event] || [];
    callbacks.push(callback);
    this._events[event] = callbacks;
    return this;
  }
  off(event: T, callback) {
    const callbacks = this._events[event];
    this._events[event] = callbacks && callbacks.filter(fn => fn !== callback);
    return this;
  }
  emit(event: T, ...args) {
    const callbacks = this._events[event];
    callbacks?.forEach(fn => fn.apply(null, [...args]));
  }
}
