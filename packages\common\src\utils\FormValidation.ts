import { ElMessage as Message } from 'element-plus';
import { isEmpty } from '@common/utils/func';

const defaultFiles = ['.pdf', '.doc', '.docx', '.ppt', '.pptx'];
const defaultVideo = ['.mp4', '.mp3'];
const defaultImage = ['.png', '.jpg', '.jpeg', '.webp'];
const exclFiles = ['.xls', '.xlsx', '.csv'];
const defaultDoc = ['.doc', '.docx'];
const defaultPdf = ['.pdf'];
const defaultXml = ['.xml'];
const defaultCsv = ['.csv'];
export const supportPreview = [
  defaultFiles,
  defaultImage,
  exclFiles,
  defaultDoc,
  defaultPdf,
  defaultCsv,
].flat();

export const ValidationFileUpload = (config: {
  image?: boolean;
  fileSize?: number;
  file?: boolean;
  video?: boolean;
  excel?: boolean;
  pdf?: boolean;
  doc?: boolean;
  csv?: boolean;
  xml?: boolean;
  extensions?: string[];
  customize?: any;
}) => {
  return (file: File) => {
    const match = /(\.[^.]+)$/.exec(file.name);
    let ext = '';
    if (match) {
      ext = match[1];
    }

    const extensions: string[] = [];
    if (config.image) extensions.push(...defaultImage);
    if (config.file) extensions.push(...defaultFiles);
    if (config.video) extensions.push(...defaultVideo);
    if (config.excel) extensions.push(...exclFiles);
    if (config.pdf) extensions.push(...defaultPdf);
    if (config.doc) extensions.push(...defaultDoc);
    if (config.csv) extensions.push(...defaultCsv);
    if (config.xml) extensions.push(...defaultXml);
    if (config.extensions) extensions.push(...config.extensions);
    if (config.customize) {
      if (config.customize(file) !== true) return false;
    }
    if (extensions.length > 0) {
      if (!extensions.includes(ext?.toLowerCase())) {
        Message.warning(`文件格式不正确，请使用 ${extensions.join(';')}`);
        return false;
      }
    }

    if (config.fileSize !== undefined) {
      const isLt10M = file.size / 1024 / 1024 < config.fileSize;
      if (!isLt10M) {
        Message.warning(`文件不能超过${config.fileSize}MB!`);
        return false;
      }
    }
    return true;
  };
};

export const validatorArrayField = (length = 3) => {
  return (rule, value, callback) => {
    if (isEmpty(value)) return callback(new Error(rule.message));
    if (Array.isArray(value)) {
      if (value.length < length) callback(new Error(rule.message));
      for (const item of value) {
        if (isEmpty(item)) return callback(new Error(rule.message));
      }
    }
    callback();
  };
};
export default {
  ValidationFileUpload,
};
