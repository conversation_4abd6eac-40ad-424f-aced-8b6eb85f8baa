<script src="./index.tsx"></script>
<style lang="scss" scoped>
.mj-interview-feedback-table {
  display: flex;
  flex-direction: column;
  height: 100%;

  :deep(.item) {
    .category {
      font-size: 12px;
      font-weight: 400;
      /* margin-bottom: 8px;
      margin-top: 16px; */
      margin-bottom: 0;
      margin-top: 0;
    }
    .title {
      margin-left: 40px;
    }
    .el-radio-group {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      margin-left: 60px;
    }
    .is-disabled {
      .el-radio__label {
        color: #464848 !important;
      }
    }
  }
}
</style>
