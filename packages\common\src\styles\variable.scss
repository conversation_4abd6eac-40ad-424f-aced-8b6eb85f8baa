/** 天鸽系统变量 **/
:root {
  --tg-body-min-width: 1380px;
  --tg-font-family: PingFang SC, Microsoft Yahei, Microsoft Sans Serif, Hiragino Sans GB, Tahoma,
    Arial;
  // 主题色，品牌色
  --theme-color: #2c4fec;
  // 主题色-rgb
  --theme-rgb-color: 0, 0, 0;
  // 字体一级颜色， 字体颜色一级
  --text-color: #1c1f2e;
  // 字体一级颜色-rgb
  --text-rgb-color: 26, 26, 26;
  // 字体二级颜色， 字体颜色二级
  --text-second-color: #4a4a4c;
  // 字体三级颜色， 字体颜色三级
  --text-third-color: #afb0bd;
  // 字体三级颜色-rgb
  --text-third-rgb-color: 153, 153, 153;
  // 字体四级颜色， 字体颜色四级
  --text-four-color: #c5c5c8;
  // 字体四级颜色-rgb
  --text-four-rgb-color: 204, 204, 204;
  // 线框颜色
  --border-line-color: #d7dbe7;
  // 线框颜色
  --border-line-rgb-color: 216, 216, 216;
  // 输入框占位字体色
  --placeholder-color: var(--text-four-color);
  //不可操作颜色
  --disabled-color: var(--text-four-color);
  // 图标颜色
  --icon-color: var(--text-third-color);
  // 警告颜色
  --warning-color: #ffac15;
  // 警告颜色-rgb
  --warning-rgb-color: 255, 172, 21;
  // 成功颜色
  --success-color: #00a86f;
  // 成功颜色-rgb
  --success-rgb-color: 0, 168, 11;
  // 错误颜色
  --error-color: #e52929;
  // 错误颜色-rgb
  --error-rgb-color: 255, 0, 22;
  // 默认状态颜色
  --default-status-color: rgba(60, 82, 105, 0.6);
  --default-status-rgb-color: 60, 82, 105;
  //wait、fail状态颜色
  --fail-color: #a4b2c2;
  --fail-rgb-color: 164, 178, 194;
  //btn,a hover
  --primary-hover-color: #5674ff;
  // 默认字体大小
  --font-size-default: 12px;
  // 标题字体大小
  --font-size-t1: 12px;
  // 标题字体大小
  --font-size-t2: 13px;
  // 标题字体大小
  --font-size-t3: 14px;
  // 标题字体大小
  --font-size-t4: 15px;
  // 弹窗标题字体大小
  --font-size-t5: 16px;
  // 字体粗细
  --font-weight-light: 300;
  --font-weight-default: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 600;
  // 默认input、select等高度
  --default-height: 28px;
  // div外框色值
  --border-line-div-color: #d1d8e0;
  // tab标签线条颜色, 表格线条颜色
  --border-line-other-color: #e5e5e5;
  // 表格线条颜色,table线框颜色
  --table-border-color: #f0f0f0;

  // table-表头颜色
  --table-thead-th-bg-color: #f8fafc;
  // table-表格行斑马线颜色
  --table-striped-bg-color: #f4f8ff;
  // table-表格行hover颜色
  --table-current-row-bg-color: #e6f6ff;
  // table-表格行选中颜色
  --table-focus-row-bg-color: #d0eaff;
  --el-font-size-large: 16px;
}

/** 定义饿了么组件样式变量 **/
:root {
  --el-element-height: var(--default-height);
  --el-component-size: var(--default-height);
  --el-element-line-height: calc(var(--el-element-height) - 2px);

  --el-element-mini-height: var(--default-height);
  --el-element-mini-line-height: calc(var(--el-element-mini-height) - 2px);

  --el-element-medium-height: var(--default-height);
  --el-element-medium-line-height: calc(var(--el-element-medium-height) - 2px);

  --el-element-text-color: var(--text-color);
  /** 表单元素 - 边框颜色 */
  // normal 边框颜色
  --el-element-border-color-normal: #d7dbe7;
  // hover 边框颜色
  --el-element-border-color-hover: #aab1c3;
  // focus 边框颜色
  --el-element-border-color-active: var(--theme-color);
  // disabled 边框颜色
  --el-element-border-color-disabled: var(--el-element-border-color-normal);
  // error 边框颜色
  --el-element-border-color-error: var(--error-color);

  --el-element-box-shadow-actived: 0 4px 12px 0 #446ff21a;

  /** 表单元素 - 边框圆角 */
  --el-element-border-radius: 6px;

  /** 表单元素 - 背景颜色 */
  // normal 背景颜色
  --el-element-background-color-normal: white;
  // error 背景颜色
  --el-element-background-color-error: #fdf6f6;
  // disabled 背景颜色
  --el-element-background-color-disabled: #f5f5f5;

  /** 表单元素 - 图标颜色 */
  // 清空图标颜色
  --el-element-clear-icon-color: #bdc1d0;
  // 箭头图标颜色
  --el-element-arrow-icon-color: #8fa9f7;

  /**---------------------- 表格相关颜色 ------------------------------**/
  // 表格表头背景颜色
  --el-table-head-background-color: #e3eafd;
  --el-table-footer-background-color: #e3eafd;
  // 表格圆角
  --el-table-radius: 12px;
  --el-table-box-shadow: 0 2px 20px 0 #1e146a0d;
  --el-table-border-color: var(--table-border-color);
  // 表头 padding
  --el-table-head-padding-topbottom: 9px;
  --el-table-head-padding-leftright: 12px;
  --el-table-head-padding: var(--el-table-head-padding-topbottom)
    var(--el-table-head-padding-leftright);
  // 表格默认行高
  --el-table-line-height: 18px;
  --el-table-cell-padding-topbottom: 7px;
  --el-table-cell-padding-leftright: var(--el-table-head-padding-leftright);
  --el-table-cell-padding: var(--el-table-cell-padding-topbottom)
    var(--el-table-cell-padding-leftright);
  // 表格斑马线
  --el-table-striped-bg-color: var(--table-striped-bg-color);
  // 表格竖线默认高度
  --el-table-max-height: 0;

  /** ---------------------input 输入框 ---------------------------------*/
  --el-input-height: var(--el-element-height);
  --el-input-line-height: var(--el-element-line-height);
  --el-input-font-size: var(--font-size-t1);
  --el-input-text-color: var(--el-element-text-color);
  --el-input-background-color: var(--el-element-background-color-normal);
  --el-input-clear-icon-color: var(--el-element-clear-icon-color);
  --el-input-border-color: var(--el-element-border-color-normal);
  --el-input-padding-left-right: 12px;
  --el-input-padding: 0 var(--el-input-padding-left-right);
  --el-input-box-shadow: none;
  --el-input-append-background-color: #f5f5f5;

  /** -----------------------select 输入框 ------------------------------*/
  --el-select-height: var(--el-element-height);
  --el-select-line-height: var(--el-element-line-height);
  --el-select-font-size: var(--font-size-t1);
  --el-select-text-color: var(--el-element-text-color);
  --el-select-background-color: var(--el-element-background-color-normal);
  --el-select-clear-icon-color: var(--el-element-clear-icon-color);
  --el-select-arrow-icon-color: var(--el-element-arrow-icon-color);
  --el-select-border-color: var(--el-element-border-color-normal);
  --el-select-padding-left-right: 12px;
  --el-select-padding: 0 var(--el-select-padding-left-right);
  --el-select-box-shadow: none;

  /** ----------------------日期选择框 ----------------------------------*/
  --el-date-picker-height: var(--el-element-height);
  --el-date-picker-line-height: var(--el-element-line-height);
  --el-date-picker-font-size: var(--font-size-t3);
  --el-date-picker-text-color: var(--el-element-text-color);
  --el-date-picker-background-color: var(--el-element-background-color-normal);
  --el-date-picker-clear-icon-color: var(--el-element-clear-icon-color);
  --el-date-picker-border-color: var(--el-element-border-color-normal);
  --el-date-picker-padding-left-right: 12px;
  --el-date-picker-padding: 0 var(--el-date-picker-padding-left-right);
  --el-date-picker-box-shadow: none;
  /** ----------------------按钮 ----------------------------------*/
  --el-button-text-color: var(--text-second-color);
  --el-button-text-color-hover: var(--el-button-text-color);
  --el-button-background-color: #fff;
  --el-button-background-color-hover: var(--el-button-background-color);

  --el-button-radius: 6px;
  --el-button-font-size: inherit;
  --el-button-font-size-icon: var(--el-button-font-size);
  --el-button-padding: 10px;
  --el-button-height: var(--default-height);
  --el-button-line-height: calc(var(--default-height) - 1px);
  --el-button-min-width: 56px;
  --el-button-font-weight: 400;
  --el-button-border: none;

  --el-button-disabled-background-color: var(--el-button-background-color);
  --el-button-disabled-text-color: var(--theme-color);
  --el-button-disabled-border: var(--el-button-border);
  --el-button-hover-background-color: var(--el-button-background-color);
  --el-button-hover-border: var(--el-button-border);
  --el-button-hover-text-color: var(--el-button-background-color);

  --el-button-primary-text-color: #ffffff;
  --el-button-primary-background-color: var(--theme-color);
  --el-button-primary-font-weight: 400;
  --el-button-primary-hover-background-color: var(--primary-hover-color);
  --el-button-primary-hover-text-color: var(--el-button-primary-text-color);
  --el-button-primary-border: none;
  --el-button-primary-disabled-background-color: #4c4d4f;
  --el-button-primary-disabled-text-color: #ffffff;

  --el-button-secondary-min-weight: 56px;
  --el-button-secondary-background-color: #f0f3fa;
  --el-button-secondary-border: none;
  --el-button-secondary-font-weight: 600;
  --el-button-secondary-text-color: var(--theme-color);
  --el-button-secondary-hover-background-color: #e0e1e1;
  --el-button-secondary-disabled-background-color: #61636b;
  --el-button-secondary-disabled-text-color: #cfd2da;

  --el-button-link-color: var(--theme-color);
  --el-button-link-border: none;
  --el-button-link-background-color: transparent;
  --el-button-link-hover-background-color: transparent;
  --el-button-link-hover-text-color: var(--el-button-link-color);
  --el-button-link-font-size: 18px;
  --el-button-link-line-height: 18px;
  --el-button-link-height: 18px;
  --el-button-link-padding: 0;
  --el-button-link-disabled-text-color: var(--text-four-color);
  --el-button-link-disabled-background-color: var(transparent);
  --el-button-small-font-size: var(--font-size-t2);
  --el-button-small-padding: 0 6px;
  --el-button-small-radius: 3px;
  --el-button-small-height: 24px;
  --el-button-small-line-height: 22px;

  --el-button-text-text-color: var(--text-second-color);
  --el-button-text-hover-text-color: var(--theme-color);

  --el-button-mini-font-size: var(--font-size-t1);
  --el-button-mini-padding: 0 6px;
  --el-button-mini-radius: 3px;
  --el-button-mini-height: 18px;
  --el-button-mini-line-height: 16px;

  --el-button-medium-font-size: var(--font-size-t1);
  --el-button-medium-padding: 0 10px;
  --el-button-medium-radius: 6px;
  --el-button-medium-min: 56px;
  --el-button-medium-height: 28px;
  --el-button-medium-line-height: 26px;
  --el-button-medium-font-size-icon: var(--font-size-t2);

  --el-button-large-font-size: var(--font-size-t3);
  --el-button-large-padding: 0 14px;
  --el-button-large-radius: 8px;
  --el-button-large-min: 62px;
  --el-button-large-height: 32px;
  --el-button-large-line-height: 30px;
  --el-button-large-font-size-icon: var(--font-size-t5);

  --el-menu-active-color: var(--theme-color) !important;
  --el-menu-hover-bg-color: #e9edfd !important;
}

//共用
:root {
  // old   ===>  tip | icon
  --tip-icon-color: #a4b2c2;
  // tip | icon --rgb
  --tip-icon-rgb-color: 164, 178, 194;
  // old-描述性字体颜色
  --text-des-color: #999999;
}
