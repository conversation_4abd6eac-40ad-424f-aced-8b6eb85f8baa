import { defineComponent, ref, onMounted, onUnmounted, nextTick } from 'vue';
import './index.scss';
import { setToken, setUserInfo } from '@/utils/token';
import { get_login_sms, save_login } from '@/services/login';
import { ElForm, ElMessage, ElMessage as Message } from 'element-plus';
import { useRouter } from 'vue-router';
import { RouterNameHome } from '@/router/types';
import useSmsCode from './use/use-sms-code';
import { useAuthStore } from '@/store';
import { useHeaderConfig } from '@/router/headerCnfig';
import { useHeaderMenus } from '@/modules/login/use/menu';
import { isMobile } from 'common';
import logo from '../../../../common/src/assets/icon-logo.png';

export default defineComponent({
  setup() {
    let captchaModel;
    const is_mobile = ref(isMobile());
    const authStore = useAuthStore();
    const formDataRef = ref<InstanceType<typeof ElForm>>();
    const formData = ref({
      mobile: '',
      account_pwd: '',
      code: '',
    });
    const codeObj = useSmsCode();
    const handleGetPhoneCode = async (validResult: any) => {
      const result = await formDataRef.value?.validateField('mobile');
      if (result) {
        if (codeObj.isCounting.value) {
          return;
        }
        const { data: response } = await get_login_sms(formData.value.mobile, validResult);
        if (response.success) {
          send_code.value = true;
          codeObj.start();
          formDataRef.value?.clearValidate('code');
        } else {
          ElMessage.error(response.message);
        }
      }
    };
    const router = useRouter();
    const send_code = ref(false);
    const submitForm = async () => {
      const result = await formDataRef.value?.validate();
      if (result) {
        const { data: response } = await save_login({
          mobile: formData.value.mobile,
          code: formData.value.code,
        });
        if (response.success) {
          setToken(response.data?.token);
          authStore.setToken(response.data?.token);
          authStore.setUseInfo(response.data?.user_info);
          setUserInfo(response.data?.user_info);
          /** 去有权限的界面 */
          const menus = useHeaderConfig();
          const { transFindHeaderMenuName } = useHeaderMenus();
          const url_name = await transFindHeaderMenuName(
            menus || [],
            response.data?.user_info?.right_codes || [],
            false,
          );
          router.push({
            name: url_name || RouterNameHome.home,
          });
        } else {
          Message.error(response.message);
        }
      }
    };
    const rules = {
      mobile: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        {
          validator: (_, value, callback) => {
            if (value && value.length !== 11) {
              callback(new Error('请输入有效手机号'));
            } else {
              callback();
            }
          },
          trigger: 'blur',
        },
      ],
      code: [{ required: true, message: '请输入短信验证码', trigger: 'blur' }],
    };
    onMounted(async () => {
      console.log('-----onMounted');
      (window as any).initAlicom4(
        {
          captchaId: 'd646cd46f8d1d8fcd5df45fa8966049d', // 请填入appId
          product: 'bind',
        },
        function (captchaObj) {
          captchaModel = captchaObj;
          captchaObj.onSuccess(function () {
            const result = captchaObj?.getValidate();
            const { captcha_id, ...resultRest } = result || {};
            console.log('result', result);
            handleGetPhoneCode(resultRest);
          });
          captchaObj.onError(function (error) {
            ElMessage.error(error.msg || '系统错误，请稍后重试');
            console.log('error', error);
          });
        },
      );
      nextTick(() => {
        if (isMobile()) {
          const app = document.querySelector('#app') as HTMLElement;
          app.classList.add('is-mobile');
        }
      });
    });

    async function handleCode() {
      const result = await formDataRef.value?.validateField('mobile');
      formDataRef.value?.clearValidate('code');
      if (result) {
        captchaModel?.showCaptcha();
      }
    }

    onUnmounted(() => {
      if (isMobile()) {
        const app = document.querySelector('#app') as HTMLElement;
        app.classList.remove('is-mobile');
      }
    });

    return () => (
      <div
        class={{
          'login-page': true,
          'is-mobile': is_mobile.value,
        }}
      >
        <div class="login-left-warp">
          <div class="icon-text"></div>
        </div>
        <div class="login-right-warp">
          <div class="login-right-warp-form">
            <div class="login-right-warp-form_title">欢迎登录</div>
            <div class="login-right-warp-form_sub_title">
              <div class="logo">
                <img src={logo} alt="image" />
              </div>
              <div>财务管理系统</div>
            </div>
            <el-form
              ref={formDataRef}
              style="max-width: 600px"
              model={formData.value}
              status-icon={false}
              rules={rules}
              label-width="auto"
              class="demo-formData"
            >
              <el-form-item prop="mobile">
                <el-input
                  maxlength="11"
                  v-model={formData.value.mobile}
                  autocomplete="off"
                  placeholder="请输入手机号"
                />
              </el-form-item>
              {/*<el-form-item prop="AccountPwd">
            <el-input  v-model={formData.value.account_pwd} autocomplete="off"
            placeholder="请输入密码" />
          </el-form-item>*/}
              <el-form-item prop="code">
                <el-input
                  maxlength="6"
                  v-model={formData.value.code}
                  placeholder="请输入短信验证码"
                  auto-complete="off"
                  ref="codeRef"
                  autocomplete="off"
                  onKeypressEnterNative={submitForm}
                >
                  {{
                    suffix: () => {
                      return (
                        <div class="suffix-center">
                          <span
                            class={[codeObj.isCounting.value ? 'disabled' : '', 'vcode-btn']}
                            onClick={handleCode}
                          >
                            {codeObj.label.value}
                          </span>
                        </div>
                      );
                    },
                  }}
                </el-input>
                {send_code.value && <div class="code-tip tart">验证码已发送，15分钟内输入有效</div>}
              </el-form-item>
              <div class="login-footer">
                <el-button class="submit-btn" type="primary" onClick={submitForm}>
                  登录
                </el-button>
              </div>
            </el-form>
          </div>
        </div>
      </div>
    );
  },
});
