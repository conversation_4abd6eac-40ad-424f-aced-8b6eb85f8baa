import { HeaderMenuType } from '@common/components/header';
import { useRoute, useRouter } from 'vue-router';

export function checkHasAuth(rights: number[], rightCodes: number[], ignoreRight: boolean) {
  if (ignoreRight) return true;
  if (!rights?.length) return true;
  return !!rights.find(el => rightCodes?.includes(el));
}

export const useHeaderMenus = () => {
  let router = useRouter();
  let route = useRoute();

  function findActiveMenu(menus?: any[], currentPath?: string) {
    for (const menu of menus as any[]) {
      if (currentPath === menu.path) {
        return menu;
      }
      if (menu.children) {
        const found = findActiveMenu(menu.children, currentPath);
        if (found) return found;
      }
    }
    return null;
  }

  const transFormMenus = (menus: HeaderMenuType[], rightCodes: number[], ignoreRight: boolean) => {
    if (!router) {
      router = useRouter();
    }
    return menus.map(el => {
      let tempRoute;
      try {
        tempRoute =
          el.name &&
          !el.hidden &&
          (router.resolve({
            name: el.name,
          }) as any);
      } catch (e) {
        console.info('error-menu: ', el, e);
      }
      return {
        ...el,
        label: el.label,
        path: el.path || (tempRoute && tempRoute.path),
        hasAuth: checkHasAuth(tempRoute?.meta?.rights, rightCodes, ignoreRight),
        rights: tempRoute?.meta?.rights,
        children: transFormMenus(el.children || [], rightCodes, ignoreRight),
      };
    });
  };
  const transformHeaderMenus = (
    headerMenus: HeaderMenuType[],
    rightCodes: number[],
    ignoreRight: boolean,
  ) => {
    const tempMenus = transFormMenus(headerMenus || [], rightCodes, ignoreRight);

    return tempMenus.map(el => {
      const rights = [
        ...(el.rights || []),
        ...(el.children?.map(child => child.rights)?.flat() || []),
      ];
      return {
        ...el,
        hasAuth: checkHasAuth(rights, rightCodes, ignoreRight),
      };
    });
  };
  const findFirstPermissionRoute = (
    headerMenus: HeaderMenuType[],
    rightCodes: number[],
    ignoreRight: boolean,
  ) => {
    const menus = transformHeaderMenus(headerMenus, rightCodes, ignoreRight);
    const firstMenu = menus?.filter(el => !el.hidden && el.hasAuth)?.[0];
    return firstMenu?.children?.filter(el => !el.hidden && el.hasAuth)?.[0];
  };
  return {
    transformHeaderMenus,
    findFirstPermissionRoute,
    findActiveMenu,
  };
};
