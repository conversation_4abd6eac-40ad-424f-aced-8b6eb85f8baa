import { ElButton, ElMessageBox } from 'element-plus';
import { defineComponent } from 'vue';
import './index.scss';
import { IBtnItemProps, IButtonGroupProps } from './interface';
import exportProcess from '../exportProcess';
import { ButtonKeyGroup, ButtonKeys } from './config';
import { useDialog } from './../../use/dialog';
import ImportProcess from '../ImportProcess';

const ButtonGroup = defineComponent<IButtonGroupProps>({
  props: ['btnList'],
  emits: ['buttonGroupClick'],
  setup(props, { emit }) {
    const dialogExportProcess = useDialog({
      title: '导出进度',
      width: 800,
      cancelText: '关闭',
      disabledOK: true,
      component: exportProcess,
      footer: false,
    });
    const dialogImportProcess = useDialog({
      title: '导入进度',
      width: 800,
      cancelText: '关闭',
      disabledOK: true,
      component: ImportProcess,
      footer: false,
    });
    const onButtonClick = (key: string, item: IBtnItemProps) => {
      switch (key) {
        case ButtonKeys.EXPORT_PROGRESS:
          dialogExportProcess.show(item?.service);
          break;
        case ButtonKeys.DELETE:
          ElMessageBox.confirm('确定删除？', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
          }).then(() => {
            emit('buttonGroupClick', key, item);
          });
          break;
        case ButtonKeys.IMPORT_PROGRESS:
          dialogImportProcess.show(item?.service);
          break;
        default:
          emit('buttonGroupClick', key, item);
          break;
      }
    };

    return () => {
      return (
        <div class="mj-button-group-wrapper">
          {props.btnList
            ?.filter(item => !item.isAuth)
            .map(item => {
              const isLink = (item?.custom?.type || item?.type) === 'link';
              const label = item.custom?.label || ButtonKeyGroup?.[item?.key]?.label;

              if (isLink) {
                return (
                  <ElButton type="primary" link onClick={() => onButtonClick(item.key, item)}>
                    {label}
                  </ElButton>
                );
              }
              return (
                <ElButton
                  type={item?.custom?.type || item.type || 'default'}
                  icon={item?.custom?.icon || ButtonKeyGroup?.[item.key]?.icon}
                  onClick={() => onButtonClick(item.key, item)}
                >
                  {label}
                </ElButton>
              );
            })}
        </div>
      );
    };
  },
});

export default ButtonGroup;
