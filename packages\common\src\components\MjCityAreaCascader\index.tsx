import { computed, defineComponent, useModel } from 'vue';

export default defineComponent({
  props: {
    modelValue: {
      type: Object,
    },
    config: {
      type: Object,
    },
    width: {
      type: String,
    },
  },
  emits: ['update:modelValue'],
  setup(props, ctx) {
    const modelValue = useModel(props, 'modelValue');
    const newProps = computed(() => {
      const { props: configProps, ...restProps } = props.config || {};
      const { props: customProps, ...restAttrs } = ctx.attrs || {};
      return {
        ...(restProps || {}),
        ...(restAttrs || {}),
        props: { ...(configProps || {}), ...(customProps || {}) },
      };
    });
    return () => {
      return (
        <div class="mj-city-area-cascader-container" style={`width: ${props.width}`}>
          <el-cascader
            model-value={modelValue.value}
            {...newProps.value}
            class="city-area-cascader__inner"
            onUpdate:modelValue={(val) => ctx.emit('update:modelValue', val)}
          />
        </div>
      );
    };
  },
});
