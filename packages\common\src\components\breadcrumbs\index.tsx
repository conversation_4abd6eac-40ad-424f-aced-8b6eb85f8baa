import { defineComponent, PropType, computed, VNode } from 'vue';
import { useRouter } from 'vue-router';
import { useCityStore } from '../../store/city';
export default defineComponent({
  name: 'MjBreadcrumbs',
  props: {
    routes: {
      type: Array as PropType<any[]>,
      required: true,
    },
    moduleName: {
      type: String,
    },
  },
  setup(props) {
    const cityStore = useCityStore();
    const router = useRouter();
    const currentCityName = computed(() => cityStore.getCurrentCityName);

    const breadcrumbsRoutes = computed<VNode[]>(() => {
      const tempRoutes: VNode[] = [];
      if (props.routes) {
        props.routes.forEach((route, index) => {
          const { title, ..._props } = route;
          if (index < props.routes.length - 1) {
            if (_props.name === undefined && _props.path === undefined) {
              tempRoutes.push(<a class="breakcrumb-no-link">{title}</a>);
            } else {
              tempRoutes.push(
                <a
                  class="breakcrumb-link"
                  onClick={(e: MouseEvent) => {
                    e.preventDefault();
                    router.push({
                      name: route.name,
                    });
                  }}
                >
                  {title}
                </a>,
              );
            }
            tempRoutes.push(<span> / </span>);
          } else {
            tempRoutes.push(<a class="breakcrumb-no-link">{route.title}</a>);
          }
        });
      }
      return tempRoutes;
    });
    const displayModuleName = computed(() => {
      const baseModuleName = props.moduleName || [...(props.routes || [])].pop()?.title || '';
      if (baseModuleName.includes('城市商品管理') || baseModuleName.includes('城市商品编辑')) {
        return `${currentCityName.value}-${baseModuleName}`;
      }
      return baseModuleName;
    });
    return () => (
      <div class="mj-breadcrumbs-container">
        <div class="current-name">{displayModuleName.value}</div>
        <div class="breadcrumbs">{breadcrumbsRoutes.value}</div>
      </div>
    );
  },
});
