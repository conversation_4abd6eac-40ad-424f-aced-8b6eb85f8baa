import { useCityList, useStoreCityList, useAgentsStoreCityList } from '@common/service';
import { reactive } from 'vue';
const CascaderProps = {
  expandTrigger: 'hover' as const,
  label: 'name',
  value: 'id',
  // emitPath: false,
  // checkStrictly: true,
};
export const useCityAreaConfig = (options?: {
  dataType?: 'all' | 'managed';
  userType?: 'agents' | 'brand';
  cascaderProps?: Record<string, any>;
  cityLevel?: number;
}) => {
  const cityList =
    options?.dataType === 'managed'
      ? options?.userType === 'agents'
        ? useAgentsStoreCityList({ level: options?.cityLevel })
        : useStoreCityList({ level: options?.cityLevel })
      : useCityList({
          level: options?.cityLevel,
        });
  return reactive({
    options: cityList,
    props: { ...CascaderProps, ...(options?.cascaderProps || {}) },
    collapseTags: true,
  });
};
