import { defineComponent, Fragment, onMounted, ref } from 'vue';
import { MJFormList } from '@common/components';
import { Select } from '@common/components/select/Select';
import { clearingStatus } from '@/enums/settlement';
import { MjTableColumn } from '@common/types/vendor/column';
import { downloadFileFromLink, FD, sleep, useDialog, usePaginationV2, useRequest } from 'common';
import ImportFile from 'common/src/components/importFile';
import {
  query_clear_division_list,
  review,
  query_import_list,
  ImportOrderRecordFile,
} from '@/services/settlement';
import { ElMessage, ElMessageBox } from 'element-plus';
import ImportProcess from '@common/components/ImportProcess';
import { usePermission } from '@/utils/permission';
import ReviewDialog from '@/modules/settlement/dialog/review';

export default defineComponent({
  setup() {
    const permission = usePermission();

    const initFormData = () => {
      return { importTime: [], status: '', reviewTime: [] };
    };

    const formData = ref(initFormData());

    const columns = ref<MjTableColumn<any>[]>([
      {
        label: '清分记录编号',
        minWidth: 180,
        align: 'center',
        prop: 'settlement_number',
        showOverflowTooltip: true,
      },
      {
        label: '账单总金额',
        minWidth: 180,
        align: 'center',
        prop: 'settlement_amount__yuan',
        showOverflowTooltip: true,
        formatter: row => {
          return FD.addThousandsSep(row.settlement_amount__yuan);
        },
      },
      {
        label: '代理商结算金额',
        minWidth: 180,
        align: 'center',
        prop: 'agent_settlement_amount__yuan',
        showOverflowTooltip: true,
        formatter: row => {
          return FD.addThousandsSep(row.agent_settlement_amount__yuan);
        },
      },
      {
        label: '美甲师结算金额',
        minWidth: 180,
        align: 'center',
        prop: 'manicurist_settlement_amount__yuan',
        showOverflowTooltip: true,
        formatter: row => {
          return FD.addThousandsSep(row.manicurist_settlement_amount__yuan);
        },
      },
      {
        label: '品牌抽佣',
        minWidth: 180,
        align: 'center',
        prop: 'brandseller_commission_amount__yuan',
        showOverflowTooltip: true,
        formatter: row => {
          return FD.addThousandsSep(row.brandseller_commission_amount__yuan);
        },
      },
      {
        label: '导入时间',
        minWidth: 180,
        align: 'center',
        prop: 'import_datetime',
        showOverflowTooltip: true,
      },
      {
        label: '导入人员',
        minWidth: 180,
        align: 'center',
        prop: 'import_operator_username',
        showOverflowTooltip: true,
      },
      {
        label: '复核时间',
        minWidth: 180,
        align: 'center',
        prop: 'review_datetime',
        showOverflowTooltip: true,
      },

      {
        label: '复核人员',
        minWidth: 180,
        align: 'center',
        prop: 'review_operator_username',
        showOverflowTooltip: true,
      },
      {
        label: '状态',
        minWidth: 180,
        align: 'center',
        prop: 'status',
        dataType: {
          type: 'enum',
          enum: clearingStatus.toMjMaps(),
        },
      },
      {
        label: '清分进度',
        minWidth: 180,
        align: 'center',
        showOverflowTooltip: true,
        formatter: row => {
          return (
            <Fragment>
              <span>
                {row.completed_detail_count}/{row.detail_count}
              </span>
            </Fragment>
          );
        },
      },
      {
        label: '操作',
        minWidth: 120,
        align: 'center',
        fixed: 'right',
        formatter: row => {
          return (
            <Fragment>
              <el-button
                link
                type="primary"
                onClick={() => {
                  downloadFileFromLink(row.import_file_url);
                }}
              >
                下载
              </el-button>
              {row.status === clearingStatus.pendingReview.value && permission.manage_review && (
                <el-button
                  link
                  type="primary"
                  onClick={() => {
                    reviewDialog.show(row);
                  }}
                >
                  复核
                </el-button>
              )}
            </Fragment>
          );
        },
      },
    ]);

    const getParams = () => {
      const params = { ...formData.value };
      return {
        status: params.status,
        start_import_datetime: params.importTime?.[0] ? params.importTime[0] : undefined,
        end_import_datetime: params.importTime?.[1] ? params.importTime[1] : undefined,
        start_review_datetime: params.reviewTime?.[0] ? params.reviewTime[0] : undefined,
        end_review_datetime: params.reviewTime?.[1] ? params.reviewTime[1] : undefined,
      };
    };

    // 复核
    const reviewReq = useRequest(review, {
      manual: true,
      onSuccess(_, oData) {
        ElMessage.success((oData as any).message);
        reqList.reload();
      },
    });

    const reqList = usePaginationV2(query_clear_division_list, {
      manual: true,
      defaultParams: [formData.value],
    });

    const onQuery = () => {
      reqList.runAsync(getParams());
    };

    const reset = () => {
      formData.value = initFormData();
      onQuery();
    };

    const dialogImportFile = useDialog({
      component: ImportFile,
      title: '导入',
      cancelText: '取消',
      okText: '确定',
      on: {
        submit() {
          onQuery();
        },
      },
    });

    const dialogExprotProcess = useDialog({
      title: '导入进度',
      width: 800,
      cancelText: '关闭',
      disabledOK: true,
      component: ImportProcess,
      footer: false,
    });

    // 审核
    const reviewDialog = useDialog({
      component: ReviewDialog,
      title: '',
      width: 420,
      disabledOK: true,
      disabledCancel: true,
      on: {
        pass() {
          onQuery();
        },
        dismissal() {
          onQuery();
        },
      },
    });

    onMounted(async () => {
      await onQuery();
    });

    return () => (
      <div class="page-container">
        <MJFormList onSearch={onQuery} onReset={reset}>
          {{
            default: () => [
              <el-form-item style="width: 252px">
                <el-date-picker
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  v-model={formData.value.importTime}
                  type="daterange"
                  range-separator="~"
                  start-placeholder="导入日期"
                  end-placeholder="导入日期"
                />
              </el-form-item>,
              <el-form-item style="width: 252px">
                <el-date-picker
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  v-model={formData.value.reviewTime}
                  type="daterange"
                  range-separator="~"
                  start-placeholder="复核日期"
                  end-placeholder="复核日期"
                />
              </el-form-item>,
              <el-form-item style="width: 120px">
                <Select
                  v-model={formData.value.status}
                  placeholder="状态"
                  options={clearingStatus.toMjOptions()}
                ></Select>
              </el-form-item>,
            ],
            otherBtns: () => (
              <Fragment>
                {permission.manage_import && (
                  <el-button
                    class="mgl-12"
                    type="primary"
                    v-loading={false}
                    onClick={() => {
                      dialogImportFile.show(
                        async (file_path: string) => {
                          const [{ data: response }, _] = await Promise.all([
                            await ImportOrderRecordFile({
                              import_file_url: file_path,
                            }),
                            await sleep(200),
                          ]);
                          return response;
                        },
                        {
                          is_no_return_data: true,
                          successMessage: '数据导入中，请在导入进度查看导入结果',
                        },
                      );
                    }}
                  >
                    导入
                  </el-button>
                )}
                <el-button
                  class="mgl-12"
                  type="primary"
                  v-loading={false}
                  onClick={() => {
                    dialogExprotProcess.show(query_import_list);
                  }}
                >
                  导入进度
                </el-button>
              </Fragment>
            ),
          }}
        </MJFormList>
        <section class="table-section">
          <mj-table
            v-loading={reqList?.loading}
            height="100%"
            data={reqList.data}
            columns={columns.value}
            pagination={{
              ...reqList.pagination,
            }}
          ></mj-table>
        </section>
      </div>
    );
  },
});
