import { defineComponent, ref } from 'vue';
import { MjTableColumn } from '@common/types/vendor/column';
import { Close } from '@element-plus/icons-vue';
import { usePaginationV2 } from '@common/utils/requestHooks';
import { Select } from '@common/components/select/Select';
import { useCityList } from '@common/service';
import { createEnterHandle } from '@common/utils/func';
export default defineComponent({
  props: ['service', 'enums', 'city_type'],
  setup({ service, enums, city_type }, { expose, emit }) {
    const reqList = usePaginationV2(service, { manual: true });
    const formData = ref<any>({});
    const selected = ref<any[]>([]);
    const { storeStatus } = enums || {};
    const columns: MjTableColumn<any>[] = [
      {
        label: '',
        width: 50,
        showOverflowTooltip: true,
        prop: 'id',
        align: 'center',
        formatter(row) {
          const findIndex = selected.value.findIndex((t: any) => t.id === row.id);
          const checked = findIndex !== -1;
          return (
            <el-checkbox
              modelValue={checked}
              onUpdate:modelValue={v => {
                if (v) {
                  selected.value.push(row);
                } else {
                  selected.value.splice(findIndex, 1);
                }
                console.log('点击了', v);
              }}
            />
          );
        },
      },
      { label: '门店名称', minWidth: 120, showOverflowTooltip: true, prop: 'name' },
      { label: '门店ID', width: 60, align: 'center', prop: 'id' },
      {
        label: '所在区域',
        minWidth: 120,
        showOverflowTooltip: true,
        formatter(row) {
          const { province, area, city } = row;
          const cities = [province, city, area].filter(Boolean);
          if (cities.length === 0) return '--';
          return cities.join('/');
        },
      },
      {
        label: '门店状态',
        align: 'center',
        prop: 'status',
        width: 80,
        dataType: {
          type: 'enum',
          enum: new Map([
            [1, '经营中'],
            [-1, '已停业'],
            [2, '合作终止'],
          ]),
        },
      },
      { minWidth: 120, label: '代理商名称', prop: 'agent_name', showOverflowTooltip: true },
    ];
    const cityList = useCityList(city_type);
    const cascaderProps = {
      expandTrigger: 'hover' as const,
      checkStrictly: true,
      label: 'name',
      value: 'name',
    };
    const query = () => {
      const { cities: [province, city, area] = [], ...rest } = formData.value;
      reqList.runAsync({
        ...rest,
        province,
        city,
        area,
      });
    };
    const onKeyUpHandle = createEnterHandle(query);
    expose({
      show(data, city_id) {
        formData.value.city_id = city_id;
        selected.value = JSON.parse(JSON.stringify(data));
        query();
      },
      onSaveBtnClick() {
        emit('submit', selected.value);
        emit('close');
      },
    });
    return () => {
      return (
        <div class="dialog-store">
          <div class="area area-data ">
            <el-form class="item-no-margin" onKeyup={onKeyUpHandle}>
              <el-form-item>
                <el-input placeholder="门店名称" v-model={formData.value.name} />
              </el-form-item>
              <el-form-item>
                <el-input placeholder="门店ID" v-model={formData.value.id} />
              </el-form-item>
              {/* <el-form-item style="width:240px">
                <el-cascader
                  v-model={formData.value.cities}
                  options={cityList.value || []}
                  props={cascaderProps}
                  style="width:100%"
                  placeholder="所在地区"
                />
              </el-form-item> */}
              <el-form-item>
                <Select
                  v-model={formData.value.status}
                  options={storeStatus?.toMjOptions()}
                  placeholder="门店状态"
                />
              </el-form-item>
              <el-form-item>
                <el-input placeholder="代理商名称" v-model={formData.value.agent_name} />
              </el-form-item>
              <el-form-item class="operating">
                <el-button type="primary" onClick={query}>
                  查询
                </el-button>
                <el-button
                  onClick={() => {
                    formData.value = { city_id: formData.value.city_id };
                    query();
                  }}
                >
                  重置
                </el-button>
              </el-form-item>
            </el-form>
            <div class="table-box">
              <mj-table
                border
                columns={columns}
                data={reqList.data}
                height="100%"
                style="height:100%"
                pagination={reqList.pagination}
              />
            </div>
          </div>
          <div class="area selected-box">
            <div class="header">
              <span>已选择 {selected.value.length} 个门店</span>
              <a
                onClick={() => {
                  selected.value = [];
                }}
              >
                清空选中
              </a>
            </div>
            <div class="selected-body">
              {selected.value.map((item, index) => {
                return (
                  <div class="selected-item">
                    <span>{item.name}</span>
                    <el-icon
                      onClick={() => {
                        selected.value.splice(index, 1);
                      }}
                    >
                      <Close />
                    </el-icon>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      );
    };
  },
});
