import { ElButton } from 'element-plus';
import './index.scss';
import { defineComponent, VNode } from 'vue';
import { createEnterHandle } from '@common/utils/func';

export default defineComponent({
  props: {
    showExport: {
      type: Boolean,
      default: () => false,
    },
    disabledExport: {
      type: Boolean,
      default: () => false,
    },
  },
  emits: ['search', 'reset', 'export', 'other'],
  setup(props, context) {
    const onEnterKey = createEnterHandle(() => {
      context.emit('search');
    });

    return (
      <div class={['tg-card', 'searchBar', 'gm-generally-search-bar']} onKeyup={onEnterKey}>
        <el-form
          size="mini"
          show-message={false}
          hide-required-asterisk={true}
          label-width="60px"
          ref="formRef"
          nativeOn={{
            submit: (e: Event) => {
              e.preventDefault();
            },
          }}
        >
          <div class={['formItemContainer', 'gm-generally-form']}>
            {(context.slots.default as any)?.map((item: VNode) => {
              return <div class={['formItem', 'gm-generally-form-item']}>{item}</div>;
            })}
            {context.slots.searchBtn ? (
              context.slots.searchBtn
            ) : (
              <div
                class={['formItemSearchOperating', 'gm-generally-form-item-buttons']}
                style={{
                  flex: props.showExport ? 'flex: 1 1 auto' : '0 0 auto',
                }}
              >
                <ElButton type="primary" onClick={() => context.emit('search')}>
                  查询
                </ElButton>
                <ElButton class="mgl-8" onClick={() => context.emit('reset')}>
                  重置
                </ElButton>
                {props.showExport && (
                  <ElButton
                    class="mgl-8"
                    disabled={props.disabledExport}
                    onClick={() => context.emit('export')}
                  >
                    导出
                  </ElButton>
                )}
              </div>
            )}
            <div class="mgl-8 mgb-8" onClick={() => context.emit('other')}>
              {context.slots.otherBtns}
            </div>
          </div>
        </el-form>
      </div>
    );
  },
});
