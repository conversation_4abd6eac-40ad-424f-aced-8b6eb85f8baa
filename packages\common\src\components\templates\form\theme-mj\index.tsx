import { computed, defineComponent, ref, VNode } from 'vue';
import './index.scss';
import { ElButton } from 'element-plus';
import { useResizeObserver } from './useResizeObserver';
import { createEnterHandle } from '@common/utils/func';
import MJForm from '@common/components/MJForm';
import { ButtonGroup } from '@common/components';
import { ButtonKeys } from '@common/components/ButtonGroup/config';
import { IBtnItemProps } from '@common/components/ButtonGroup/interface';
import { transformVue2Slots } from '@common/use/form';

const getVNodeTag = (node: VNode) => {
  return (node.type as any)?.name ?? '';
};
const getTypePlaceholderWidth = (node: VNode) => {
  const tag = getVNodeTag(node).toLowerCase();
  let start_width = 120;
  if (tag.includes('department-select')) {
    return 220;
  } else if (tag.includes('input') || tag.includes('select')) {
    const strlen = Math.max(0, getVNodePlaceHolderLength(node) - 4);
    return Math.max(start_width, Math.abs(strlen * 20) + start_width);
  } else if (tag.includes('date-picker')) {
    const placeholderLength = ((node as any)?.ctx?.attrs?.['start-placeholder'] || '').length;
    const strlen = Math.max(0, placeholderLength - 4);
    start_width = 220;
    return Math.max(start_width, Math.abs(strlen * 20) + start_width);
  } else {
    return undefined;
  }
};
const getVNodePlaceHolderLength = (node: VNode) => {
  return ((node as any)?.ctx?.attrs?.placeholder || '').length;
};
// 动态计算需要设置的宽度
const calculateTheDynamicWidth = (...nodes: VNode[]): number | undefined => {
  const children: VNode[] = [...nodes];
  let width: number | undefined;
  let current: VNode | undefined = children.shift();
  while (current) {
    width = getTypePlaceholderWidth(current);
    if (width !== undefined) break;
    if (current?.children) {
      children.push(current.children as any);
    }
    current = children.shift();
  }
  return width ?? 120;
};

const normalizedStyleWidth = (max: number, node: any) => {
  let style = node.props?.style || node.props?.staticStyle;
  let oriWidth = 0;

  if (!style) {
    if (!['ElFormItem', 'el-form-item'].includes(getVNodeTag(node))) {
      return oriWidth;
    }
    style = `width:${calculateTheDynamicWidth(node.children.default())}px;`;
    if (node.props) {
      node.props.style = style;
    } else {
      node.props = { style };
    }
  }
  let tmp: any;
  if (typeof style === 'object') {
    tmp = `width:${style.width}`;
  } else {
    tmp = style;
  }

  if (typeof tmp === 'string') {
    tmp = /width:\s*(\d+)(px|%)/.exec(tmp);
    if (!tmp) return oriWidth;
    if (tmp[2] === '%') {
      oriWidth = (Number(tmp[1]) / 100) * max;
    } else {
      oriWidth = Number(tmp[1]);
    }
  } else {
    oriWidth = Number(tmp);
  }
  return oriWidth;
};

const MJFormList = defineComponent({
  props: {
    showExport: {
      type: Boolean,
      default: () => false,
    },
    semiAngle: {
      type: Boolean,
      default: false,
    },
    disabledExport: {
      type: Boolean,
      default: () => false,
    },
  },
  emits: ['search', 'reset', 'export'],
  setup(props, context) {
    const filterAreaRef = ref<HTMLElement>();
    const defaultLineRef = ref<HTMLElement>();
    const actionAreaRef = ref<HTMLElement>();
    const clientWidth = ref(0);
    const defaultClientWidth = ref(0);
    const actionAreaWidth = ref(0);
    const isUnfold = ref(false);

    useResizeObserver(filterAreaRef, clientWidth);
    useResizeObserver(defaultLineRef, defaultClientWidth);
    useResizeObserver(actionAreaRef, actionAreaWidth);
    const getNodeKey = (index: number) => {
      return `key_${index++}`;
    };

    const onEnterKey = createEnterHandle(() => {
      context.emit('search');
    });

    return () => {
      const vue2Context = transformVue2Slots(context);
      let childNode: VNode[] = vue2Context.slots?.default?.() || ([] as any);
      childNode = childNode
        .flatMap((t: any) => (typeof t.type === 'symbol' ? t.children : t))
        .filter(t => !!t);

      // 第一行数据
      const filterLine: { widths: string; nodes: any[]; maxWidth: number }[] = [];
      const gapWidth = 12;

      // 容器有宽度时才进行计算
      if (clientWidth.value > 0) {
        let maxWidth = clientWidth.value - actionAreaWidth.value;
        let currentWidth = 0;
        let currentLine: { widths: string; nodes: any[]; maxWidth: number } = undefined as any;

        if (childNode.length > 0) {
          currentLine = { widths: '', nodes: [], maxWidth };
          filterLine.push(currentLine);
        }
        let index = 0;
        for (const node of childNode) {
          node.key = getNodeKey(index++);
          let nodeWidth = normalizedStyleWidth(maxWidth, node);
          if (nodeWidth === 0) {
            continue;
          }
          const renderNode: any = node;
          currentWidth += nodeWidth + gapWidth;
          currentLine.maxWidth = currentWidth;
          const wrapperRenderNode = renderNode;
          if (currentWidth > maxWidth) {
            maxWidth = defaultClientWidth.value;
            nodeWidth = normalizedStyleWidth(maxWidth, node);
            currentWidth = nodeWidth + gapWidth;
            currentLine = { widths: '', nodes: [], maxWidth };
            filterLine.push(currentLine);
          }
          currentLine.widths += `${nodeWidth}px `;
          currentLine.nodes.push(wrapperRenderNode);
        }
      } else {
        childNode.forEach((el, elIdx) => {
          el.key = getNodeKey(elIdx);
        });
      }
      const [firstLine, ...otherLine] = filterLine;

      let hasChildNode = false;
      if (firstLine && firstLine.nodes.length > 0) {
        hasChildNode = true;
      }
      const hasOtherBtn = vue2Context.slots.otherBtns?.() || props.showExport;

      const onButtonGroupClick = (key: string) => {
        switch (key) {
          case ButtonKeys.EXPAND:
          case ButtonKeys.RETRACT:
            isUnfold.value = !isUnfold.value;
            break;
          case ButtonKeys.SEARCH:
            context.emit('search');
            break;
          case ButtonKeys.RESET:
            context.emit('reset');
            break;
          default:
            break;
        }
      };

      const getBtnList = computed((): IBtnItemProps[] => {
        return [
          ...(otherLine.length > 0
            ? [
                {
                  key: isUnfold.value ? ButtonKeys.RETRACT : ButtonKeys.EXPAND,
                },
              ]
            : []),
          { key: ButtonKeys.SEARCH, type: 'primary' },
          { key: ButtonKeys.RESET },
        ];
      });
      return (
        <div
          class={`mjt-formlist-search-bar ${props.semiAngle ? 'semi-angle' : ''}`}
          onKeyup={onEnterKey}
        >
          <MJForm
            show-message={false}
            hide-required-asterisk={true}
            label-width="0"
            ref="formRef"
            nativeOn={{
              submit: (e: Event) => {
                e.preventDefault();
              },
            }}
          >
            <div class="mjt-formlist-form-item-container">
              <div
                class={`mjt-formlist-default-line ${hasOtherBtn ? 'two' : ''}`}
                ref={defaultLineRef}
              >
                <div class="mjt-formlist-filter-area" ref={filterAreaRef}>
                  <div
                    class="mjt-formlist-filter-content"
                    key="mjt-formlist-filter-content"
                    style={`grid-template-columns:${firstLine?.widths}; ${
                      !firstLine?.nodes?.length ? 'display: none' : ''
                    }`}
                  >
                    {firstLine?.nodes || vue2Context.slots?.default?.()}
                  </div>
                  <div
                    class="mjt-formlist-action-area"
                    key="mjt-formlist-action-area"
                    ref={actionAreaRef}
                  >
                    {vue2Context.slots.searchBtn ? (
                      vue2Context.slots.searchBtn?.()
                    ) : hasChildNode ? (
                      <div class="mjt-formlist-form-item-search-operation">
                        <ButtonGroup
                          onButtonGroupClick={onButtonGroupClick}
                          btnList={getBtnList.value}
                        />
                      </div>
                    ) : undefined}
                  </div>
                  <div class="mjt-formlist-action-fill" key="mjt-formlist-action-fill" />
                </div>

                {hasOtherBtn && (
                  <div class="mjt-formlist-expansion-zone">
                    {vue2Context.slots.otherBtns?.()}
                    {props.showExport && (
                      <ElButton
                        icon="ico-common-daochu-linear"
                        disabled={props.disabledExport}
                        onClick={() => context.emit('export')}
                      >
                        导出
                      </ElButton>
                    )}
                  </div>
                )}
              </div>
              {isUnfold.value &&
                otherLine.map(item => {
                  return (
                    <div
                      class="mjt-formlist-extend_line"
                      style={`grid-template-columns:${item.widths}`}
                    >
                      {item.nodes}
                    </div>
                  );
                })}
            </div>
          </MJForm>
        </div>
      );
    };
  },
});

export default MJFormList;
