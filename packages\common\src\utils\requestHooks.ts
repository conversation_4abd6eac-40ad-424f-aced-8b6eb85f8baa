import { reactive, Ref, ref, toRefs, UnwrapRef } from 'vue';
import { HttpResponse, IGPageQuery, ListResponse } from '../types/base/http';
import { ElMessage as Message } from 'element-plus';

interface AnyFunc {
  (...args: any[]): any;
}

type ReturnTypeReq<T> = T extends TFunc<Promise<HttpResponse<infer R>>> ? R : any;
type TransformFunc<Req extends AnyFunc> = (e: ReturnTypeReq<Req>) => any;
//type ReturnTypeReqPage<T> = T extends TFunc<Promise<ListResponse<infer R>>> ? R : any;

export interface RequestOption<Req extends AnyFunc, TF extends TransformFunc<Req>> {
  // 是否手动提交
  manual?: boolean;
  // 首次默认执行时,传递给service的参数
  defaultParams?: Parameters<Req>;
  /** 是否手动报错 */
  manualError?: boolean;
  // 请求成功后,返回的res.data对象
  onSuccess?: (data: ReturnTypeReq<Req>, oData: HttpResponse<ReturnTypeReq<Req>>) => void;
  // 请求出错时调用的函数
  onError?: (e: Error) => void;
  // 不管成功失败,调用的方法
  onFinally?: (params: Parameters<Req>, data?: ReturnTypeReq<Req>, e?: Error) => void;
  // 转换data数据
  transform?: TF;
  // 初始化返回数据
  defaultData?: any;
}

export interface RequestPageOption<
  Req extends AnyFunc,
  TF extends (e: ReturnTypePaginReq<Req>) => any,
> {
  // 是否手动提交
  manual?: boolean;
  // 首次默认执行时,传递给service的参数
  defaultParams?: Parameters<Req>;
  /** 是否手动报错 */
  manualError?: boolean;
  // 请求成功后,返回的res.data对象
  onSuccess?: (data: ReturnTypeReq<Req>, oData: HttpResponse<ReturnTypeReq<Req>>) => void;
  // 请求出错时调用的函数
  onError?: (e: Error) => void;
  // 不管成功失败,调用的方法
  onFinally?: (params: Parameters<Req>, data?: ReturnTypeReq<Req>, e?: Error) => void;
  // 转换data数据
  transform?: TF;
  // 初始化返回数据
  defaultData?: any;
}

type emptyFun = () => void;
type anyFunc = (...args: any) => any;
type TFunc<T> = (...args: any) => T;

type getTransFormType<TF extends anyFunc, Deft> = TF extends (...args: any[]) => infer R ? R : Deft;
// Ref<ReturnTypeReq<Req>>
type RequestResult<Req extends AnyFunc, TF extends anyFunc> = UnwrapRef<{
  name: 'useRequest';
  data?: Ref<getTransFormType<TF, ReturnTypeReq<Req>>>;
  params: Parameters<Req>[] | any;
  loading: boolean;
  error?: unknown;
  reload: emptyFun;
  runAsync: (...args: Parameters<Req>) => ReturnType<Req>;
}>;

type PaginationRequestResult<Req extends anyFunc, TF extends anyFunc> = UnwrapRef<{
  name: 'usePagination';
  data?: Ref<getTransFormType<TF, ReturnTypePaginReq<Req>[]>>;
  statistics?: any;
  params: Parameters<Req>[] | any;
  loading: boolean;
  error?: unknown;
  reload: emptyFun;
  runAsync: (...args: Parameters<Req>) => ReturnType<Req>;
}>;

type ReturnTypePaginReq<T extends anyFunc> = T extends TFunc<Promise<ListResponse<infer R>>>
  ? R
  : any;

export const useRequest = <Req extends AnyFunc, TF extends TransformFunc<Req>>(
  service: Req,
  options?: RequestOption<Req, TF>,
): RequestResult<Req, TF> => {
  const { manual, onError, onSuccess, defaultParams, transform, manualError, defaultData } =
    options || ({} as any);
  const data = ref<ReturnTypeReq<Req>>(defaultData);
  const error = ref<unknown>();
  const loading = ref(false);
  const params = ref<Parameters<Req>[]>([]);
  const _transform: any = transform;
  const reload = () => {
    run(...(params.value as any));
  };
  const run = async (...args: any[]) => {
    return Promise.resolve(args)
      .then(() => {
        loading.value = true;
      })
      .then(() => {
        if (args.length === 0 && defaultParams && defaultParams.length > 0) {
          return defaultParams;
        }
        return args;
      })
      .then((args: any) => {
        params.value = args;
        return args;
      })
      .then(args => service(...args))
      .then((res: any) => {
        loading.value = false;
        if (!res.data.success) throw res.data;
        if (_transform) {
          data.value = _transform(res.data.data) as any;
        } else {
          data.value = res.data.data;
        }
        onSuccess && onSuccess(data.value as any, res.data);
        return res;
      })
      .then(res => {
        loading.value = false;
        return res;
      })
      .catch((e: any) => {
        loading.value = false;
        error.value = e.message;
        if (e.stopPropagation) throw e;
        onError && onError(e);
        if (!manualError) {
          if (e?.message) {
            if (e?.error_level === 'warning') {
              Message.warning(e?.message);
            } else if (e?.error_level === 'info') {
              Message.info(e?.message);
            } else {
              Message.error(e?.message);
            }
          }
        }
        throw e;
      });
  };
  const runAsync = run;
  if (!manual) {
    run();
  }

  return reactive({
    name: 'useRequest',
    data,
    error,
    loading,
    reload,
    run,
    runAsync: runAsync as any,
    params,
  }) as any;
};

type RequestOptionPagination<
  Req extends (page: IGPageQuery, ...args: any) => any,
  TF extends anyFunc,
> = RequestPageOption<Req, TF> & {
  defaultPageSize?: number;
  defaultParams?: Parameters<Req>;
  onSuccess?: (data: ReturnTypeReq<Req>, oData: ListResponse<ReturnTypeReq<Req>>) => void;
};

interface Pagination {
  total: number;
  // num: number;
  page_num: number;
  page_size: number;
  page_sizes: number[];
  layout: string;
  onSizeChange: (num: number) => void;
  onCurrentChange: (num: number) => void;
  reQuery: (...args: any[]) => void;
}

type IServiceReqBase<Req extends anyFunc> = (
  page: IGPageQuery,
  ...args: any
) => Promise<ListResponse<ReturnTypePaginReq<Req>>>;

type ParametersFilterFirst<T extends (...args: any) => any> = T extends (
  first: any,
  ...args: infer P
) => any
  ? P
  : never;

/**
 分页查询V2版本
 请求参数, 不在需要第一个参数传递分页
 */
export const usePaginationV2 = <Req extends IServiceReqBase<Req>, TF extends TransformFunc<Req>>(
  service: Req,
  option: Omit<RequestOptionPagination<Req, TF>, 'defaultParams'> & {
    defaultParams?: ParametersFilterFirst<Req>;
  } = {},
): Omit<PaginationRequestResult<Req, TF>, 'runAsync'> & {
  pagination: Pagination;
  runAsync: (...args: ParametersFilterFirst<Req>) => ReturnType<Req>;
} => {
  const { defaultPageSize = 20, transform, defaultData, ...rest } = option;
  const data = ref<ReturnTypeReq<Req>>(defaultData);
  const pagination = reactive({
    total: 0,
    page_num: 1,
    page_size: defaultPageSize,
    page_sizes: [10, 20, 50, 100],
    layout: 'total,sizes, prev, pager, next, jumper',
    onCurrentChange: (num: number) => {
      pagination.page_num = num;
      pagination.refresh();
    },
    onSizeChange: (size: number) => {
      pagination.page_size = size;
      pagination.page_num = 1;
      pagination.refresh();
    },
    refresh: () => {
      return result.reload();
    },
    reQuery(...args: any[]) {
      pagination.page_num = 1;
      return result.runAsync(...(args as any));
    },
  });
  const result = useRequest(
    ((...args: any[]) => {
      return service(
        {
          page_num: pagination.page_num,
          page_size: pagination.page_size,
        },
        ...args,
      );
    }) as any,
    {
      ...rest,
      onSuccess(_data: any, res: any) {
        if (transform) {
          data.value = transform(_data);
        } else {
          data.value = _data.data;
        }
        pagination.total = _data.total;
        if ((result.params[0] as IGPageQuery)?.page_num !== undefined) {
          pagination.page_num = (result.params[0] as IGPageQuery)?.page_num;
        }

        option.onSuccess && option.onSuccess(data.value as any, res);
      },
    } as any,
  );
  result.runAsync = (...args) => {
    pagination.page_num = 1;
    return (result as any).run(...args);
  };
  const { data: _, ...restResult } = toRefs(result);

  return reactive({
    ...restResult,
    data,
    pagination,
    name: 'usePagination',
  }) as any;
};

export const usePagination = <
  Req extends (page: IGPageQuery, ...args: any) => Promise<ListResponse<ReturnTypePaginReq<Req>>>,
  TF extends TransformFunc<Req>,
>(
  service: Req,
  option: RequestOptionPagination<Req, TF> = {},
): PaginationRequestResult<Req, TF> & { pagination: Pagination } => {
  const { defaultPageSize = 20, defaultParams = [], transform, ...rest } = option;
  const data = ref<ReturnTypeReq<Req>>();
  /** 合计 */
  const statistics = ref<any>();
  const pagination = reactive({
    total: 0,
    page_num: 1,
    // num: defaultPageSize,
    page_size: defaultPageSize,
    page_sizes: [10, 20, 50, 100],
    layout: 'total, prev, pager, next, sizes, jumper',
    onCurrentChange: (num: number) => {
      const params: any[] = [...result.params];
      if (params[0]) {
        params[0].page_num = num;
      } else {
        params[0] = {
          // num: defaultPageSize,
          page_size: defaultPageSize,
          page_num: num,
        };
      }
      result.runAsync(...(params as any)).then(() => {
        pagination.page_num = num;
      });
    },
    onSizeChange: (size: number) => {
      const params: any[] = [...result.params];
      if (params[0]) {
        params[0].page_size = size;
        // params[0].num = size;
      } else {
        params[0] = {
          // num: size,
          page_size: size,
          page_num: 1,
        };
      }
      result.runAsync(...(params as any)).then(() => {
        pagination.page_size = size;
      });
    },
    onCurrentSizeChange: () => {
      const params: any[] = [...result.params];
      if (params[0]) {
        params[0].page_num = 1;
        params[0].page_size = defaultPageSize;
        // params[0].num = defaultPageSize;
      } else {
        params[0] = {
          // num: defaultPageSize,
          page_size: defaultPageSize,
          page_num: 1,
        };
      }
      result.runAsync(...(params as any)).then(() => {
        pagination.page_num = 1;
        pagination.page_size = defaultPageSize;
      });
    },
    refresh: () => {
      return pagination.onCurrentSizeChange();
    },
    reQuery(...args: any[]) {
      const params: any = [];
      if (result.params[0]) {
        params[0] = result.params[0];
        params[0].page_num = 1;
        pagination.page_num = 1;
      } else {
        params[0] = {
          page_size: defaultPageSize,
          page_num: 1,
        };
        pagination.page_num = 1;
      }
      params.push(...args);
      return result.runAsync(...params);
    },
  });
  const [defaultPage = { page_size: defaultPageSize, page_num: 1 }, ...restInit] = defaultParams;
  const result = useRequest(service, {
    defaultParams: [defaultPage, ...restInit] as any,
    ...rest,
    onSuccess(_data: any, res: any) {
      if (transform) {
        data.value = transform(_data);
      } else {
        data.value = _data.data;
      }
      /** 合计 */
      statistics.value = _data.statistics;
      pagination.total = _data.total;
      if ((result.params[0] as IGPageQuery)?.page_num !== undefined) {
        pagination.page_num = (result.params[0] as IGPageQuery)?.page_num;
      }
      option.onSuccess && option.onSuccess(data.value as any, res);
    },
  });
  const { data: _, ...restResult } = toRefs(result);
  return reactive({
    ...restResult,
    data,
    statistics,
    pagination,
    name: 'usePagination',
  }) as any;
};

export type ListTemplateConfig = {
  service?: ReturnType<typeof usePaginationV2>;
};
export const useListTemplate = (options: ListTemplateConfig) => {
  const query = () => {};
  return {
    query,
    options,
  };
};
