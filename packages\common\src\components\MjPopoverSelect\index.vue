<script src="./index.tsx"></script>
<style scoped lang="scss">
  .gm-popover-select-container {
    :deep(.el-tooltip__trigger) {
      cursor: pointer;
    }
    .popover-select__popover__inner {
    }
    .popover-select__popover__inner-filter-input {
      padding: 0 12px 4px;
    }
    :deep(.gm-popover-select-popover) {
      --el-popover-padding: 12px 0 6px;
      .popover-select__popover-list {
        max-height: 300px;
        overflow: auto;
        background: white;
        .popover-select__popover-list-item {
          cursor: pointer;
          padding: 9px 32px 9px 20px;
          &.is-active {
            color: #2877ff;
          }
        }
      }
    }
  }
</style>

<!--<style lang="scss">-->
<!--  .gm-popover-select-container {-->
<!--    .el-tooltip__trigger {-->
<!--      cursor: pointer;-->
<!--    }-->
<!--    .gm-popover-select-popover {-->
<!--      &#45;&#45;el-popover-padding: 6px 0;-->
<!--    }-->
<!--  }-->
<!--</style>-->
