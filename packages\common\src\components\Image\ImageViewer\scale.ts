import { Ref } from 'vue';
const defaultMultiples = 1;
const defaultTransX = 0;
const defaultTransY = 0;
const defaultRotate = 0;

export const useScale = (el: Ref<any | undefined>) => {
  // 图片放大 缩小
  let multiples = defaultMultiples;
  // 图片拖拽后的偏移
  let newTransX = defaultTransX;
  let newTransY = defaultTransY;
  // 图片旋转角度
  let rotate = defaultRotate;
  // 鼠标相对图片的位置
  let disX = 0;
  let disY = 0;
  /**
   * 鼠标摁下onmousedown，触发dragImg事件，记录鼠标和图片的位置，从而计算鼠标相对图片的坐标
   *     鼠标相对图片的坐标：鼠标相对浏览器（有效区域）左侧的位置-图片的translateX和translateY的值
   *
   * 监听鼠标的移动onmousemove，当鼠标移动时被拖拽的元素跟随鼠标移动，根据鼠标当前坐标和鼠标相对图片的距离计算出图片当前的坐标
   *     图片当前的translateX和translateY的值：鼠标当前相对浏览器（有效区域）位置-鼠标相对图片的距离 计算出图片移动的距离
   *
   * 是否存在边界限制 暂无
   */
  // 改变元素位置的方式有：定位、translate偏移

  function getTarget() {
    return el.value?.$el || el.value;
  }

  function dragImg($event: DragEvent) {
    const e = $event || event;
    e.preventDefault();
    // let dragEl = document.querySelector('#bigImg');
    getTransInfo(e);
    const moveHandler = (e: MouseEvent) => {
      newTransX = e.clientX - disX;
      newTransY = e.clientY - disY;
      setScaleAndPosition(multiples, newTransX, newTransY, rotate);
    };
    const mouseUphandler = () => {
      document.onmousemove = null;
      document.removeEventListener('mousemove', moveHandler);
      document.removeEventListener('mouseup', mouseUphandler);
    };
    document.addEventListener('mousemove', moveHandler);
    document.addEventListener('mouseup', mouseUphandler);
  }

  function getTransInfo(e: DragEvent) {
    const target = getTarget();
    if (!target) return;
    // 获取元素当前的宽高
    // let dragEl = document.querySelector('#bigImg');
    // 获取元素当前的偏移量
    const marginLeft = getComputedStyle(target).marginLeft;
    const marginTop = getComputedStyle(target).marginTop;
    // 获取translateX
    const transX = parseInt(marginLeft, 10);
    // 获取translateY
    const transY = parseInt(marginTop, 10);
    // 记录鼠标的起始位置
    //鼠标指针相对于浏览器页面（或当前窗口）的水平坐标
    const startX = e.clientX;
    //鼠标指针相对于浏览器页面（或当前窗口）的y坐标
    const startY = e.clientY;

    disX = startX - transX; //鼠标相对于图片左侧的水平距离
    disY = startY - transY;
  }

  // 鼠标滚轮缩放
  const scrollFunc = function (e: any) {
    e = e || window.event;
    // 火狐下没有wheelDelta，用detail代替，由于detail值的正负和wheelDelta相反，所以取反
    e.delta = e.wheelDelta || -e.detail;
    // e.preventDefault();
    if (e.delta > 0) {
      //当滑轮向上滚动时
      biggerImg();
    }
    if (e.delta < 0) {
      //当滑轮向下滚动时
      smallerImg();
    }
  };

  function smallerImg(step: number = 0.1) {
    multiples = Number(multiples - step);
    if (multiples > 0.1) {
      setScaleAndPosition(multiples, newTransX, newTransY, rotate);
    } else {
      multiples = 0.1;
    }
  }

  function biggerImg(step: number = 0.1) {
    multiples = Number(multiples + step);
    if (multiples < 20.1) {
      setScaleAndPosition(multiples, newTransX, newTransY, rotate);
    } else {
      multiples = 20;
    }
  }
  function setScaleAndPosition(mul: number, x: number, y: number, rotate: number) {
    const target = getTarget();
    // 数字相加一定要注意字符串，一定要防止数字变成字符串数字，请刻入DNA
    if (!target) return;
    target.style.transform = `scale(${mul}) rotate(${rotate}deg)`;
    target.style.marginLeft = `${x}px`;
    target.style.marginTop = `${y}px`;
  }
  function startRotate(deg: number) {
    rotate = Number(rotate) + deg;
    rotate = Math.abs(rotate) === 360 ? defaultRotate : rotate;
    setScaleAndPosition(multiples, newTransX, newTransY, rotate);
  }
  function scaleUp() {
    biggerImg(0.2);
  }
  function scaleDown() {
    smallerImg(0.2);
  }
  function rotateLeft() {
    startRotate(-90);
  }
  function rotateRight() {
    startRotate(90);
  }
  function resetScale() {
    multiples = defaultMultiples;
    newTransX = defaultTransX;
    newTransY = defaultTransY;
    rotate = defaultRotate;
    setScaleAndPosition(multiples, newTransX, newTransY, rotate);
  }
  return {
    scrollFunc,
    dragImg,
    resetScale,
    scaleDown,
    scaleUp,
    rotateLeft,
    rotateRight,
  };
};
