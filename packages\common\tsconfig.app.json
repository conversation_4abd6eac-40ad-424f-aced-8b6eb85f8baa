{
  "compilerOptions": {
    "target": "ESNext",
    "useDefineForClassFields": true,
    "module": "ESNext",
    "lib": ["ESNext", "DOM", "DOM.Iterable"],
    "skipLibCheck": true,
    "baseUrl": ".",

    /* Bundler mode */
    "moduleResolution": "bundler",
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "jsx": "preserve",
    "noImplicitAny": false,
    "paths": {
      "@common/*": ["src/*"]
    },

    /* Linting */
    "strict": true
  },
  "include": ["./*.ts", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"]
}
