import { computed, defineComponent, h, PropType, ref } from 'vue';
import navBg from '../../assets/icon-nav-bg.png';
import { RouterLink, useRoute, useRouter } from 'vue-router';
import { useHeaderMenus } from '@common/use/header';
import { Expand, Fold } from '@element-plus/icons-vue';
import logo from '../../assets/icon-logo.png';

export type HeaderMenuType = {
  label: string; // 没有设置name，不会被设置在面包屑上
  name?: string /** 在面包屑上显示的名称，优先级高于label */;
  breadcrumbName?: string;
  children?: HeaderMenuType[];
  hidden?: boolean;
  rights?: number[];
  parent?: HeaderMenuType;
};

export default defineComponent({
  name: 'sidebar',
  components: {
    RouterLink,
  },
  props: {
    menus: {
      type: Array as PropType<HeaderMenuType[]>,
      required: true,
    },
    rightCodes: {
      type: Array as PropType<number[]>,
    },
    ignoreRight: {
      type: Boolean,
      default: () => true,
    },
  },
  setup(props, ctx) {
    const router = useRouter();
    const route = useRoute();
    const { transformHeaderMenus, findActiveMenu } = useHeaderMenus();

    const menuList = computed(() => {
      return transformHeaderMenus(props.menus || [], props.rightCodes || [], props.ignoreRight);
    });

    const activeMenu = computed(() => {
      // const value = findActiveMenu(menuList.value);
      const value = findActiveMenu(menuList.value, route.path); // 传入当前路由路径
      return value;
    });
    console.log('🚀 ~ activeMenu ~ activeMenu:', activeMenu);

    function getDisplayMenus(menus?: any[]) {
      return menus?.filter(el => !el.hidden && el.hasAuth);
    }

    const isCollapse = ref(false);
    const handleClick = () => {
      isCollapse.value = !isCollapse.value;
    };

    return () => (
      <div class="layout-left-menu-box">
        <div class="flex-column-100">
          <el-scrollbar class="layout-left-menu-scroll">
            <el-menu
              defaultActive={activeMenu.value?.path}
              collapse={isCollapse.value}
              unique-opened
              scroll
              router
              menu-trigger="click"
              class={['flex-column-100 layout-left-menu', 'is-collapsed']}
            >
              <section class="logo-section">
                <div class="main-title">
                  <div class={['logo', isCollapse.value && 'clear-margin']} onClick={handleClick}>
                    <img src={logo} alt="image" />
                  </div>
                  {!isCollapse.value && <div>财务管理系统</div>}
                </div>
                {!isCollapse.value && <div class="subtitle">Financial Management System</div>}
              </section>

              <div class="flex-column-100">
                {getDisplayMenus(menuList.value)?.map(menu => {
                  return menu.children.length ? (
                    <el-sub-menu key={menu.path} index={menu.path}>
                      {{
                        title: () => (
                          <div>
                            <el-icon>{h(menu.icon)} </el-icon>
                            {!isCollapse.value && <span>{menu.label}</span>}
                          </div>
                        ),
                        default: () => (
                          <>
                            {getDisplayMenus(menu.children)?.map(child => {
                              return (
                                <el-menu-item index={child.path}>
                                  <span>{child.label}</span>
                                </el-menu-item>
                              );
                            })}
                          </>
                        ),
                      }}
                    </el-sub-menu>
                  ) : (
                    <el-menu-item index={menu.path} class="sub-menu">
                      <el-icon>{h(menu.icon)}</el-icon>
                      {!isCollapse.value && <span>{menu.label}</span>}
                    </el-menu-item>
                  );
                })}
              </div>
              <el-icon size="20" color="#AFB0BD" onClick={handleClick}>
                {isCollapse.value ? <Expand /> : <Fold />}
              </el-icon>
            </el-menu>
          </el-scrollbar>
        </div>
      </div>
    );
  },
});
function useHeaderConfig() {
  throw new Error('Function not implemented.');
}
