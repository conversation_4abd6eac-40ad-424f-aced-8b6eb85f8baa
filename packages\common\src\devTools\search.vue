<script src="./search.tsx"></script>
<style lang="scss" scoped>
.search-box {
  position: fixed;
  margin: auto;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9000;
  display: flex;
  flex-direction: column;
  align-items: center;

  .search-body {
    overflow: hidden;
    border-radius: 10px;
    border: solid 1px #f1f1f1;
    box-shadow: 4px 4px 17px #00000030;
    background-color: #f7f7f7d0;
    backdrop-filter: blur(10px);
    margin-top: 20vh;
    max-width: 50vw;
    min-width: 20vw;
    width: 600px;

    .st-input {
      height: 50px;
      display: flex;
      align-items: center;

      .el-icon {
        margin: 0 10px;
      }
      svg {
        color: var(--text-third-color);
      }

      input {
        width: 100%;
        height: 100%;
        outline: none;
        margin: 0;
        border: none;
        padding: 0 10px 0 0;
        line-height: 50px;
        font-size: 20px;
        background-color: transparent;
      }
    }

    .search-list {
      display: flex;
      flex-direction: column;
      border-top: solid 1px var(--border-line-color);
      padding: 10px 10px;
      font-size: 16px;
      max-height: 300px;
      overflow: auto;

      .search-list-item {
        height: 30px;
        overflow: hidden;
        display: flex;
        align-items: center;
        column-gap: 10px;
        cursor: pointer;
        padding: 0 10px;
        flex: 0 0 auto;

        &.active {
          background-color: var(--theme-color);
          color: #ffffff;
          border-radius: 4px;

          svg {
            color: #ffffff;
          }
        }

        svg {
          color: #7d7d7d;
        }

        .disabled {
          //color: #7d7d7d;
        }

        .bar {
          color: var(--text-four-color);
        }
      }
    }
  }
}
</style>
