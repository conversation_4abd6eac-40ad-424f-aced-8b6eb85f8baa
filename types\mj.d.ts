import { VueComponent, VueFormComponent } from './common';
import { OptionType } from '@common/types/base/index';
declare global {
  namespace JSX {
    interface IntrinsicElements {
      'mj-upload': VueComponent<{
        'show-file-list'?: boolean;
        action?: string;
        'list-type'?: string;
        data?: {
          type: 'any' | string;
          [key: string]: any;
        };
        beforeUpload?: (file: File) => boolean;
        showList?: boolean;
        success?: (res: { success: boolean; message: string; data: any }) => void;
      }>;
      'mj-upload-file-list': VueComponent<{}>;
      'mj-input': VueFormComponent<{
        /**
         * 数据类型
         * text 文本
         * number 数字
         * Integer 整数
         */
        dataType?: 'text' | 'number' | 'Integer';
        maxlength?: number;
        autocomplete?: string | 'off';

        /**
         * 小数位数限制
         */
        decimalLimit?: number;
      }>;
      'mj-select': VueFormComponent<{
        options?: OptionType<unknown>[];
        onChange?: (value: unknown) => void;
        filterable?: boolean;
        remote?: boolean;
        clearable?: boolean;
        'reserve-keyword'?: boolean;
        'remote-method'?: (v: string) => void;
      }>;
    }
  }
}
