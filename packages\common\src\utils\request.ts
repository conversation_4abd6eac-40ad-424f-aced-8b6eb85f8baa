/**
 * 请求封装
 * <AUTHOR> <<EMAIL>>
 * @since   2020-10-21 19:36:32
 */
// import { getToken, getCode, removeToken } from "@/utils/token";
import axios, { AxiosRequestConfig } from 'axios';
import { requestEventEmitter } from './requestEventHandler';

//接口统一域名
const instance = axios.create({
  // baseURL: baseUrl,
  baseURL: '',
  timeout: 60000,
  validateStatus: status => status < 500,
});

// 请求拦截
instance.interceptors.request.use(
  config => {
    // config.headers = {
    //   ...config.headers,
    //   // Authorization: getToken(),
    //   Authorization: tempToken,
    //   // Actcode: getCode(),
    // } as any;
    // config.paramsSerializer = (params) => {
    //   return JSON.stringify(params);
    // };
    requestEventEmitter.emit('RequestFullfilled', config);
    return config;
  },
  error => Promise.reject(error),
);

instance.interceptors.response.use(
  response => {
    // 4种需要重新登录的场景
    // if ([100, 101, 104, 105].includes(response.data.error_code)) {
    //   if (window.location.pathname !== "/login") {
    //     Message.error({
    //       message: "会话超时，请重新登录",
    //       duration: 1500,
    //       onClose: () => {
    //         // removeToken();

    //         router.push({
    //           name: "Login",
    //           query: {
    //             redirect: window.location.pathname + window.location.search,
    //           },
    //         });
    //       },
    //     });
    //     throw new Error("会话超时，请重新登录");
    //   }
    // }
    requestEventEmitter.emit('ResponseFullfilled', response);
    return response;
  },
  error => {
    // let msg = "";
    // if (error.message.includes("timeout")) {
    //   msg = "请求超时了,请重新尝试";
    // } else {
    //   msg = error.message ?? "服务器出错了";
    // }

    // Message.error(msg);
    requestEventEmitter.emit('ResponseRejected', error);
    return Promise.reject(error);
  },
);

/**
 * Get 请求
 * <AUTHOR> <<EMAIL>>
 * @since  2020-06-10 15:32:24
 */
export const Get = <T = any>(url: string, options?: AxiosRequestConfig) =>
  instance.get<T>(url, options);

/**
 * Post 请求
 * <AUTHOR> <<EMAIL>>
 * @since  2020-06-10 15:33:20
 */
export const Post = <T = any>(
  url: string,
  data?: Record<string, any>,
  options?: AxiosRequestConfig,
) => instance.post<T>(url, data, options);

/**
 * Patch 请求
 * <AUTHOR> <<EMAIL>>
 * @since  2020-06-10 15:33:22
 */
export const Patch = <T = any>(
  url: string,
  data?: Record<string, any>,
  options?: AxiosRequestConfig,
) => instance.patch<T>(url, data, options);

/**
 * Put 请求
 * <AUTHOR>
 * @since  2020-06-17 16:13:45
 */
export const Put = <T = any>(
  url: string,
  data?: Record<string, any>,
  options?: AxiosRequestConfig,
) => instance.put<T>(url, data, options);

/**
 * Delete 请求
 * <AUTHOR> <<EMAIL>>
 * @since  2020-06-10 15:33:23
 */
export const Delete = <T = any>(url: string, options?: AxiosRequestConfig) =>
  instance.delete<T>(url, options);

export const request = instance;
export default instance;
