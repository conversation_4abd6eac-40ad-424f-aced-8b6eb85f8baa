import { EnumUtil, EnumValue } from 'common';

export class clearingStatus extends EnumUtil {
  static pendingReview = EnumValue(1, '待复核');
  static execut = EnumValue(2, '执行中');
  static complete = EnumValue(3, '完成');
  static exception = EnumValue(4, '执行异常');
  static failure = EnumValue(5, '复核失败');
}

export class clearingDetailsStatus extends EnumUtil {
  static toBeExecut = EnumValue(2, '待执行');
  // static process = EnumValue(2, '处理中');
  static success = EnumValue(3, '成功');
  static fail = EnumValue(4, '失败');
}
