import { useRouter } from 'vue-router';
import { defineComponent, onUpdated, ref } from 'vue';
import './index.scss';
type ComponentValue<T = string | number> = string | T;

/**
 * 选项卡数据
 * <AUTHOR> <<EMAIL>>
 * @since   2020-09-17 20:57:58
 * @prop {string} label
 * @prop {ComponentValue} value
 */
interface Tab<T = ComponentValue> {
  label: string;
  value: T;
  activeList?: string[];
}

export interface BmTabsOptions {
  tabs?: Tab<string | string[]>[] | null;
  semi?: boolean;
  defaultTab?: string | null | undefined;
  /** Tab类型 , router 为 name跳转, tab 为query跳转 nothing 什么都不做 */
  type?: 'router' | 'tab' | 'nothing';
  /** 替换内置 tab 组件*/
  component?: string | any;
  /** 跳转是否携带参数 **/
  query?: boolean | Record<string, any>;
  /** 跳转是否携带参数 **/
  params?: boolean | Record<string, any>;
  /** 传递给tab组件的Props参数 */
  inProps?: any;
}

const TabsComponent = defineComponent({
  props: ['tabs', 'modelValue', 'semi'],
  emits: ['update:modelValue', 'change'],
  setup(props, ctx) {
    return () => (
      <div class={`mj-tabs ${props.semi ? 'semi-angle' : ''}`}>
        <div class="mj-tabs-header">
          {props.tabs.map(item => {
            return (
              <div
                onClick={() => {
                  ctx.emit('update:modelValue', item.value);
                  ctx.emit('change', item);
                }}
                class={`mj-tabs-item ${
                  item.value === props.modelValue || item.activeList?.includes(props.modelValue)
                    ? 'active'
                    : ''
                }`}
              >
                {item.label}
              </div>
            );
          })}
        </div>
        {ctx.slots.default?.()}
      </div>
    );
  },
});

export const useMjTabs = (options: BmTabsOptions = {}) => {
  const { type = 'tab', tabs = [], defaultTab, query, params, semi, inProps } = options;
  const router = useRouter();
  const _tabs = ref<Tab<string>[]>(tabs as Tab<string>[]);
  const _defaultTab = ref(defaultTab);
  if (!defaultTab) {
    if (type === 'tab' && router.currentRoute.value.query.tab)
      _defaultTab.value = router.currentRoute.value.query.tab as string;
    else if (type === 'router') _defaultTab.value = router.currentRoute.value?.name as string;
    else _defaultTab.value = tabs?.[0].value;
  }
  onUpdated(() => {
    if (type === 'router' && router.currentRoute.value?.name !== _defaultTab?.value) {
      if (_tabs.value?.some(t => t.value === (router.currentRoute.value.name as string))) {
        updateChecked(router.currentRoute.value.name as string);
      }
    }
  });
  const updateTabs = (tabs: Tab<string>[]) => {
    _tabs.value = tabs;
  };
  const updateChecked = (value: string) => {
    _defaultTab.value = value;
  };
  const componentProps = inProps ? { props: inProps } : {};
  const component = defineComponent({
    name: 'MjTabs',
    render() {
      return (
        <TabsComponent
          tabs={_tabs.value}
          v-model={_defaultTab.value}
          onChange={(value: Tab<string>) => {
            this.$emit('change', value);
            if (type === 'tab') {
              router.push({
                name: router.currentRoute.value.name as string,
                query: {
                  tab: value.value,
                },
              });
            } else if (type === 'router') {
              router.push({
                name: value.value,
                query: query ? router.currentRoute.value.query : undefined,
                params: params ? router.currentRoute.value.params : undefined,
              });
            }
          }}
          semi={semi}
          {...componentProps}
        >
          {this.$slots.default}
        </TabsComponent>
      );
    },
  });

  return {
    component,
    updateTabs,
    updateChecked,
    get checkedTab() {
      return _defaultTab.value as string;
    },
  };
};

export default TabsComponent;
