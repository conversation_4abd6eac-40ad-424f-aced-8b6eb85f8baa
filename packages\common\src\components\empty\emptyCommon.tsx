import EmptyImage from '../../assets/icon-empty.png';
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'emptyCommon',
  props: {
    /** 无数据的时候显示文字内容 */
    detailText: {
      default: '暂无数据',
      type: String,
      required: false,
    },
    /** 图片宽度 */
    imgWidth: {
      required: false,
      type: Number,
      default: 215,
    },
    /** 图片高度 */
    imgHeight: {
      required: false,
      type: Number,
      default: 128,
    },
  },
  data() {
    return {
      imgWidthNum: 215,
      imgHeightNum: 128,
      EmptyImage,
    };
  },
  computed: {
    detailstr() {
      return this.detailText ? this.detailText : '暂无数据';
    },
  },
  created() {
    this.imgWidthNum = this.imgWidth ? this.imgWidth : this.imgWidthNum;
    this.imgHeightNum = this.imgHeight ? this.imgHeight : this.imgHeightNum;
  },
});
