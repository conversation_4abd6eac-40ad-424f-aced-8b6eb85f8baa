import { createApp } from 'vue';
import 'common/src/styles/variable.scss';
import './style.scss';
import router from '@/router';
import { createPinia } from 'pinia';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';
import App from './App.vue';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import { CommonUI } from 'common';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import './utils/request';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
import { installDebugger } from 'common/src/devTools';
import { useHeaderConfig } from '@/router/headerCnfig';

// 这里处理全局Promise未被处理的Catch, 阻止继续冒泡, 在控制台输出错误信息
// window.addEventListener('unhandledrejection', e => {
//   e.stopPropagation();
//   e.preventDefault();
//   e.promise.catch(error => {
//     // throw error;
//   });
//   return true;
// });
const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);
const app = createApp(App);

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}
app
  .use(pinia)
  .use(router)
  .use(ElementPlus, {
    locale: zhCn,
  })
  .use(CommonUI)
  .mount('#app');
app.config.errorHandler = (err, vm, info) => {
  // err: 错误对象
  // vm: 发生错误的组件实例
  // info: Vue特定的错误信息，如"render function"

  console.error('[Global Error Handler]', err, '\n', info);

  // 可以在这里添加错误上报逻辑
  // reportErrorToServer(err);

  // 或者显示全局错误提示
  // showGlobalErrorToast(err.message);
};
setTimeout(() => installDebugger(useHeaderConfig()));
