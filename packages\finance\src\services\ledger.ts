import { Delete, Get, ObjectFilterEmpty, Patch, Post } from 'common';
import { HttpResponse, IGPageQuery, ListResponse } from 'common/src/types/base/http';

/**
 * 查询品牌台账明细
 */

export const query_brand_ledger_list = async (
  pager: IGPageQuery,
  payload: any,
): Promise<ListResponse<any>> =>
  Get('/api/clearing/settlement/ledger/page', {
    params: {
      ...ObjectFilterEmpty({
        ...pager,
        ...(payload || {}),
      }),
    },
  });

/**
 * 品牌台账余额查询
 */
export const brand_ledger_balance_query = async (): Promise<ListResponse<any>> =>
  Get('/api/clearing/settlement/ledger/balance_amount');

//子台账开户流程>>>>>
/**
 * 发送校验短信
 */
export const get_verify_sms = async (mobile, payload) =>
  Post(
    '/api/join/verify_sms',
    ObjectFilterEmpty({
      mobile,
      ...ObjectFilterEmpty(payload || {}),
    }),
  );
/**
 * 查询当前代理商对应的子台账信息
 */
export const account = async () => {
  return Get('/api/clearing/account/');
};
/**
 * 创建代理商对应的子台账信息
 */
export const clearing_account = async payload =>
  Post(
    '/api/clearing/account/',
    ObjectFilterEmpty({
      ...ObjectFilterEmpty(payload || {}),
    }),
  );

/**
 * 绑定非平安对公银行卡到当前代理商的子台账
 */
export const bind_verify = async payload =>
  Post(
    '/api/clearing/account/bind_verify',
    ObjectFilterEmpty({
      ...ObjectFilterEmpty(payload || {}),
    }),
  );

/**
 *
绑定非平安对公银行卡到当前代理商的子台账 ✅
 */
export const bind_bank_card = async payload =>
  Post(
    '/api/clearing/account/account/bind_bank_card',
    ObjectFilterEmpty({
      ...ObjectFilterEmpty(payload || {}),
    }),
  );

/**
 *
绑定平安对公银行卡到当前代理商的子台账 ✅ ✅
 */
export const bind_pingan_bank_card = async payload =>
  Post(
    '/api/clearing/account/bind_pingan_bank_card',
    ObjectFilterEmpty({
      ...ObjectFilterEmpty(payload || {}),
    }),
  );

/**
 *
对公绑卡 协议上送用户行为接口 同意协议 ✅ ✅
 */
export const terms_agreement = async () => Post('/api/clearing/account/terms_agreement');

/**
 * 获取所有银行列表
 */
export const banks = async payload => {
  return Get(
    '/api/clearing/bank/',
    ObjectFilterEmpty({
      ...ObjectFilterEmpty(payload || {}),
    }),
  );
};
//子台账开户流程<<<<<<<<<

/**
 * 提现获取验证码
 */

export const get_verification_code = async (payload: any): Promise<HttpResponse<any>> =>
  Post('/api/clearing/account/account/withdraw_verify_code', ObjectFilterEmpty(payload));

/**
 * 提现
 */

export const withdrawal = async (payload: any): Promise<HttpResponse<any>> =>
  Post('/api/clearing/account/account/withdraw', ObjectFilterEmpty(payload));
