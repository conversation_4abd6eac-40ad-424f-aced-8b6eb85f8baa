import { HeaderMenuType } from '@common/components/header';
import { useRouter } from 'vue-router';
import indexRouter from '@/router/index';

export function checkHasAuth(rights: number[], rightCodes: number[], ignoreRight: boolean) {
  if (ignoreRight) return true;
  if (!rights?.length) return true;
  return !!rights.find(el => rightCodes?.includes(el));
}

export const useHeaderMenus = () => {
  let router: any = indexRouter;
  if (!router) {
    router = useRouter();
  }

  const transFormMenus = (menus: HeaderMenuType[], rightCodes: number[], ignoreRight: boolean) => {
    if (!router) {
      router = useRouter();
    }

    return menus.map(el => {
      let tempRoute;
      try {
        tempRoute =
          el.name &&
          !el.hidden &&
          (router?.resolve({
            name: el.name,
          }) as any);
      } catch (e) {
        console.error('error-menu: ', el, e);
      }
      return {
        ...el,
        label: el.label,
        path: tempRoute && tempRoute.path,
        hasAuth: checkHasAuth(tempRoute?.meta?.rights, rightCodes, ignoreRight),
        rights: tempRoute?.meta?.rights,
        children: transFormMenus(el.children || [], rightCodes, ignoreRight),
      };
    });
  };
  const transFindHeaderMenuName = (
    headerMenus: HeaderMenuType[],
    rightCodes: number[],
    ignoreRight: boolean,
  ) => {
    const tempMenus = transFormMenus(headerMenus || [], rightCodes, ignoreRight);
    const list = tempMenus
      .map(el => {
        const rights = [
          ...(el.rights || []),
          ...(el.children?.map(child => child.rights)?.flat() || []),
        ];
        return {
          ...el,
          hasAuth: checkHasAuth(rights, rightCodes, ignoreRight),
        };
      })
      .filter(e => e.hasAuth);
    let url_name = '';
    list.map(item => {
      if (url_name === '') {
        if ((item.children || []).length < 1 && item.hasAuth) {
          url_name = item.name;
        } else {
          const no_hidden_children = (item.children || []).filter(e => !e.hidden) || [];
          const children = (item.children || []).filter(e => e.hasAuth && !e.hidden) || [];
          if (children.length > 0) {
            url_name = children[0].name;
          } else if (no_hidden_children.length < 1 && item.hasAuth) {
            url_name = item.name;
          }
        }
      }
    });
    return url_name;
  };

  return {
    transFindHeaderMenuName,
  };
};
