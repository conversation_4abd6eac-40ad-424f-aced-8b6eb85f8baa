import { ElButton } from 'element-plus';
import { defineComponent, cloneVNode, ref } from 'vue';

export default defineComponent({
  name: 'HeaderLine',
  props: {
    header_title: {
      type: String,
    },
    desc_title: {
      type: String,
    },
    direction: {
      type: String, //Horizontal,vertical
      default: 'Horizontal',
    },
    currentColor: {
      type: String,
      default: 'var(--text-color)',
    },
    line: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, { slots }) {
    return () => {
      const slotContent = slots.default?.() ?? [];
      return (
        <div>
          <div
            class="header-line-div"
            style={{
              borderBottom: props.line ? '1px solid #d7dbe7' : '',
            }}
          >
            <div class="header-nav">
              <div
                class="title-div"
                style={{
                  color: props.currentColor,
                  // 'border-bottom': '2px solid ' + props.currentColor,
                }}
              >
                {props.header_title}
              </div>
              {props.desc_title && <div class="desc_title-div">{props.desc_title}</div>}
            </div>
            {slotContent.length > 0 && (
              <div class="right-div">{cloneVNode(slotContent[0] as any)}</div>
            )}
          </div>
        </div>
      );
    };
  },
});
