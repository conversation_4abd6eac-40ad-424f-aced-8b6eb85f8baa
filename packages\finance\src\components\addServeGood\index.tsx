import {
  api_goods_platform_page_dropdown_get,
  api_goods_city_$cityId_page_dropdown_get,
} from '@mjGenerate/request';
import { MjTableColumn } from '@common/types/vendor/column';
import { usePaginationV2 } from 'common';
import { ElButton, ElForm, ElFormItem, ElInput, ElTag } from 'element-plus';
import { defineComponent, ref, computed } from 'vue';
export default defineComponent({
  setup(props, ctx) {
    function initFormData() {
      return {} as any;
    }
    const formData = ref<any>(initFormData());
    const city_id = ref();
    const tableRef = ref<any>();
    const multipleSelection = ref<any[]>([]);
    const tableData = ref<any[]>([]);
    const type = ref(); // 1平台商品 2服务商品

    // 平台商品
    const reqList = usePaginationV2(api_goods_platform_page_dropdown_get as any, {
      manual: true,
      defaultPageSize: 100,
      onSuccess(data) {
        tableData.value = data;
        setTimeout(() => {
          multipleSelection.value.forEach(item => {
            const find = tableData.value?.find(i => Number(item.id) === Number(i.id));
            if (find) {
              tableRef.value?.toggleRowSelection(find, true);
            }
          });
        }, 100);
      },
    });

    // 城市商品
    const cityReqList = usePaginationV2(api_goods_city_$cityId_page_dropdown_get as any, {
      manual: true,
      defaultPageSize: 100,
      onSuccess(data) {
        tableData.value = data;
        setTimeout(() => {
          multipleSelection.value.forEach(item => {
            const find = tableData.value?.find(i => Number(item.id) === Number(i.id));
            if (find) {
              tableRef.value?.toggleRowSelection(find, true);
            }
          });
        }, 100);
      },
    });

    const getParams = () => {
      return {
        ...formData.value,
      };
    };

    const show = val => {
      city_id.value = val.city_id;
      multipleSelection.value = val.selectList || [];
      type.value = val.type || 1;
      onQuery();
    };

    const onSaveBtnClick = () => {
      ctx.emit('submit', { list: multipleSelection.value, ...formData.value });
    };
    // 核心
    ctx.expose({ onSaveBtnClick, show });

    const onQuery = () => {
      const params = getParams();
      params.goods_class = 2;
      params.is_physical = false;
      if (type.value == 1) {
        params.active_status = true;
        reqList.runAsync(params);
      } else {
        cityReqList.runAsync(city_id.value, params);
      }
    };

    const onSearch = () => {
      onQuery();
    };

    const onReset = () => {
      formData.value = { ...initFormData(), theme_id: formData.value.theme_id };
      onQuery();
    };

    const columns = computed<MjTableColumn<any>[]>(() => [
      {
        type: 'selection',
        width: 36,
        align: 'center',
        reserveSelection: true,
      },
      {
        label: '商品编号',
        minWidth: 50,
        prop: 'goods_number',
        showOverflowTooltip: true,
      },
      {
        label: '商品名称',
        width: 120,
        prop: 'name',
        showOverflowTooltip: true,
      },
      {
        label: '别名',
        width: 120,
        prop: 'name_short',
        showOverflowTooltip: true,
      },
      {
        label: '货盘原价',
        width: 80,
        prop: 'list_price__yuan',
        showOverflowTooltip: true,
        formatter(row) {
          return row.list_price__yuan ? '￥' + row.list_price__yuan : '--';
        },
      },
      {
        label: '状态',
        width: 120,
        prop: 'theme_name',
        showOverflowTooltip: true,
        formatter(row) {
          return row.active_status
            ? `${type.value === 1 ? '启用' : '上架'}`
            : `${type.value === 1 ? '停用' : '下架'}`;
        },
      },
    ]);

    const handleSelectionChange = selectList => {
      const temp = [
        ...selectList,
        ...multipleSelection.value.filter(
          item => !tableData.value.find(i => Number(i.id) === Number(item.id)),
        ),
      ];
      const map: any = [];

      temp.forEach(item => {
        if (!map.find(i => Number(i.id) === Number(item.id))) {
          map.push(item);
        }
      });

      multipleSelection.value = map;
    };

    const onClosable = key => {
      const find = multipleSelection.value.find(item => item.id === key);
      multipleSelection.value = multipleSelection.value.filter(i => i.id !== key);
      tableRef.value.tableRef?.toggleRowSelection(find, false, false);
    };

    const onClear = () => {
      tableRef.value.tableRef.clearSelection();
      multipleSelection.value = [];
    };

    return () => (
      <div class="agent-select-dialog-content">
        <ElForm inline={true} form={formData.value}>
          <div class="form-content">
            <ElFormItem>
              <ElInput
                style="width: 120px"
                placeholder="商品编号"
                clearable
                v-model={formData.value.goods_number}
              ></ElInput>
            </ElFormItem>
            <ElFormItem>
              <ElInput
                style="width: 120px"
                placeholder="商品名称"
                clearable
                v-model={formData.value.name}
              ></ElInput>
            </ElFormItem>
            <el-button type="primary" onClick={onSearch}>
              搜索
            </el-button>
            <el-button onClick={onReset}>重置</el-button>
          </div>
        </ElForm>
        <div class="agent-select-content">
          <div class="agent-select-list">
            <mj-table
              ref={tableRef}
              border
              height="390px"
              rowKey="id"
              data={tableData.value}
              pagination={type.value === 1 ? reqList.pagination : cityReqList.pagination}
              columns={columns.value}
              onSelectionChange={handleSelectionChange}
            ></mj-table>
          </div>

          <div class="agent-select-selected">
            <div class="title">
              <span>已选中{multipleSelection.value.length || 0}个商品</span>
              <ElButton link type="primary" onClick={() => onClear()}>
                清空选中
              </ElButton>
            </div>
            <div class="agent-select-selected-list">
              {multipleSelection.value.map(item => {
                return (
                  <span class="agent-select-tag">
                    <ElTag type="info" closable onClose={() => onClosable(item.id)}>
                      {(item as any).name}
                    </ElTag>
                  </span>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    );
  },
});
