/**
文档地址 https://tailwind.nodejs.cn/docs/
用到什么自己拷贝什么
 */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.flex-nowrap {
  flex-wrap: nowrap;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-auto {
  flex: 1 1 auto;
}
.flex-initial {
  flex: 0 1 auto;
}
.flex-none {
  flex: none;
}
.items-stretch {
  align-items: stretch;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-end {
  justify-content: end;
}
.justify-stretch {
  justify-content: stretch;
}
.justify-between {
  justify-content: space-between;
}
// 伸张
.grow {
  flex-grow: 1;
}
.grow-0 {
  flex-grow: 0;
}
// 搜索
.shrink {
  flex-shrink: 1;
}

.basis-auto {
  flex-basis: auto;
}
.basis-full {
  flex-basis: 100%;
}
.basis-0 {
  flex-basis: 0;
}
.col-end-5 {
  grid-column-end: 5;
}
.col-start-1 {
  grid-column-start: 1;
}
.col-span-2 {
  grid-column: span 2 / span 2;
}
.col-span-full {
  grid-column: 1 / -1;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-clip {
  overflow: clip;
}
.overflow-auto {
  overflow: auto;
}
.overflow-y-auto {
  overflow-y: auto;
}
.h-full {
  height: 100%;
}
