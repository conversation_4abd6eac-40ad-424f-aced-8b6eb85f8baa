import { Warning } from '@element-plus/icons-vue';

export const HomeOrderTipsContent = '美团数据T+1导入，今日数据中暂不包含。';
export const HomeOrderAmountTipsContent = `1、根据商品的货盘原价统计；\n2、美团数据T+1导入，今日数据中暂不包含。`;
export const HomePayAmountTipsContent = '用户实付金额：根据用户的实付金额统计（不包含平台补贴）';
export const HomeWriteOffAmountTipsContent = `1、抖音订单初始数据未包含平台补贴金额，1-2小时后更新；\n2、美团订单初始数据未剔除商家营销费用，月度出账后更新`;
export const HomeDailyPercentTipsContent = '与前一日当前时间数据的环比';
export const HomeMonthlyPercentTipsContent = `与前一周期数据的环比，如：\n1、2.1-2.10对比1.1-1.0数据；\n2、2.1-2.28对比1月整月数据；\n3、3.1-3.31对比2月整月数据。`;

export const HomeOrderTipsNewContent =
  '美团数据T+1导入，今日数据中暂不包含;\n不包含美睫等实物商品订单及核销数据；';
export const HomeOrderAmountTipsNewContent =
  '1、根据商品的货盘原价统计；\n2、美团数据T+1导入，今日数据中暂不包含；\n3、不包含美睫等实物商品订单及核销数据；';
export const HomePayAmountTipsNewContent =
  '用户实付金额：根据用户的实付金额统计（不包含平台补贴）;\n不包含美睫等实物商品订单及核销数据；';
export const HomeWriteOffOrderTipsNewContent =
  '不包含美睫等实物商品订单及核销数据';
export const HomeWriteOffAmountTipsNewContent =
  '1、抖音订单初始数据未包含平台补贴金额，1-2小时后更新；\n2、美团订单初始数据未剔除商家营销费用，月度出账后更新；\n3、不包含美睫等实物商品订单及核销数据；';
export function homeGenerateTips(
  content: string,
  optoins?: {
    iconStyle?: any;
    iconClass?: string;
  },
) {
  return (
    <el-popover
      effect="dark"
      placement="top"
      trigger="hover"
      content={content}
      popper-style={{
        width: 'auto',
        whiteSpace: 'pre-wrap',
      }}
    >
      {{
        reference: () => (
          <el-icon
            class={{
              'tips-icon__default': true,
              'mgl-10': true,
              [optoins?.iconClass || '']: !!optoins?.iconClass,
            }}
            style={optoins?.iconStyle}
          >
            <Warning />
          </el-icon>
        ),
      }}
    </el-popover>
  );
}
