<script src="./index.tsx"></script>
<style lang="scss" scoped>
.operation-record {
  display: flex;
  flex-direction: column;
  flex: 1;
  max-height: 100%;
  overflow-y: scroll;
  .title {
    width: 100%;
    height: 16px;
    font-size: 14px;
    font-family: -700;
    font-weight: bold;
    color: #333333;
    background-color: #fff;
    padding-bottom: 34px;
    position: sticky;
    z-index: 1000;
    top: 0;
  }
  .setp-wrapper {
    overflow-y: scroll;
    height: 0;
    // max-height: 450px;
    flex: 1;
    .op-icon {
      width: 8px;
      height: 8px;
      display: block;
      background: #ff3c9f;
      border-radius: 100px;
      border: 4px solid #fff1f9;
      box-sizing: content-box;
      margin-top: -95px;
    }

    ::v-deep(.el-step__icon):has(.op-icon) {
      background: transparent !important;
    }
    ::v-deep(.el-step__line) {
      width: 1px !important;
    }
    ::v-deep(.el-step__head) {
      transform: translateY(-1px) !important;
    }
  }
}
</style>
