import { defineComponent, onMounted, onUnmounted, ref, Ref } from 'vue';
import './index.less';
import { VNode } from 'vue';
import { ElForm, ElButton } from 'element-plus';

const getVNodeTag = (node: VNode) => {
  console.log('vnode', node);
  return (node.type as string) ?? '';
};
const getTypePlaceholderWidth = (node: VNode) => {
  const tag = getVNodeTag(node).toLowerCase();
  let start_width = 120;
  if (tag.includes('department-select')) {
    return 220;
  } else if (tag.includes('input') || tag.includes('select')) {
    const strlen = Math.max(0, getVNodePlaceHolderLength(node) - 4);
    return Math.max(start_width, Math.abs(strlen * 20) + start_width);
  } else if (tag.includes('date-picker')) {
    const placeholderLength = (node?.props?.['start-placeholder'] || '').length;
    const strlen = Math.max(0, placeholderLength - 4);
    start_width = 220;
    return Math.max(start_width, Math.abs(strlen * 20) + start_width);
  } else {
    return undefined;
  }
};
const getVNodePlaceHolderLength = (node: VNode) => {
  return (node?.props?.placeholder || '').length;
};
// 动态计算需要设置的宽度
const calculateTheDynamicWidth = (...nodes: VNode[]): number | undefined => {
  const children: VNode[] = [...nodes];
  let width: number | undefined;
  let current: VNode | undefined = children.shift();
  while (current) {
    width = getTypePlaceholderWidth(current);
    if (width !== undefined) break;

    if (current?.children) {
      children.push(...(current.children as any));
    }

    current = children.shift();
  }
  return width ?? 120;
};

const normalizedStyleWidth = (max: number, node: any) => {
  let style = node.data?.style || node.data?.staticStyle;
  let oriWidth = 0;
  if (!style) {
    if (!['el-form-item', 'bm-form-item'].includes(getVNodeTag(node))) {
      return oriWidth;
    }
    style = `width:${calculateTheDynamicWidth(node)}px;`;
    node.data.style = style;
  }
  let tmp: any;
  if (typeof style === 'object') {
    tmp = `width:${style.width}`;
  } else {
    tmp = style;
  }
  if (typeof tmp === 'string') {
    tmp = /width:\s*(\d+)(px|%)/.exec(tmp);
    if (!tmp) return oriWidth;
    if (tmp[2] === '%') {
      oriWidth = (Number(tmp[1]) / 100) * max;
    } else {
      oriWidth = Number(tmp[1]);
    }
  } else {
    oriWidth = Number(tmp);
  }
  return oriWidth;
};
export default defineComponent({
  components: {
    ElForm,
    ElButton,
  },
  props: {
    showExport: {
      type: Boolean,
      default: () => false,
    },
    semiAngle: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    onEnterKey(e: any) {
      if (e.key !== 'Enter') return;
      const input = e.target as HTMLInputElement;
      if (input.tagName.toUpperCase() !== 'INPUT') return;
      if (input.className !== 'el-input__inner') return;
      if (!input.parentElement) return;
      let className = input.parentElement?.className || '';
      if (!className.includes('el-input')) return;
      // 这里额外处理, 如果是下拉框中的input也跳过,  后期看情况添加其他表单中的input
      className = input.parentElement?.parentElement?.className || '';
      if (className.includes('el-select')) return;
      this.$emit('search');
    },
  },
  setup() {
    const filterAreaRef = ref<HTMLElement>();
    const defaultLineRef = ref<HTMLElement>();
    const actionAreaRef = ref<HTMLElement>();
    const clientWidth = ref(0);
    const defaultClientWidth = ref(0);
    const actionAreaWidth = ref(0);
    const isUnfold = ref(false);

    const watchResize = (target: Ref<any>, change: Ref<any>) => {
      const resizeObserver = new ResizeObserver(entries => {
        for (const entrin of entries) {
          setTimeout(() => {
            if (entrin.contentBoxSize !== undefined) {
              change.value = entrin.contentBoxSize[0].inlineSize;
            } else {
              change.value = entrin.contentRect.width;
            }
          });
        }
      });
      onMounted(() => {
        if (!target.value) return;
        change.value = target.value.clientWidth;
        resizeObserver.observe(target.value);
      });
      onUnmounted(() => {
        resizeObserver.disconnect();
      });
    };
    watchResize(filterAreaRef, clientWidth);
    watchResize(defaultLineRef, defaultClientWidth);
    watchResize(actionAreaRef, actionAreaWidth);
    function getNodeKey(index: number) {
      return `key_${index++}`;
    }
    return {
      filterAreaRef,
      defaultLineRef,
      actionAreaRef,
      clientWidth,
      actionAreaWidth,
      defaultClientWidth,
      isUnfold,
      getNodeKey,
    };
  },
  render() {
    const childNode: VNode[] = this.$slots.default?.() || [];
    // 第一行数据
    const filterLine: { widths: string; nodes: any[]; maxWidth: number }[] = [];
    const gapWidth = 12;
    // 容器有宽度时才进行计算
    if (this.clientWidth > 0) {
      let maxWidth = this.clientWidth - this.actionAreaWidth;
      let currentWidth = 0;
      let currentLine: { widths: string; nodes: any[]; maxWidth: number } = undefined as any;

      if (childNode.length > 0) {
        currentLine = { widths: '', nodes: [], maxWidth };
        filterLine.push(currentLine);
      }
      let index = 0;
      for (const node of childNode) {
        node.key = this.getNodeKey(index++);
        let nodeWidth = normalizedStyleWidth(maxWidth, node);
        if (nodeWidth === 0) {
          continue;
        }

        const renderNode: any = node;
        currentWidth += nodeWidth + gapWidth;
        currentLine.maxWidth = currentWidth;
        const wrapperRenderNode = renderNode;
        if (currentWidth > maxWidth) {
          maxWidth = this.defaultClientWidth;
          nodeWidth = normalizedStyleWidth(maxWidth, node);
          currentWidth = nodeWidth + gapWidth;
          currentLine = { widths: '', nodes: [], maxWidth };
          filterLine.push(currentLine);
        }
        currentLine.widths += `${nodeWidth}px `;
        currentLine.nodes.push(wrapperRenderNode);
      }
    } else {
      childNode.forEach((el, elIdx) => {
        el.key = this.getNodeKey(elIdx);
      });
    }
    const [firstLine, ...otherLine] = filterLine;
    let hasChildNode = false;
    if (firstLine && firstLine.nodes.length > 0) {
      hasChildNode = true;
    }

    const hasOtherBtn = this.$slots.otherBtns || this.showExport;

    return (
      <div
        class={`bmt-formlist-search-bar ${this.semiAngle ? 'semi-angle' : ''}`}
        onKeyup={this.onEnterKey}
      >
        <el-form
          size="medium"
          show-message={false}
          hide-required-asterisk={true}
          label-width="0"
          ref="formRef"
          nativeOn={{
            submit: (e: Event) => {
              e.preventDefault();
            },
          }}
        >
          <div class="bmt-formlist-form-item-container">
            <div
              class={`bmt-formlist-default-line ${hasOtherBtn ? 'two' : ''}`}
              ref="defaultLineRef"
            >
              <div class="bmt-formlist-filter-area" ref="filterAreaRef">
                {/* {firstLine && ( */}
                <div
                  class="bmt-formlist-filter-content"
                  key="bmt-formlist-filter-content"
                  style={`grid-template-columns:${firstLine?.widths}; ${
                    !firstLine?.nodes?.length ? 'display: none' : ''
                  }`}
                >
                  {firstLine?.nodes || this.$slots.default}
                </div>
                {/* )} */}
                <div
                  class="bmt-formlist-action-area"
                  key="bmt-formlist-action-area"
                  ref="actionAreaRef"
                >
                  {this.$slots.searchBtn ? (
                    this.$slots.searchBtn
                  ) : hasChildNode ? (
                    <div class="bmt-formlist-form-item-search-operation">
                      {otherLine.length > 0 && (
                        <el-button
                          class="mgr-10"
                          size="medium"
                          onClick={() => {
                            this.isUnfold = !this.isUnfold;
                          }}
                        >
                          {this.isUnfold ? '收 起' : '展 开'}
                        </el-button>
                      )}
                      <el-button
                        class="mgr-10"
                        type="primary"
                        size="medium"
                        onClick={() => this.$emit('search')}
                      >
                        搜 索
                      </el-button>
                      <el-button size="medium" onClick={() => this.$emit('reset')}>
                        重 置
                      </el-button>
                    </div>
                  ) : undefined}
                </div>
                <div class="bmt-formlist-action-fill" key="bmt-formlist-action-fill" />
              </div>

              {hasOtherBtn && (
                <div class="bmt-formlist-expansion-zone">
                  {this.$slots.otherBtns}
                  {this.showExport && (
                    <el-button
                      icon="ico-common-daochu-linear"
                      size="medium"
                      // disabled={this.disabledExport}
                      onClick={() => this.$emit('export')}
                    >
                      导出
                    </el-button>
                  )}
                </div>
              )}
            </div>
            {this.isUnfold &&
              otherLine.map(item => {
                return (
                  <div
                    class="bmt-formlist-extend_line"
                    style={`grid-template-columns:${item.widths}`}
                  >
                    {item.nodes}
                  </div>
                );
              })}
          </div>
        </el-form>
      </div>
    );
  },
});
