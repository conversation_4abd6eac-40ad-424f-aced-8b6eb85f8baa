import { EnumUtil, EnumValue } from 'common';

export class tradingAccount extends EnumUtil {
  static pendingClearingAccount = EnumValue(1, '待清分账户');
  static pjCommissionAccount = EnumValue(2, 'PJ佣金账户');
  // static agentSubLedger = EnumValue(3, '代理商子台账');
  // static storeSubLedger = EnumValue(4, '门店子台账');
}

export class incomeExpenditureType extends EnumUtil {
  static storeCommission = EnumValue(1, '门店分佣');
  static brandCommission = EnumValue(2, '品牌抽佣');
  static agentTransfer = EnumValue(3, '提现');
  static withdrawal = EnumValue(4, '提现退单');
}

export class transactionType extends EnumUtil {
  static outAccount = EnumValue(1, '出账');
  static entry = EnumValue(2, '入账');
}
