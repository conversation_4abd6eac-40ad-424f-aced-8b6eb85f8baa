<script src="./index.tsx"></script>
<style lang="scss" scoped>
  .page-container {
    display: flex;
    flex-direction: column;

    .data-section {
      display: flex;
      margin-bottom: 12px;
      padding: 12px;
      background-color: #ffffff;
      box-shadow: 0 2px 12px 0 #1e146a0d;
      border-radius: 10px;

      .data-item {
        width: 180px;
        height: 82px;
        border-radius: 8px;
        background: #f5f9ff;
        padding: 14px 0 16px 16px;
        margin-right: 12px;

        .title {
          display: flex;
          align-items: center;
          font-size: 14px;
          color: #1c1f2e;
          margin-bottom: 8px;

          .text {
            margin-right: 4px;
          }
        }

        .amount {
          display: flex;
          align-items: baseline;
          font-size: 16px;
          color: var(--theme-color);
          font-weight: 700;

          .integer {
            font-size: 24px;
          }

          .decimal {
            padding-right: 10px;
          }

          .oper_btn {
            color: var(--theme-color);
            font-size: 14px;
          }
          .opcity {
            width: 0;
            opacity: 0;
          }
        }
      }
    }

    .filter-section {
      display: flex;
      background: #ffffff;
      border-radius: 12px;
      align-items: center;
      padding: 12px 16px 0;
      margin-bottom: 12px;

      :deep(.el-form) {
        display: flex;
        flex-wrap: wrap;
        width: 100%;

        .el-form-item {
          margin-bottom: 12px;
          margin-right: 10px;
        }
      }

      .form-content {
        display: grid;
        grid-template-columns: auto 140px 350px;
      }

      .form-item-list {
        display: flex;
        flex-wrap: wrap;
      }
    }

    .table-section {
      flex: 1;
      overflow: hidden;
      background: #ffffff;
      border-radius: 12px;
      padding: 12px 16px;

      :deep(.el-table) {
        .header-container {
          display: flex;
          align-items: center;
          justify-content: flex-end;

          .tips-icon {
            font-size: 14px;
          }
        }
      }
    }
  }
</style>
