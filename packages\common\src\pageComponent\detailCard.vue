<script src="./detailCard.tsx"></script>
<style lang="scss" scoped>
  .detail-card {
    display: grid;
    grid-template: min-content / auto;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 0 5px #0000001a;
    font-size: 12px;
    $paddingtop: 20px;
    padding: $paddingtop 16px $paddingtop 16px;
    &:has(.detail-header) {
      padding-top: 0;
    }
    .detail-header {
      border-bottom: solid 1px #aaaaaa73;
      padding: $paddingtop 0 10px 0;
      font-size: 14px;
      font-weight: 600;
      line-height: 1;
      & + .detail-body {
        padding: var(--detail-card-body-padding, 8px) 0 0 0;
      }
    }
    .detail-body {
      display: grid;
      grid-template: 1fr / auto;
      grid-row-gap: var(--detail-card-row-gap, 12px);

      :deep(.el-form-item) {
        margin-bottom: initial;
      }
    }
  }
</style>
