import { findSchemaRefValue, getJavascriptType, getObjectDeepValue, genType } from './utils.js';

let uuid = 1;

/**
 * 解析 Params 参数, 确定函数名
 */
const parseParams = data => {
  let url = data.path;
  data.paramsCode = [];
  url = url
    .split('/')
    .map(str => {
      const m = /<(\w+):(\w+)>/.exec(str);
      if (!m) return str;
      let paramsName = m[2];
      let paramsType = getJavascriptType(m[1]);
      // 参数名字 api_$xxx_xxx
      paramsName = '$' + paramsName.replace(/_(\w)/g, ($0, $1) => $1.toUpperCase());
      data.paramsCode.push({
        name: paramsName,
        type: paramsType,
      });

      return paramsName;
    })
    .filter(t => t !== '')
    .join('_');
  data.functionName = url;
};
/**
 * 解析 query类型的参数
 */
const parseQueryParams = data => {
  const { current } = data;
  data.paramsQuery = null;
  // 获取接口描述
  data.summary = current.summary.replace(/\n/g, '');
  if (!current.parameters || current.parameters.length === 0) return;
  data.paramsQuery = current.parameters.map(item => {
    return { name: item.name, type: getJavascriptType(item.schema.type) };
  });
};
/**
 * 生成post参数类型
 */
const genPostParamsType = data => {
  const { openData, bodyParams } = data;
  const typeMap = bodyParams.schema;
  const typeDes = typeMap ? findSchemaRefValue(openData, typeMap) : {};
  return genType(typeDes);
};

/**
 * 解析 body 参数
 */
const parseBodyParams = data => {
  data.bodyParams = null;
  const { current } = data;
  if (current.requestBody) {
    const check = current?.requestBody?.content?.['application/json']?.schema?.$ref;
    if (check === undefined) {
      //todo 其他格式需要支持
      console.error('未在请求中找到 application/json');
    }
    data.bodyParams = { type: 'schema', schema: check };
  }
};
const parseResponse = data => {
  data.response = null;
  const { current } = data;
  const $ref =
    getObjectDeepValue(current, 'responses.200.content.application/json.schema.$ref') ||
    getObjectDeepValue(current, 'responses.200.content.application/octet-stream.schema.$ref');
  if (!$ref) {
    console.error('缺少输出类型， 请补充');
  }
  const $value = findSchemaRefValue(data.openData, $ref);
  data.hasPagination = hasPaginationResponse($value);
  data.responseConf = {
    type: 'schema',
    schema: $ref,
    responseData: hasPaginationResponse
      ? getObjectDeepValue($value, 'properties.data.properties.data')
      : getObjectDeepValue($value, 'properties.data'),
  };
};

// 判断是否 分页 请求
const hasPaginationResponse = value => {
  const pageData = getObjectDeepValue(value, 'properties.data.properties.data');
  const total = getObjectDeepValue(value, 'properties.data.properties.total');
  if (pageData && total) return true;
  return false;
};

const generateClaimCode = data => {
  const { declareCode, functionName, method } = data;
  let code = '';
  const functionNameFull = `${functionName}_${method}`;
  const paramsCode = [];
  // 实际代码
  if (data.hasPagination) {
    paramsCode.push(`pager:IGPageQuery`);
  }
  if (data.paramsCode.length > 0) {
    paramsCode.push(
      ...data.paramsCode.map(item => {
        return `${item.name}:${item.type}`;
      }),
    );
  }

  // 如果有 query 参数
  if (data.paramsQuery) {
    const tmpMap = new Map();
    const interfaceName = `IReq_${uuid++}`;
    code += `  interface ${interfaceName} {\n`;
    code += data.paramsQuery
      .filter(item => {
        if (tmpMap.has(item.name)) return false;
        tmpMap.set(item.name, 1);
        return true;
      })
      .map(item => {
        return `    ${item.name}:${item.type}`;
      })
      .join('\n');
    code += '\n  }\n';
    paramsCode.push(`$query:${interfaceName}`);
  }
  // 如果有post参数
  if (data.bodyParams) {
    // todo 来不及了， 先any吧
    paramsCode.push(`
      post:${genPostParamsType(data)}
      `);
  }
  if (data.responseConf.responseData) {
    data.responseConf.responseDataType = genType(data.responseConf.responseData) || 'any';
  }
  // 方法描述
  code += `  \/**\n   ${data.summary} \n    ${data.path}\n  *\/\n`;
  code += `  export declare const ${functionNameFull}: (${paramsCode.join(',')})=> Promise<${
    data.hasPagination ? 'ListResponse' : 'HttpResponse'
  }<${data.responseConf.responseDataType || 'any'}>>;`;
  declareCode.push(code);
};
const generateRunCode = data => {
  const { sourceCode, functionName, method } = data;
  let code = ``;
  const functionNameFull = `${functionName}_${method}`;
  let url = data.path;
  const paramsCode = [];
  const hasOtherParams = [];
  // 实际代码
  if (data.hasPagination) {
    paramsCode.push(`pager`);
  }
  if (data.paramsCode.length > 0) {
    paramsCode.push(
      ...data.paramsCode.map(item => {
        return `${item.name}`;
      }),
    );
    let index = 0;
    url = url.replace(/<(\w+):(\w+)>/g, () => {
      return `\${${data.paramsCode[index++].name}}`;
    });
  }

  // 如果有 query 参数
  if (data.paramsQuery) {
    paramsCode.push(`query`);
    if (data.hasPagination) {
      hasOtherParams.push(',', '    params:ObjectFilterEmpty({...query,...pager})');
    } else {
      hasOtherParams.push(',', '    params:ObjectFilterEmpty(query)');
    }
  }
  // 如果有post参数
  if (data.bodyParams) {
    paramsCode.push('body');
    hasOtherParams.push(',', '    data:ObjectFilterEmpty(body)');
  }

  // 方法描述
  code += `export const ${functionNameFull} = (${paramsCode.join(',')}) => request({
    method:'${data.method}',
    url:\`${url}\`${hasOtherParams.join('\n')}
});`;
  sourceCode.push(code);
};

// 组合代码
const assembleCode = data => {
  generateClaimCode(data);
  generateRunCode(data);
};

/**
 * 解析openApi
 * @param openData
 */
export const parseOpenApi = openData => {
  // ts申明代码
  // 当前步骤的数据
  const parseData = { declareCode: [], sourceCode: [], openData, current: null };
  // 遍历所有接口
  Object.keys(openData.paths).forEach(path => {
    parseData.path = path;
    parseData.paramsCode = [];
    parseParams(parseData);
    const req = openData.paths[path];
    Object.keys(req).forEach(method => {
      parseData.method = method;
      parseData.current = req[parseData.method];
      parseQueryParams(parseData);
      parseBodyParams(parseData);
      parseResponse(parseData);
      assembleCode(parseData);
    });
  });
  return parseData;
};
