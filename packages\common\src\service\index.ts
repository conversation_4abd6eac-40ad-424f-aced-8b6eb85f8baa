import { ref } from 'vue';
import {
  get_resources_cities,
  get_store_city_tree,
  get_agents_store_city_tree,
} from '@common/services';
import { ICity } from '@common/types/city';

const cityCache = new Map();
const cityLoadingModel = {} as any;
const getCityLoading = (key: number | undefined) => cityLoadingModel[key || ''];
const setCityLoading = (key: number | undefined, loading: boolean) =>
  (cityLoadingModel[key || ''] = loading);
export const mj_get_city_list = async (): Promise<ICity[]> => {
  return mj_get_city_list_long({ level: 2 });
};
// 有三级的配置
export const mj_get_city_list_long = async (options?: { level?: number }): Promise<ICity[]> => {
  setCityLoading(options?.level, true);
  return get_resources_cities(options)
    .then(res => res.data.data || [])
    .finally(() => setCityLoading(options?.level, false));
};

const getCityCacheKey = (level?: number) => {
  return `mj_city_cache_key_${level}`;
};
export const useCityList = (options?: { level?: number }) => {
  const cityRef = ref<ICity[]>([]);
  const key = getCityCacheKey(options?.level);
  if (cityCache.has(key)) {
    cityRef.value = cityCache.get(key);
  } else {
    if (!getCityLoading(options?.level)) {
      mj_get_city_list_long(options).then(data => {
        cityRef.value = data;
        cityCache.set(key, data);
      });
    }
  }
  return cityRef;
};

export const useStoreCityList = (options?: { level?: number }) => {
  const cityRef = ref<ICity[]>([]);
  get_store_city_tree({ level: options?.level }).then(data => {
    cityRef.value = data.data.data || [];
  });
  return cityRef;
};

export const useAgentsStoreCityList = (options?: { level?: number }) => {
  const cityRef = ref<ICity[]>([]);
  get_agents_store_city_tree({ level: options?.level }).then(data => {
    cityRef.value = data.data.data || [];
  });
  return cityRef;
};

export const useCityShortList = () => {
  const cityRef = ref<ICity[]>([]);
  const key = getCityCacheKey(2);
  if (cityCache.has(key)) {
    cityRef.value = cityCache.get(key);
  } else {
    if (!getCityLoading(2)) {
      mj_get_city_list().then(data => {
        cityCache.set(key, data);
        cityRef.value = data;
      });
    }
  }
  return cityRef;
};

// export const useResourceCityList = () => {
//   const cityRef = ref<any[]>([]);
//   if (cityCache.has('type')) {
//     cityRef.value = cityCache.get('type');
//   } else {
//     get_resources_cities().then(data => {
//       cityRef.value = data;
//       cityCache.set('type', data);
//     });
//   }
//   return cityRef;
// };
