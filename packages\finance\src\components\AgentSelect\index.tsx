import { defineComponent, onMounted, ref } from 'vue';
import './index.scss';
import { ElButton, ElDialog, ElForm, ElFormItem, ElInput, ElTag } from 'element-plus';
import { MjTableColumn } from 'common/src/types/vendor/column';
import { deepClone, useCityList, usePagination } from 'common';
import { getAgentDropdownApi } from '@/services/message';

interface ValueItemProps {
  id: number;
  name: string;
}

export default defineComponent({
  name: 'AgentSelect',
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    extendOptions: [],
    modelValue: Array<ValueItemProps>,
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const visible = ref<boolean>(false);
    const options = ref<any[]>([]);
    const multipleSelection = ref<any[]>([]);

    const cityList = useCityList();
    const cascaderProps = {
      expandTrigger: 'hover' as const,
      checkStrictly: true,
      label: 'name',
      value: 'name',
    };

    const reqList = usePagination(getAgentDropdownApi as any, {
      manual: true,
      // defaultPageSize: 8,
    });

    const columns = ref<MjTableColumn<any>[]>([
      {
        type: 'selection',
        width: 36,
        align: 'center',
        reserveSelection: true,
      },
      {
        label: '所在地区',
        minWidth: 180,
        prop: 'area',
        showOverflowTooltip: true,
        formatter: row => {
          return (
            <div>
              {row.province}/{row.city}/{row.area}
            </div>
          );
        },
      },
      {
        label: '代理商',
        width: 160,
        prop: 'name',
        showOverflowTooltip: true,
      },
    ]);

    function initFormData() {
      return {} as any;
    }

    const formData = ref<any>(initFormData());
    const tableRef = ref<any>();

    const getParams = () => {
      const [province, city, area] = formData.value?.cities || [];
      return {
        cities: formData.value?.cities || undefined,
        name: formData.value?.name || undefined,
        province: province || undefined,
        area: area || undefined,
        city: city || undefined,
      };
    };

    const onQuery = (new_page_num?: number) => {
      if (new_page_num) reqList.pagination.page_num = new_page_num;
      const params = getParams();
      reqList.runAsync(
        {
          join_status: 3,
          page_size: 10000,
        },
        params,
      );
    };

    const onSelect = () => {
      visible.value = true;
      multipleSelection.value = props.extendOptions || [];
      options.value = deepClone(props.extendOptions || []);
    };

    const onOpened = () => {
      multipleSelection.value.forEach(item => {
        const find = reqList.data?.find(i => item.id === i.id);
        if (find) {
          tableRef.value?.toggleRowSelection(find, true, false);
        }
      });
    };

    const onSearch = () => {
      onQuery(1);
    };

    const onReset = () => {
      formData.value = initFormData();
    };

    const handleSelectionChange = selectList => {
      multipleSelection.value = selectList;
    };

    const onClosable = key => {
      const find = multipleSelection.value.find(item => item.id === key);
      multipleSelection.value = multipleSelection.value.filter(i => i.id !== key);
      tableRef.value.tableRef?.toggleRowSelection(find, false, false);
    };

    const onClear = () => {
      tableRef.value.tableRef.clearSelection();
      multipleSelection.value = [];
    };

    const onCancel = () => {
      visible.value = false;
      multipleSelection.value.map(item => {
        tableRef.value.tableRef?.toggleRowSelection(item, false, false);
      });
    };

    const onSave = () => {
      emit('update:modelValue', multipleSelection.value);
      visible.value = false;
      options.value = deepClone(multipleSelection.value);
    };

    onMounted(() => {
      multipleSelection.value = props.extendOptions || [];
      options.value = deepClone(props.extendOptions || []);
      onQuery(1);
    });

    return () => (
      <div class="agent-select-wrapper">
        <div
          class="agent-select-input"
          style={{ width: '200px', backgroundColor: props.disabled ? 'rgb(240, 243, 250)' : '' }}
        >
          {`已选中${options.value.length}个代理商`}
        </div>
        <ElButton onClick={onSelect} type="primary" disabled={props.disabled}>
          选择
        </ElButton>
        <ElDialog
          v-model={visible.value}
          title="选择代理商"
          center
          width={860}
          onClose={onCancel}
          onOpened={onOpened}
        >
          <div class="agent-select-dialog-content">
            <ElForm inline={true}>
              <div class="form-content">
                <ElFormItem>
                  <el-cascader
                    v-model={formData.value.cities}
                    options={cityList.value || []}
                    props={cascaderProps}
                    placeholder="所在地区"
                    clearable
                    style="width: 200px"
                  />
                </ElFormItem>
                <ElFormItem>
                  <ElInput
                    style="width: 120px"
                    placeholder="代理商"
                    options={[]}
                    clearable
                    v-model={formData.value.name}
                  ></ElInput>
                </ElFormItem>
                <el-button type="primary" onClick={onSearch}>
                  搜索
                </el-button>
                <el-button onClick={onReset}>重置</el-button>
              </div>
            </ElForm>
            <div class="agent-select-content">
              <div class="agent-select-list">
                <mj-table
                  ref={tableRef}
                  border
                  height="450px"
                  rowKey="id"
                  data={reqList.data}
                  columns={columns.value}
                  onSelectionChange={handleSelectionChange}
                ></mj-table>
              </div>

              <div class="agent-select-selected">
                <div class="title">
                  <span>已选中{multipleSelection.value.length || 0}家代理商</span>
                  <ElButton link type="primary" onClick={() => onClear()}>
                    清空选中
                  </ElButton>
                </div>
                <div>
                  {multipleSelection.value.map(item => {
                    return (
                      <span class="agent-select-tag">
                        <ElTag type="info" closable onClose={() => onClosable(item.id)}>
                          {(item as any).name}
                        </ElTag>
                      </span>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
          <div class="dialog-footer">
            <el-button onClick={onCancel}>取消</el-button>
            <el-button type="primary" onClick={onSave}>
              保存
            </el-button>
          </div>
        </ElDialog>
      </div>
    );
  },
});
