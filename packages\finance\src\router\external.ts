// import { RouteRecordRaw } from 'vue-router';
// import { RouterNameExternal } from './types';
// export const externalRoutes: RouteRecordRaw[] = [
// {
//   path: '/agentApply',
//   name: RouterNameExternal.agent_apply,
//   component: () => import('../modules/external/agentApply/index.vue'),
//   meta: {
//     name: '代理商入驻申请',
//   },
// },
// ].map(el => ({ ...el, path: '/external' + el.path }));
