import { FinanceOrderStatusEnum } from '@/enums/finance';

export interface FinanceBillParam {
  city?: string;
  province?: string;
  start_month?: string;
  agent_id?: number;
  agent_name?: string;
  end_month?: string;
  status?: FinanceOrderStatusEnum;
}

export interface FinanceBillModel {
  agent_id?: number;
  agent_name?: number;
  /** 通道费总金额 */
  channel_fee_amount?: number;

  /** 佣金返利总金额 */
  commission_rebate_amount?: number;
  end_date?: string;
  id?: number;
  /** 应付代理商总金额 */
  pay_mount?: number;
  start_date?: string;
  status?: FinanceOrderStatusEnum;
  update_time?: number;
  /** 核销订单总金额 */
  write_off_order_amount?: number;
  // 审核状态
  pay_status: 0 | 1;
  pay_status_name: string;
  /** 新增字段 */
  /** 订单实收金额 */
  order_pay_amount?: number;
  /** 技术服务费-品牌 */
  our_tech_service_amount?: number;
  /** 技术服务费-代理商 */
  agent_tech_service_amount?: number;
  /** 品牌分佣 */
  our_commission_amount?: number;
  /** 代理商分佣 */
  agent_commission_amount?: number;
  /** 订单分成保底补贴 */
  basic_allowance_amount?: number;
  /** 品牌佣金返利 */
  agent_rebate_amount?: number;
  /** 调账金额 */
  adjust_amount?: number;
  /** 美甲师分佣 */
  manicurist_earn_amount?: number;
  /** 品牌实际收入合计 */
  our_earn_amount?: number;
  /** 代理商实际收入合计 */
  agent_earn_amount?: number;
  pay_amount?: number;
  /** 账单状态name*/
  status_name?: string;
  /** 对公账号 */
  bank_account?: string;
  /** 开户地区 */
  bank_city?: string;
  /** 开户支行 */
  bank_name?: string;
  /** 打款时间 */
  paid_date?: string;
  /** 打款凭证 */
  paid_attachments?: string[];
  our_audit_remark?: string;
  our_audit_attachments?: string[];
  adjust_amount__yuan?: number | string;
}
