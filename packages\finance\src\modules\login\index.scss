.login-page.router-view {
  width: 100vw;
  height: 100vh;
  background: url('@/assets/icon-login-bg.jpg') no-repeat;
  background-size: 100% 100%;
  padding: 0;
  background-color: #e1e5f0;

  .el-input {
    --el-border-color: transparent;
    --el-text-color-placeholder: #afb0bd;
    width: 360px;

    .el-input__wrapper {
      background: #f7f8fa;
      border-radius: 6px;
      padding: 9px 12px;
    }
    .el-input__inner {
      font-size: 14px;
    }
  }
  .el-form-item {
    margin-bottom: 20px;
  }

  .login-left-warp {
    border-radius: 8px;
    width: 413px;
    height: 100%;
    // padding: 32px;
    display: flex;
    bottom: 0;
    top: 0;
    left: 115px;
    position: absolute;
    z-index: 0;

    .icon-text {
      background: url('@/assets/icon-login-text.png') no-repeat;
      background-size: 100% 100%;
      width: 310px;
      height: 89px;
      margin-top: calc(100vh - (100vh - 70px));
    }
  }

  .login-right-warp {
    box-sizing: border-box;
    width: 380px;
    height: 406px;
    background-color: #fff;
    padding: 32px 30px 52px 30px;
    display: flex;
    bottom: calc(50vh - 200px);
    right: 150px;
    position: absolute;
    background-image: linear-gradient(180deg, #e0e5fc 0%, #d3d9f300 100%);
    background-size: 100% 130px;
    background-repeat: no-repeat;
    box-shadow: 0 5px 20px 0 #0215381a;
    border-radius: 12px;

    .login-right-warp-form {
      width: 100%;
    }

    .login-right-warp-form_title {
      margin-bottom: 17px;
      text-align: left;
      color: #202f3f;
      font-weight: 600;
      font-size: 30px;
    }
    .login-right-warp-form_sub_title {
      margin-bottom: 31px;
      text-align: left;
      color: #202f3f;
      font-weight: 600;
      font-size: 20px;
      display: flex;
      .logo {
        margin-right: 6px;
        // width: 25px;
        // height: 19px;
        img {
          width: 25px;
          height: 19px;
        }
      }
    }

    .code-tip {
      margin-bottom: -10px;
      // color: var(--theme-color);
    }

    .vcode-btn {
      color: var(--theme-color);
      cursor: pointer;
      font-size: 14px;

      &.disabled {
        color: var(--el-text-color-disabled);
      }
    }

    .suffix-center {
      display: flex;
      align-items: center;
    }

    .login-footer {
      display: flex;
      justify-content: center;
      margin-top: 40px;
    }

    .submit-btn {
      width: 100%;
      height: 44px;
      font-size: 16px;
    }
  }
  &.is-mobile {
    @media only screen and (min-width: 100px) and (max-width: 480px) {
      .login-left-warp {
        display: none;
      }
      .login-right-warp {
        right: unset;
        left: 20px;
        width: calc(100% - 40px);
        max-width: 356px;
      }
    }
  }
}
