<template>
  <div style="border: 1px solid #ccc; width: 80%; border-radius: 4px">
    <Toolbar
      style="border-bottom: 1px solid #ccc; border-radius: 4px"
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      :mode="mode"
    />
    <Editor
      style="height: 350px; overflow-y: hidden"
      v-model="valueHtml"
      :defaultConfig="editorConfig"
      :mode="mode"
      @onCreated="handleCreated"
      @onChange="handleChange"
    />
  </div>
</template>
<script>
import '@wangeditor/editor/dist/css/style.css'; // 引入 css

import { onBeforeUnmount, ref, shallowRef, watch } from 'vue';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';

export default {
  components: { Editor, Toolbar },
  props: ['disabled', 'modelValue'],
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    // 编辑器实例，必须用 shallowRef
    const editorRef = shallowRef();

    // 内容 HTML
    const valueHtml = ref(props.modelValue);

    watch(
      () => props.modelValue,
      val => {
        valueHtml.value = props.modelValue;
        if (props.disabled) {
          editorRef.value.disable();
        }
      },
      { deep: true, immediate: true },
    );
    const toolbarConfig = {
      excludeKeys: [
        // 排除菜单组，写菜单组 key 的值即可
        'headerSelect',
        'italic',
        'group-more-style',
        'todo',
        'emotion',
        'insertTable',
        'codeBlock',
        'fullScreen',
        'group-video',
        'blockquote',
      ],
    };
    const editorConfig = {
      placeholder: '请输入内容...',
      MENU_CONF: {
        uploadImage: {
          server: '/api/resources/upload_file',
          meta: {
            type: 'any',
          },
          fieldName: 'file',
          // 自定义插入图片
          customInsert(res, insertFn) {
            if (res && res.data.source) {
              insertFn(res.data.source, '', res.data.source);
            }
          },
          headers: {
            Actcode: localStorage.getItem('Mj-Finance-Actcode'),
            Authorization: localStorage.getItem('Mj-Finance-Auth-Token'),
          },
        },
      },
    };

    // 组件销毁时，也及时销毁编辑器
    onBeforeUnmount(() => {
      const editor = editorRef.value;
      if (editor === null) return;
      editor.destroy();
    });

    const handleCreated = editor => {
      editorRef.value = editor; // 记录 editor 实例，重要！
    };

    const handleChange = editor => {
      emit('update:modelValue', editor.getHtml());
    };

    return {
      editorRef,
      valueHtml,
      mode: 'default', // 或 'simple'
      toolbarConfig,
      editorConfig,
      handleCreated,
      handleChange,
    };
  },
};
</script>
