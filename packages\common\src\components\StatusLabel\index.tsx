import { defineComponent } from 'vue';

export const StatusLabelColor = {
  /** 绿色 **/
  green: '#33BA5D',
  /** 灰色 **/
  gray: 'rgba(60, 82, 105, 0.6)',
  /** 红色 **/
  red: '#fe5e5e',
  /** 黄色 **/
  yellow: 'rgb(255, 183, 3)',
  /** 蓝色 */
  blue: '#446ff2',
  /** 四级字体颜色 **/
  textFourColor: '#cccccc',
  pink: '#ff3a9ab3',
};

const StatusLabel = defineComponent({
  props: {
    color: String,
  },
  render() {
    return (
      <span class="comp-status-label" style={this.color ? `--status-color:${this.color}` : null}>
        {this.$slots.default?.()}
      </span>
    );
  },
});

export const createEnumStatusLabel = (map: Map<any, any>, colors: Record<any, any>) => {
  return (value: any) => {
    const OriginalVal = value;
    let newVal = OriginalVal;
    let color;
    if (!map.has(newVal)) {
      newVal = '--';
    } else {
      color = colors[OriginalVal];
      if (color === undefined) color = colors['default'];
      newVal = map.get(newVal);
    }
    return <StatusLabel color={color}>{newVal}</StatusLabel>;
  };
};

export default StatusLabel;
