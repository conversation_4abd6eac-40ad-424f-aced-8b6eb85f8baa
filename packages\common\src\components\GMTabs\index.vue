<script src="./index.tsx"></script>
<style lang="scss" scoped>
.tabs-header-line {
  padding: 0 16px 0;
  flex: 1;
  background-color: #fff;
  border-top-right-radius: 12px;
  border-top-left-radius: 12px;
  .tabs-header-nav {
    border-bottom: 1px solid #d7dbe7;
    button {
      height: 40px;
      padding: 12px 10px;
      border: 0;
      font-size: 14px;
      font-weight: 500;
    }
    button:hover {
      background-color: rgb(255, 255, 255);
    }
  }
}

.tabs-content {
}
</style>
