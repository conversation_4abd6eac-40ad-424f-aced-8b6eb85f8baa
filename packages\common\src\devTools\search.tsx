import { defineComponent, ref, nextTick, onUnmounted, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
interface MenuOption {
  label: string;
  name: string;
  icon?: string;
  parent?: MenuOption;
}
interface CommandConfig {
  command: string;
  desc: string;
  func: any;
  type: 'command';
}

const openCurrentRouter = () => {
  const file =
    router.currentRoute.value.matched[router.currentRoute.value.matched.length - 1]?.components
      ?.default?.__file;
  if (file) fetch('/plugins/generate/open', { headers: { file } });
  else {
    ElMessage.error('未找到对应文件');
  }
};
const hasCommandConfig = (config: any): config is CommandConfig => config.type === 'command';
const commandMap: CommandConfig[] = [
  { command: 'open target', desc: '打开指定路由', type: 'command', func: () => {} },
  { command: 'open', desc: '打开当前路由', type: 'command', func: openCurrentRouter },
];

export default defineComponent({
  props: ['menus'],
  setup(props, ctx) {
    // const router = useRouter();
    const searchList = ref<MenuOption[][]>([]);
    const inputValue = ref('');
    const show = ref(false);
    const inputRef = ref<HTMLInputElement>();
    const defaultActive = ref(0);
    const onKeyPress = (e: KeyboardEvent) => {
      try {
        const target = e.target as HTMLElement;
        const key = e.key.toUpperCase();

        // 不显示状态
        if (show.value === false) {
          if (target.tagName === 'INPUT') return;
          if (key === 'F' && e.ctrlKey && e.shiftKey) {
            inputValue.value = '';
            searchList.value = [];
            show.value = true;
            nextTick(() => {
              inputRef.value?.focus();
            });
          }
          return;
        }
        // 显示状态
        if (key === 'ESCAPE') {
          show.value = false;
        } else if (key === 'ARROWUP') {
          defaultActive.value = Math.max(defaultActive.value - 1, 0);
        } else if (key === 'ARROWDOWN') {
          defaultActive.value = Math.min(defaultActive.value + 1, searchList.value.length - 1);
        } else if (key === 'ENTER') {
          const item = searchList.value[defaultActive.value];
          onClickItem(item);
        }
      } catch {}
    };
    const menus: any = [];

    onUnmounted(() => window.removeEventListener('keydown', onKeyPress));
    const getMenuDirectoriesToArray = menu => {
      const result: any[] = [];
      let tmp = menu;
      while (tmp) {
        result.unshift(tmp);
        tmp = tmp.parent;
      }
      return result;
    };
    const onInputChange = (e: Event) => {
      const input = e.target as HTMLInputElement;
      const value = input.value.trim().toLowerCase();
      if (value === '') {
        searchList.value = [];
        return;
      }

      const list: any[] = [];
      const findMap = new WeakSet();
      for (const menu of menus) {
        if (!menu.last) continue;
        if (!menu.pinyin?.includes(value) && !menu.label.includes(value)) continue;
        findMap.add(menu);
        const result = getMenuDirectoriesToArray(menu);
        list.push(result);
      }
      for (const cmd of commandMap) {
        if (cmd.command.includes(value) || cmd.desc.includes(value)) {
          list.unshift(cmd);
        }
      }
      searchList.value = list as any;
      defaultActive.value = 0;
    };

    const forEachItem = (items, parent) => {
      for (const item of items) {
        const { name, label, hidden, children, search } = item;
        const filterSourceChildren = children?.filter(t => t.hidden);
        const hasChildren = filterSourceChildren && filterSourceChildren.length > 0;
        const tmp = {
          name,
          label,
          hidden,
          parent,
          pinyin: window.pinyinPro.pinyin(label, { pattern: 'first' }).replace(/\s/g, ''),
          last: hasChildren || search,
        };
        menus.push(tmp);
        if (children) {
          forEachItem(children, tmp);
        }
      }
    };
    forEachItem(props.menus, null);

    const onClickItem = (item: MenuOption[]) => {
      if (!item) return;
      if (hasCommandConfig(item)) {
        item.func(inputValue.value);
      } else {
        window.router.push({
          name: item[item.length - 1].name,
        });
      }
      show.value = false;
    };
    onMounted(() => {
      window.addEventListener('keydown', onKeyPress);
    });
    ctx.expose({
      inputRef,
    });
    return () => {
      if (!show.value) return;
      return (
        <div class="search-box">
          <div class="search-body ">
            <div class="st-input">
              <el-icon>
                <Search />
              </el-icon>
              <input
                ref={inputRef}
                v-model={inputValue.value}
                inputmode="latin"
                placeholder="聚焦搜索"
                oninput={onInputChange}
              />
            </div>
            {searchList.value.length > 0 && (
              <div class="search-list">
                {searchList.value.map((item, index) => {
                  let list;
                  let iconMenu: any;
                  if (hasCommandConfig(item)) {
                    list = (
                      <>
                        <span class="disabled">{item.command}</span>
                        <span class="bar">-</span>
                        <span class="disabled">{item.desc}</span>
                      </>
                    );
                  } else {
                    list = item.map(menu => {
                      if (menu.icon)
                        iconMenu = (
                          <el-icon>
                            <Search />
                          </el-icon>
                        );
                      return (
                        <>
                          {menu.parent && <span class="bar">-</span>}
                          <span class="disabled">{menu.label}</span>
                        </>
                      );
                    });
                  }

                  return (
                    <div
                      class={['search-list-item', index === defaultActive.value ? 'active' : null]}
                      onclick={() => onClickItem(item)}
                      onmouseenter={() => {
                        defaultActive.value = index;
                      }}
                    >
                      {iconMenu}
                      {list}
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      );
    };
  },
});
