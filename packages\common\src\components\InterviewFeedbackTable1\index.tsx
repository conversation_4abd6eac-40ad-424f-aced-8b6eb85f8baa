import { defineComponent, PropType, reactive, ref, toRefs, Ref, computed, inject } from 'vue';
import { ElTable, ElInputNumber, ElSelect } from 'element-plus';
import { columnsList, mergeCells, formatColumnsList } from './config/index';
import { Select } from '../select/Select';
export default defineComponent({
  props: {
    list: {
      type: Array as unknown as PropType<Ref<any[]>>,
      default: () => [],
    },
    isEdit: {
      type: Boolean as PropType<Boolean>,
      default: true,
    },
    tableProps: {
      type: Object as PropType<Record<string, any>>,
    },
    scores: {
      type: Array as PropType<Record<string, any>[]>,
    },
    dynamicHead: {
      type: Array as any as PropType<Ref<any>>,
      // default: () => [],
    },
  },
  components: {
    ElTable,
    ElInputNumber,
    Select: Select as any,
  },
  setup(props, ctx) {
    const columns = computed(() => {
      return formatColumnsList('scores', props.dynamicHead?.value);
    });
    const mergeArr = computed(() => {
      return mergeCells(props.list.value);
    });
    const spanMethod = ({ row, column, rowIndex, columnIndex }) => {
      if (columnIndex === 0) {
        return mergeArr.value[rowIndex];
      }
    };

    const { isEdit } = toRefs(props);
    return () => (
      <div class="mj-interview-feedback-table">
        <el-table
          style="width: 100%"
          data={props.list.value}
          spanMethod={spanMethod}
          {...props.tableProps}
        >
          {columns.value.map((item, index) => {
            if (isEdit.value && item.prop === 'score') {
              return (
                <el-table-column {...item}>
                  {{
                    default: scope => {
                      return (
                        <Select
                          clearable={false}
                          v-model={scope.row.score}
                          placeholder="请选择"
                          options={props.scores}
                        ></Select>
                        // <el-input-number v-model={scope.row.score} min={1} max={5} size="small" />
                      );
                    },
                  }}
                </el-table-column>
              );
            } else if (!isEdit.value && item.prop === 'score') {
              return <el-table-column {...item} />;
            } else if (item.children) {
              return (
                <el-table-column {...item}>
                  {item.children.map(itm => {
                    return <el-table-column {...itm} />;
                  })}
                </el-table-column>
              );
            } else {
              return <el-table-column {...item} />;
            }
          })}
        </el-table>
      </div>
    );
  },
});
