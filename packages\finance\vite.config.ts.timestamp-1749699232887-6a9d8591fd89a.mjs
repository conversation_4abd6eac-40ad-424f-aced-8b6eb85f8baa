// vite.config.ts
import { defineConfig, loadEnv } from "file:///D:/Code/meijia-clearing/node_modules/.pnpm/vite@5.4.19_@types+node@22._a9e06e0785e96acc102733cd16c391e4/node_modules/vite/dist/node/index.js";
import { fileURLToPath, URL } from "node:url";
import vue from "file:///D:/Code/meijia-clearing/node_modules/.pnpm/@vitejs+plugin-vue@5.2.4_vi_99b47c9be1c18d3709c25afc2474cd0b/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///D:/Code/meijia-clearing/node_modules/.pnpm/@vitejs+plugin-vue-jsx@4.1._a27c71a1e0eec6c33526b12609be5a75/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import { PluginVueModuleManager } from "file:///D:/Code/meijia-clearing/packages/plugins/plugins-vue-module-manager/index.js";
import { PluginsRequestManager } from "file:///D:/Code/meijia-clearing/packages/plugins/plugins-request-manager/index.js";
import { PluginVueRouterManager } from "file:///D:/Code/meijia-clearing/packages/plugins/plugins-vue-router-manager/index.js";
var __vite_injected_original_import_meta_url = "file:///D:/Code/meijia-clearing/packages/finance/vite.config.ts";
var vite_config_default = ({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  return defineConfig({
    server: {
      host: "127.0.0.1",
      //自定义主机名
      allowedHosts: true,
      port: env.VITE_SERVER_PORT ? Number(env.VITE_SERVER_PORT) : 80,
      //自定义端口
      proxy: {
        "/api": {
          target: env.VITE_BASE_URL,
          changeOrigin: true
          // rewrite: (path) => path.replace(/^\/api/, ""),
        }
      }
    },
    plugins: [
      vue(),
      vueJsx(),
      PluginVueModuleManager(),
      PluginsRequestManager(),
      PluginVueRouterManager()
    ],
    resolve: {
      extensions: [".vue", ".tsx", ".ts", ".js"],
      alias: {
        "@": fileURLToPath(new URL("./src", __vite_injected_original_import_meta_url)),
        "~@": fileURLToPath(new URL("./src", __vite_injected_original_import_meta_url)),
        "@common": fileURLToPath(new URL("../../packages/common/src", __vite_injected_original_import_meta_url))
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          silenceDeprecations: ["legacy-js-api"]
        }
      }
    }
  });
};
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
