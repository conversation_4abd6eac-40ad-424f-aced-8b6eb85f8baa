import path from 'path';
import { readFile, writeFile } from 'fs/promises';
import { ensureDir, pathExists } from 'fs-extra';
const projectConfig = {
  permissionsSource: 'project/permissions.json',
  generateDir: '.generate',
};
const virtualFieldId = `@mjGenerate`;
const virtualFieldIdInternal = `\0${virtualFieldId}`;
const generateContentMap = new Map();
export const PluginVueModuleManager = () => {
  let viteConfig = {};

  const init = async () => {
    const { envDir } = viteConfig;
    await ensureDir(path.join(envDir, projectConfig.generateDir));
  };
  // 编译权限声明文件
  const buildPermissions = async () => {
    const { envDir } = viteConfig;
    const permissionsSource = path.join(envDir, projectConfig.permissionsSource);
    const permissionsTarget = path.join(envDir, projectConfig.generateDir, 'permissions.d.ts');
    let permissionsObject;
    if (await pathExists(permissionsSource)) {
      const file = await readFile(permissionsSource, {
        encoding: 'utf-8',
      });
      permissionsObject = JSON.parse(file);
    }

    const Code = ['export const RightCodeMap = {'];
    const statement = [
      `declare module '${virtualFieldId}' {`,
      `\texport declare const RightCodeMap: {`,
    ];
    if (permissionsObject) {
      permissionsObject.rules.forEach(item => {
        // 声明文件
        statement.push(`\t\t/** ${item.description} */\n\t\t${item.name}: number;`);
        Code.push(`${item.name} :${item.code} ,`);
      });
    }
    statement.push(`};\n}\n`);
    Code.push(`}`);
    await writeFile(permissionsTarget, statement.join('\n'));
    generateContentMap.set('permissions', Code.join('\n'));

    console.log('权限类型生成完成');
    // console.log('viteConfig', viteConfig);
  };

  return {
    name: 'mj-module-manager',
    async buildStart() {
      await init();
      await buildPermissions();
    },
    configResolved(config) {
      viteConfig = config;
    },
    configureServer(server) {
      server.watcher.on('change', async filePath => {
        if (filePath.includes('permissions.json')) {
          await buildPermissions();
          const md = server.moduleGraph.getModuleById(virtualFieldIdInternal);
          if (md) {
            server.moduleGraph.invalidateModule(md);
            server.ws.send({
              type: 'full-reload',
            });
          }
        }
      });
    },
    resolveId(id) {
      switch (id) {
        case virtualFieldId:
          return virtualFieldIdInternal;
      }
    },
    handleHotUpdate(ctx) {
      // console.log('handleHotUpdate', ctx.modules);
    },
    load(id) {
      switch (id) {
        case virtualFieldIdInternal:
          return generateContentMap.get('permissions') ?? '';
      }
    },
  };
};
