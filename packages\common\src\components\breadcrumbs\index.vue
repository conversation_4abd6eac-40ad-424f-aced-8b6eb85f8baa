<script src="./index.tsx"></script>
<style scoped lang="scss">
.mj-breadcrumbs-container {
  // background-color: #f5f5f7;
  // padding: 16px 22px 0;
  font-weight: 500;
  font-size: 16px;
  color: #000000;
  display: flex;
  align-items: center;

  .current-name {
    font-weight: 600;
  }

  .breadcrumbs {
    margin-left: 16px;
    color: #979797;
    font-size: 13px;

    a {
      color: #979797;
    }
  }

  .breakcrumb-link {
    font-weight: 400;
    cursor: pointer;

    &:hover {
      color: #2877ff;
    }
  }

  .breakcrumb-no-link {
    font-weight: 400;
  }
}
</style>
