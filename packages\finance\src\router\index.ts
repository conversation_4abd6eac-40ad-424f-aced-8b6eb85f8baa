import { createRouter, createWebHistory } from 'vue-router';
import { RouterNameCommon, RouterNameHome, RouterNameSettlement } from './types';
import { getToken } from '@/utils/token';
import { checkRoutes } from 'common';
// import { externalRoutes } from './external';
import { RightCodeMap } from '@mjGenerate';
import { useAuthStore } from '@/store';
import { ElMessage } from 'element-plus';

const routerConfig = Object.entries(import.meta.glob('@/router/modules/**/*.ts', { eager: true }))
  .map((it: any) => {
    return it[1].default;
  })
  .flat();

const routerInstance = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: checkRoutes([
    {
      path: '/',
      redirect: { name: RouterNameSettlement.manage },
      rights: [RightCodeMap.view_home, RightCodeMap.view_home],
    },
    // {
    //   path: '/home',
    //   name: RouterNameHome.home,
    //   component: () => import('../modules/home/<USER>'),
    //   meta: {
    //     hiddenBreadcrumbs: true,
    //     rights: [RightCodeMap.view_home, RightCodeMap.view_home],
    //   },
    // },
    {
      path: '/login',
      name: RouterNameHome.login,
      component: () => import('../modules/login/index'),
      meta: {
        hiddenBreadcrumbs: true,
      },
    },
    {
      path: '/debugger',
      name: 'debugger',
      component: () => import('@/modules/debugger'),
      meta: {},
    },
    {
      path: '/403',
      name: RouterNameCommon.noPermission,
      component: () => import('@/modules/403'),
      meta: {},
    },
    ...routerConfig,
    // ...externalRoutes,
  ]),
});
// 登录验证判断
const doLoginProcess = (to: any, _: any, next: any) => {
  // 登录页自动进入系统
  const token = getToken();

  if (token && to.name === RouterNameHome.login) {
    return next('/');
  }
  /**
   *  过滤不鉴定路径
   *  /external/login 外部登录
   *
   */
  const exclude = ['/external/', '/login', '/403'];
  for (let i = 0; i < exclude.length; i++) {
    if (to.path.indexOf(exclude[i]) >= 0) {
      return next();
    }
  }
  // 未登陆跳转登录页
  if (!token) {
    return next({
      path: '/login',
      query: { redirect: window.location.pathname + window.location.search },
    });
  }

  // // 已登录但无权限
  // const authStore = useAuthStore();
  // if (!authStore.userInfo?.right_codes?.includes(101000)) {
  //   return next({ path: '/403' });
  // }
  // 已登录但路由需要权限验证
  const authStore = useAuthStore();

  // 获取路由需要的权限码
  const requiredRights = to.meta?.rights || [];

  // 如果路由不需要特定权限则放行
  if (!requiredRights.length) {
    return next();
  }

  // 检查用户是否有任一所需权限
  const hasPermission = requiredRights.some(right =>
    authStore.userInfo?.right_codes?.includes(right),
  );

  if (!hasPermission) {
    return next({ path: '/403' });
  }

  next();
};
routerInstance.beforeEach(doLoginProcess);

window.router = routerInstance;
export default routerInstance;
