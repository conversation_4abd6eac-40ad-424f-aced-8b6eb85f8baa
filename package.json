{"name": "root", "private": true, "scripts": {"dev:finance": "pnpm -F finance dev", "build:finance": "pnpm -F finance build"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@types/node": "^22.7.5", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "5.33.1", "@typescript-eslint/parser": "5.33.1", "@vue/cli-plugin-typescript": "5.0.8", "@vue/eslint-config-typescript": "^9.1.0", "decimal.js": "^10.4.3", "fs-extra": "^11.2.0", "pinia": "^2.2.4", "pinia-plugin-persistedstate": "^4.1.1", "qrcode.vue": "^3.6.0", "sortablejs": "^1.15.6", "vue-router": "^4.4.5", "element-plus": "^2.8.4", "moment": "^2.30.1", "v-viewer": "^3.0.20", "viewerjs": "^1.11.6", "vue": "^3.5.10", "axios": "^1.7.7"}, "devDependencies": {"typescript": "^5.5.3", "typescript-eslint": "^8.13.0", "eslint": "8.22.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "prettier": "^2.4.1", "query-string": "^9.1.1", "vite": "^5.4.8", "vue-tsc": "^2.1.6", "@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "^4.0.1"}}