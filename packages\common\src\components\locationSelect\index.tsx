import { deepClone } from '../../utils/func';
import { defineComponent, ref } from 'vue';
import locationPicker from '../locationPicker/index.vue';

export default defineComponent({
  components: { locationPicker },
  name: 'MjLocationSelect',
  setup(props, ctx) {
    const addressLonLat = ref();
    const is_show = ref(false);
    const show = (data: any) => {
      addressLonLat.value = deepClone(data || {});
      is_show.value = true;
      formData.value = initFormData(data);
    };

    const onSaveBtnClick = () => {
      ctx.emit('submit', formData.value);
      is_show.value = false;
      ctx.emit('close');
    };
    const onCloseBtnClick = () => {
      is_show.value = false;
    };
    const formData = ref<any>(initFormData());

    function initFormData(data?: any) {
      return deepClone(data || {});
    }

    ctx.expose({
      show,
      onSaveBtnClick,
      onCloseBtnClick,
    });

    return () => (
      <div class="mj-add-store-page-container">
        {is_show.value && (
          <location-picker
            address={addressLonLat.value?.address}
            v-model={formData.value}
          ></location-picker>
        )}
      </div>
    );
  },
});
