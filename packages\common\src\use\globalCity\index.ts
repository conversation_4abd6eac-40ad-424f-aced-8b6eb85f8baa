import { computed, ref, watch } from 'vue';
import { useGlobalCityStore } from '../../store';
import { ICity } from '@common/types/city';
import { get_service_cities, get_service_agents_cities } from '@common/services';
import { getToken } from '@common/config';
import { ElMessage } from 'element-plus';
const DefaultCity = {
  id: undefined,
  name: '全部城市',
};
const cityDataLoading = ref(false);
const cityList = ref<ICity[]>([DefaultCity]);

const getCityList = (options: {
  city_type?: string;
  disableCache?: boolean;
  serviceCities?: any[];
  finally?: (cities: ICity[]) => void;
}) => {
  if (!getToken() || cityDataLoading.value) return cityList;
  if (!options.disableCache && cityList.value.length > 1) return cityList;
  const processResponse = data => {
    return (data || []).map(el => ({
      id: el.city_id,
      name: el.city_name,
    }));
  };

  cityDataLoading.value = true;
  let fetchCities: Promise<any>;
  if (options.serviceCities) {
    fetchCities = Promise.resolve({
      data: {
        data: options.serviceCities,
      },
    });
    console.log('fetchCities 更新了');
  } else {
    fetchCities =
      options.city_type !== 'agents' ? get_service_cities() : get_service_agents_cities();
  }
  fetchCities
    .then(res => {
      cityList.value = [DefaultCity];
      cityList.value.push(...(processResponse(res.data.data) || []));
    })
    .finally(() => {
      cityDataLoading.value = false;
      options?.finally?.(cityList.value);
    });
  return cityList;
};

export const useGlobalCity = (options?: {
  onCityChange?: (value: string | number | undefined, city: ICity | undefined) => void;
  city_type?: string;
  authStore?: any;
}) => {
  const reloadCityList = (reloadOption?: any) => {
    getCityList({
      ...(reloadOption || {}),
      city_type: options?.city_type,
      finally: cities => {
        const finder = cities.find(el => el.id === cityId.value);
        if (!finder) {
          ElMessage.warning({
            message: '您没有当前城市的查看权限，系统已自动切换到全部城市~',
          });
          setCityInfo(DefaultCity);
        }
      },
    });
  };
  reloadCityList();
  const globalCityStore = useGlobalCityStore();
  const cityId = computed(() => globalCityStore.cityId);
  const displayCityName = computed(() => globalCityStore.cityInfo.name || '全部城市');
  const cityInfo = computed(() => globalCityStore.cityInfo);
  const setCityInfo = (cityInfo: ICity) => globalCityStore.setCityInfo(cityInfo);

  watch(
    () => cityId.value,
    () => {
      options?.onCityChange?.(cityId.value, cityInfo.value);
    },
  );
  watch(
    () => [options?.authStore?.token, options?.authStore?.serviceCities],
    () => {
      if (options?.authStore?.token) {
        // getCityList({
        //   city_type: options?.city_type,
        //   serviceCities: options?.authStore.serviceCities,
        //   disableCache: true,
        // });
        reloadCityList({
          disableCache: true,
          city_type: options?.city_type,
          serviceCities: options?.authStore.serviceCities,
        });
      } else {
        cityList.value = [DefaultCity];
      }
    },
  );
  return {
    data: cityList,
    cityId,
    cityInfo,
    displayCityName,
    setCityInfo,
    reloadCityList,
  };
};
