import { RouterNameSyetem } from '@/router/types';
import { RightCodeMap } from '@/const/rightCodes';

export default [
  {
    path: '/system',
    children: [
      {
        path: '/system/bank',
        name: RouterNameSyetem.bank,
        component: () => import('@/modules/system/bank'),
        meta: {
          name: '银行管理',
          isKeepLive: true,
          rights: [RightCodeMap.bank_manage],
        },
      },
      {
        path: '/system/whiteList',
        name: RouterNameSyetem.whiteList,
        component: () => import('@/modules/system/whiteList'),
        meta: {
          name: '白名单管理',
          isKeepLive: true,
        },
      },
    ],
  },
];
