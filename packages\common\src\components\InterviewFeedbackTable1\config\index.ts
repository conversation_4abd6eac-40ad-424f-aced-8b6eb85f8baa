interface columnsListItem {
  prop: String;
  label: String;
  width?: String;
  children?: any[];
  align?: String;
}

export const columnsList: columnsListItem[] = [
  {
    prop: 'category',
    label: '内容',
    align: 'center',
  },
  {
    prop: 'item',
    label: '要求',
    align: 'center',
  },
  {
    prop: 'scores',
    label: '评分标准（每项最高5分）',
    align: 'center',
    children: [
      {
        prop: 'score1',
        label: '1分',
        align: 'center',
      },
      {
        prop: 'score3',
        label: '3分',
        align: 'center',
      },
      {
        prop: 'score5',
        label: '5分',
        align: 'center',
      },
    ],
  },
  {
    prop: 'score',
    label: '分值',
    align: 'center',
    width: '150px',
  },
];

export const formatColumnsList = (key: string, value: any) => {
  return columnsList.map(item => {
    if (item.prop === key) {
      return {
        ...item,
        children: value,
      };
    }
    return item;
  });
};

// 计算面试反馈表合并配置
export const mergeCells = data => {
  const mergeMap: Record<string, any> = {};
  data.forEach((item, index) => {
    const name = item.category;
    if (!mergeMap[name]) {
      mergeMap[name] = {
        start: index,
        count: 1,
      };
    } else {
      mergeMap[name].count++;
    }
  });
  const mergeArr: any[] = [];
  Object.values(mergeMap).forEach(({ start, count }) => {
    for (let i = 0; i < count; i++) {
      if (i === 0) {
        mergeArr.push([count, 1]);
      } else {
        mergeArr.push([0, 0]);
      }
    }
  });
  return mergeArr;
};
