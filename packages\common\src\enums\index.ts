import { EnumUtil, EnumValue } from '@common/utils/enum';

export class MJFileExportStatusEnum extends EnumUtil {
  static exporting = EnumValue(1, '导出中');
  static success = EnumValue(2, '导出成功');
}

/** 导出任务状态 **/
export class ExportTaskQueueStatus extends EnumUtil {
  static waiting = EnumValue(0, '待执行');
  static running = EnumValue(1, '执行中');
  static end = EnumValue(2, '已完成');
  static failed = EnumValue(3, '执行失败');
}

/** 对账单设置操作类型 **/
export class BrandsellerSettlementSettingOperatorType extends EnumUtil {
  static add = EnumValue(1, '新增');
  static edit = EnumValue(2, '编辑');
}

export class EnumPlatformsChannel extends EnumUtil {
  static WeiXin = EnumValue(1, '微信小程序');
  static DouYin = EnumValue(2, '抖音团购');
  static MeiTuan = EnumValue(3, '美团');
}

/** 上下架状态 */
export class EnumActiveStatus extends EnumUtil {
  static Down = EnumValue(false, '下架');
  static Up = EnumValue(true, '上架');
}
/** 上下架状态 */
export class EnumActiveStatusNum extends EnumUtil {
  static Down = EnumValue(0, '下架');
  static Up = EnumValue(1, '上架');
}

export class EnumPointGoodsType extends EnumUtil {
  static Down = EnumValue(1, '优惠券');
}

export class EnumSource extends EnumUtil {
  static douyin = EnumValue(2, '抖音团购');
  static meituan = EnumValue(3, '美团');
  static xiaochengxu = EnumValue(1, '微信小程序');
}
/** 产品类型 */
export class EnumGoodsClass extends EnumUtil {
  static shape = EnumValue(1, '款式');
  static serve = EnumValue(2, '服务');
}

/** 轮播图启用状态 */
export class EnumCarouselActiveStatus extends EnumUtil {
  static serve = EnumValue(1, '启用');
  static shape = EnumValue(0, '停用');
}

/** 轮播图应用页面 */
export class EnumCarouselPagePosition extends EnumUtil {
  static home = EnumValue(1, '首页');
  static serve = EnumValue(2, '服务商品页');
}

/** 轮播图搜索类型 */
export class EnumCarouselSearchType extends EnumUtil {
  static search = EnumValue(1, '搜索');
  static specialSubjects = EnumValue(2, '专题页');
  static rank = EnumValue(3, '榜单');
}
/** 专题类型 */
export class EnumSpecialSubjectType extends EnumUtil {
  static normal = EnumValue(1, '普通专题');
  static new = EnumValue(2, '上新专题');
}
