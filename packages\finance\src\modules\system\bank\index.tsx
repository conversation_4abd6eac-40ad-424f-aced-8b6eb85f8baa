import { defineComponent, Fragment, onMounted, ref } from 'vue';
import { MJFormList } from '@common/components';

import { MjTableColumn } from '@common/types/vendor/column';
import { delete_bank, query_bank_list } from '@/services/bank';
import { useDialog, usePaginationV2, useRequest } from 'common';
import { ElMessage, ElMessageBox } from 'element-plus';
import edit from './dialog/edit.vue';

export default defineComponent({
  setup() {
    const initFormData = () => {
      return {
        name: '',
        code: '',
        full_name: '',
      };
    };

    // 表单数据
    const formData = ref(initFormData());

    // 表格列
    const columns = ref<MjTableColumn<any>[]>([
      {
        label: '银行简称',
        minWidth: 180,
        align: 'center',
        prop: 'name',
        showOverflowTooltip: true,
      },
      {
        label: '银行全称',
        minWidth: 180,
        align: 'center',
        prop: 'full_name',
        showOverflowTooltip: true,
      },
      {
        label: '大小额接入行行号',
        minWidth: 180,
        align: 'center',
        prop: 'code',
        showOverflowTooltip: true,
      },
      {
        label: '排序',
        minWidth: 180,
        align: 'center',
        prop: 'sort_code',
        showOverflowTooltip: true,
      },

      {
        label: '操作',
        minWidth: 120,
        align: 'center',
        formatter: row => {
          return (
            <Fragment>
              <el-button
                link
                type="primary"
                onClick={() => {
                  editDialog.show(row);
                }}
              >
                编辑
              </el-button>
              <el-button
                link
                type="primary"
                onClick={() => {
                  ElMessageBox.confirm('确认删除该银行，删除后不可恢复！', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                  })
                    .then(() => {
                      reqDelete.runAsync(row.id);
                    })
                    .catch(() => {});
                }}
              >
                删除
              </el-button>
            </Fragment>
          );
        },
      },
    ]);

    // 查询列表
    const reqList = usePaginationV2(query_bank_list, { manual: true });

    // 删除银行
    const reqDelete = useRequest(delete_bank, {
      manual: true,
      onSuccess(_, oData) {
        ElMessage.success((oData as any).message);
        reqList.reload();
      },
    });

    // 查询
    const onQuery = () => {
      reqList.runAsync(formData.value);
    };

    // 重置
    const reset = () => {
      formData.value = initFormData();
      onQuery();
    };

    const editDialog = useDialog({
      component: edit,
      title: '编辑',
      width: 400,
      okText: '确定',
      on: {
        submit() {
          onQuery();
        },
      },
    });

    onMounted(async () => {
      await onQuery();
    });

    return () => (
      <div class="page-container">
        <MJFormList onSearch={onQuery} onReset={reset}>
          {{
            default: () => [
              <el-form-item style="width: 144px">
                <el-input clearable placeholder="简称" vModel_trim={formData.value.name} />
              </el-form-item>,
              <el-form-item style="width: 144px">
                <el-input clearable placeholder="全称" vModel_trim={formData.value.full_name} />
              </el-form-item>,
              <el-form-item style="width: 144px">
                <el-input
                  clearable
                  placeholder="大小额接入行行号"
                  vModel_trim={formData.value.code}
                />
              </el-form-item>,
            ],
          }}
        </MJFormList>
        <section class="table-section">
          <mj-table
            height="100%"
            data={reqList.data}
            columns={columns.value}
            pagination={reqList.pagination}
          ></mj-table>
        </section>
      </div>
    );
  },
});
