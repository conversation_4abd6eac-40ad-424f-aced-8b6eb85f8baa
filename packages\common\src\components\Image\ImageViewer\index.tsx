import { createApp } from "vue";
import Component from "./View.vue";

export default {
  show(urls: string[], index = 0) {
    const appendRoot = document.createElement("div");
    appendRoot.className = "ImageViewRoot";
    document.body.appendChild(appendRoot);
    const app = createApp(Component, {
      onClose() {
        document.body.removeChild(appendRoot);
        app.unmount();
      },


    });
    const vm = app.mount(appendRoot) as any;
    // const instance = app._instance?.proxy as { show?: (...args) => void };
    vm?.show?.(urls, index);
  },
};
