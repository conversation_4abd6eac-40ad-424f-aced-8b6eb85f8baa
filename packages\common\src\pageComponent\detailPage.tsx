import { defineComponent } from 'vue';
import { transformVue2Slots } from '@common/use/form';

export default defineComponent({
  emits: ['save', 'cancel', 'middle'],
  props: [
    'bodyClass',
    'showMidButton',
    'showOk',
    'showCancel',
    'footer',
    'okText',
    'midText',
    'cancelText',
  ],
  setup(props, context) {
    const ctx = transformVue2Slots(context);
    const emitSave = () => ctx.emit('save');
    const emitCancel = () => ctx.emit('cancel');
    const emitMiddle = () => ctx.emit('middle');
    return () => {
      const { footer = true, showOk = true, showCancel = true } = props;
      return (
        <div class={`mj-dialog-detail ${footer ? 'footer' : ''}`}>
          <div class={`mj-dialog-body ${props.bodyClass ?? ''}`}>{ctx.slots?.default?.()}</div>
          {footer && (
            <div class="mj-dialog-footer">
              {showCancel && (
                <el-button onClick={emitCancel}>{props.cancelText || '取消'}</el-button>
              )}
              {props.showMidButton && (
                <el-button onClick={emitMiddle}>{props.midText || '预览'}</el-button>
              )}
              {ctx.slots?.custom && ctx.slots?.custom?.()}
              {showOk && (
                <el-button type="primary" onClick={emitSave}>
                  {props.okText || '提交'}
                </el-button>
              )}
            </div>
          )}
        </div>
      );
    };
  },
});
