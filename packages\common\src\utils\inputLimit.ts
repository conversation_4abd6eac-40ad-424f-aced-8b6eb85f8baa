import { REG_REMOVE_PREFIX_ZERO } from '../const/regexp';
import { FD } from '@common/utils/formatData';

/** 数据符号类型 */
enum DigitalType {
  /** 正数或负数 */
  both = 0,
  /** 只能是负数 */
  negativeOnly = 1,
  /** 只能是正数 */
  positiveOnly = 2,
}

const defaultDigitalLimitOptions = {
  digitalType: DigitalType.positiveOnly,
};

const InputLimit = {
  /**
   * 数字输入限制，支持整数、负数和正负数，支持自定义整数部分和小树部分的位数
   * @param value
   * @param options
   *  intLimit 整数部分位数限制，大于等于1，默认不限制
   *  decimalLimit 小数部分位数限制，大于等于0，默认不限制
   *  digitalType 数字类型，默认 positiveOnly，只能输入整数
   * @constructor
   */
  DigitalLimit(
    value: string,
    options: {
      intLimit?: number;
      decimalLimit?: number;
      digitalType?: DigitalType;
    },
  ) {
    const newOptions = { ...defaultDigitalLimitOptions, ...(options || {}) };
    const { intLimit, decimalLimit, digitalType } = newOptions;
    // 符号部分
    const symbolPart =
      digitalType === DigitalType.positiveOnly
        ? ''
        : digitalType === DigitalType.negativeOnly
        ? '-'
        : '-?';
    // 小数部分
    const decimalPart =
      decimalLimit === null || decimalLimit === undefined
        ? `(?:\\.\\d{0,})?`
        : decimalLimit <= 0
        ? ''
        : `(?:\\.\\d{0,${decimalLimit}})?`;
    // 整数部分
    const intPart =
      (intLimit || 0) <= 0 ? `(?:0|[1-9]\\d{0,})?` : `(?:0|[1-9]\\d{0,${(intLimit || 0) - 1}})?`;
    const reg = new RegExp(symbolPart + intPart + decimalPart, 'u');
    const execText = () => {
      return digitalType === DigitalType.positiveOnly
        ? value
            .replace(/[^.\d]+/gu, '')
            .replace(REG_REMOVE_PREFIX_ZERO, '')
            .replace(/^\.$/u, '0.')
        : value
            .replace(/[^-.\d]+/gu, '')
            .replace(REG_REMOVE_PREFIX_ZERO, '')
            .replace(/^-0(?=\d)/u, '-')
            .replace(/^-\.$/u, '-0.')
            .replace(/^\.$/u, '0.');
    };
    const result = (reg.exec(execText()) ?? [''])[0];
    return result;
  },
  /**
   * // 限制只能输入数字和小数
   * @param value
   * @param isNegative 是否负数
   * @constructor
   */
  IntergerAndDecimals(value: string, isNegative = false, decimal = 2) {
    const match = isNegative
      ? /-?(\d+)$|^-?\d+\.?\d{0,2}|^-$/.exec(value)
      : decimal === 1
      ? /(\d+)$|\d+\.?\d{0,1}/.exec(value)
      : /(\d+)$|\d+\.?\d{0,2}/.exec(value);
    return match ? match[0] : '';
    // return value.replace(/[^\d.]/g, '').replace(/(\..{2,2}).+/, '$1');
  },
  // 限制只能输入数字
  Interger(value: string) {
    if (String(value || '').indexOf('.') >= 0) {
      value = String(value || '').split('.')[0];
    }
    return FD.isEmpty(value) ? '' : value.replace(/[^\d]/g, '');
  },
  // 限制两个数之间
  NumberRange(min: number, max: number) {
    return (value: string): string => {
      const newValue: string = InputLimit.IntergerAndDecimals(value);
      if (newValue === '') return newValue;
      const num = Number(newValue);
      if (num < min) return min + '';
      else if (num > max) return max + '';
      else return newValue;
    };
  },
  // 千分位
  Thousands(value: string) {
    if (value === null || value === undefined || value === '') return value;
    value = value + '';
    const match = /^(\d+)(\.\d+$)?$/.exec(value + '');
    if (!match) return value;
    const leftValue = match[1];
    const rightValue = match[2];
    let newValue = leftValue.replace(/(\d{1,3})(?=(\d{3})+$)/g, sp => `${sp},`);
    if (rightValue !== undefined) {
      newValue = `${newValue}${rightValue}`;
    }
    return newValue;
  },
  // 限制 9 位整数, 2位小数
  NineIntergerAndDecimals(value: string) {
    // const result = (/(?:0|[1-9]\d{0,8})(?:\.\d{0,2})?/u.exec(
    //   value.replace(/[^.\d]+/gu, '').replace(REG_REMOVE_PREFIX_ZERO, ''),
    // ) ?? [''])[0];
    // return result;
    return InputLimit.DigitalLimit(value, {
      intLimit: 9,
      decimalLimit: 2,
    });
  },
  // 限制 8 位整数, 2位小数
  EightIntergerAndDecimals(value: string, intLimit = 8) {
    // const result = (/(?:0|[1-9]\d{0,7})(?:\.\d{0,2})?/u.exec(
    //   value.replace(/[^.\d]+/gu, '').replace(REG_REMOVE_PREFIX_ZERO, ''),
    // ) ?? [''])[0];
    // return result;
    return InputLimit.DigitalLimit(value, {
      intLimit: intLimit,
      decimalLimit: 2,
    });
  },
};

export default InputLimit;
