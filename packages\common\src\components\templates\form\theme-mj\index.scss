.mjt-formlist-search-bar {
  background: #ffffff;
  padding: 12px 16px 12px 16px;
  box-shadow: 0 2px 12px 0 #1e146a0d;
  border-radius: 10px;
  margin-bottom: 12px;
  // margin-bottom: 16px;
  &.semi-angle {
    --mj-formlist-semiangle: 0;
    box-shadow: none;
  }
  .mjt-formlist-form-item-container {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    > div {
      margin-top: 12px;
      &:first-child {
        margin-top: unset;
      }
    }
    .el-form-item {
      margin: 0;
      .el-select {
        .el-input--medium .el-input__inner {
          height: var(--el-element-medium-height) !important;
        }
        .el-tag {
          max-width: 130px;
        }
        &.long {
          .el-tag {
            max-width: 180px;
          }
        }
      }
    }
    .mjt-formlist-default-line {
      display: grid;
      grid-template-columns: 1fr;
      grid-gap: 12px;
      align-items: center;
      &.two {
        grid-template-columns: 1fr auto;
      }
    }
    .mjt-formlist-action-area {
      padding-left: 10px;
      .mjt-formlist-form-item-search-operation {
        display: flex;
        align-items: center;
        .mjt-formlist-reset {
          min-width: 36px;
          padding: 0;
          .mj-btn-content {
            display: flex;
            align-items: center;
            justify-content: center;
            column-gap: unset;
            svg {
              font-size: 14px;
            }
          }
        }
      }
    }
    .mjt-formlist-action-fill {
      flex: 1 1 0;
    }
    .mjt-formlist-filter-area {
      flex: 1 1 0;
      overflow: hidden;
      .mjt-formlist-filter-content {
        flex: 0 0 auto;
        overflow: hidden;
        width: initial;
      }
    }
    .mjt-formlist-filter-content,
    .mjt-formlist-extend_line {
      width: 100%;
      display: grid;
      grid-gap: 10px;
      .el-form-item__content {
        & > .el-input,
        & > .el-select {
          width: 100% !important;
        }
      }
    }

    .mjt-formlist-action-area,
    .mjt-formlist-filter-area,
    .mjt-formlist-expansion-zone {
      display: flex;
      align-items: center;
    }
    .mjt-formlist-action-area {
      flex-shrink: 0;
    }
  }
}
