<script src="./index.tsx"></script>
<style lang="scss" scoped>
.mj-import-approval {
  background-color: white;
  padding: 0 0 16px;
  min-height: 200px;

  .error-div {
    margin: -12px 24px 24px 18px;
    border: 0.5px solid var(--error-color);
    padding: 12px;
    border-radius: 4px;
    background-color: rgba(var(--error-rgb-color), 0.05);

    .tips-div {
      color: var(--text-color);
      font-size: var(--font-size-t3);

      .error-icon {
        color: var(--error-color);
        font-size: 14px;
        margin-right: 8px;
      }
    }

    .msg-div {
      font-size: var(--font-size-default);
      max-height: 200px;
      padding: 6px 8px 0 24px;
      color: var(--text-second-color);
      white-space: pre-wrap;
      overflow: auto;
      /*  text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 5;
        -webkit-box-orient: vertical;*/
    }
  }

  :deep(.upload-file-list) {
    .file-item {
      padding-left: 80px;
    }

    margin-bottom: 12px;
  }
}
</style>
