import { defineComponent, toRefs, computed } from 'vue';
import { ElSteps, ElStep } from 'element-plus';
import iconRight from '../../assets/icon-right.png';
export default defineComponent({
  name: 'RecruitStep',
  props: ['list', 'active', 'title', 'current'],
  emits: ['update:active', 'change'],
  components: {
    ElSteps,
    ElStep,
  },
  setup(props, { slots, emit }) {
    const { list, title, active, current } = toRefs(props);
    const renderList = computed(() => {
      return list.value.map((item, index) => {
        let icon;
        // 如果当前节点为状态2说明通过
        if (item.status === 2) {
          icon = 'CircleCheck';
          if (active.value === index) {
            icon = 'SuccessFilled';
          }
        }
        return {
          ...item,
          icon,
        };
      });
    });
    return () => (
      <div class="recruit-step">
        <div class="title">{title.value}</div>
        <div className="setp-wrapper">
          <el-steps active={active?.value}>
            {renderList?.value?.map((item, index) => (
              <el-step
                onClick={() => {
                  if (current.value < index) return;
                  emit('update:active', index);
                }}
                class={{ 'with-out-border': item.icon, cursor: current.value >= index }}
                icon={item.icon}
              >
                {{
                  icon: () => {
                    return (
                      <div
                        class={{
                          'diy-icon': true,
                          active: active.value === index,
                          complete: index < current.value,
                          'border-red': index <= current.value,
                        }}
                      >
                        {current.value > index && <img src={iconRight} />}
                        {item.title}
                      </div>
                    );
                  },
                }}
              </el-step>
            ))}
          </el-steps>
        </div>
      </div>
    );
  },
});
