// ! 固定行数显示
@mixin line-clamp($line: 1) {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: $line;
  word-break: break-all;
}

html {
  width: 100vw;
  height: 100vh;
}

body {
  width: 100%;
  height: 100%;
  overflow: hidden;
  overflow-x: auto;
  margin: 0;
  font-family: var(--tg-font-family);
}

#app {
  height: 100%;
  width: 100%;
  min-width: 1140px;
  display: flex;
  flex-direction: column;
  &.is-mobile {
    @media only screen and (min-width: 100px) and (max-width: 480px) {
      min-width: unset;
    }
  }
}

::-webkit-scrollbar-track,
::-webkit-scrollbar-track-piece {
  background-color: red;
}

::-webkit-scrollbar-track,
::-webkit-scrollbar-track-piece {
  background-color: transparent;
}

::-webkit-scrollbar-track {
  border-radius: 3px;
  background: transparent;
}

::-webkit-scrollbar-thumb {
  // background-image: linear-gradient(180deg, #646464, #646464);
  border-radius: 7px;
  background-color: rgba(241, 243, 244, 0.3) !important;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

div {
  box-sizing: border-box;
}

.line-clamp-1 {
  @include line-clamp(1);
}

.line-clamp-2 {
  @include line-clamp(2);
}

.line-clamp-3 {
  @include line-clamp(3);
}

$mgIdx: 32;
@while $mgIdx >= 0 {
  .mgl-#{$mgIdx} {
    margin-left: 1px * $mgIdx;
  }
  .mgr-#{$mgIdx} {
    margin-right: 1px * $mgIdx;
  }
  .mgt-#{$mgIdx} {
    margin-top: 1px * $mgIdx;
  }
  .mgb-#{$mgIdx} {
    margin-bottom: 1px * $mgIdx;
  }
  .pdl-#{$mgIdx} {
    padding-left: 1px * $mgIdx;
  }
  .pdr-#{$mgIdx} {
    padding-right: 1px * $mgIdx;
  }
  .pdt-#{$mgIdx} {
    padding-top: 1px * $mgIdx;
  }
  .pdb-#{$mgIdx} {
    padding-bottom: 1px * $mgIdx;
  }
  $mgIdx: $mgIdx - 2;
}

/** 修改 MessageBox.confirm 弹框样式 */
.el-overlay-message-box {
  .el-message-box {
    --el-messagebox-padding-primary: 14px;
    --el-messagebox-border-radius: 6px;
    padding: 24px;

    .el-message-box__btns {
      justify-content: center;
    }

    .el-message-box__container {
      justify-content: center;
    }
  }
}

/** 修改样式 */
* {
  --el-color-primary: var(--theme-color);
  --el-color-primary-light-3: var(--primary-hover-color);
  --el-color-primary-light-7: #7289f1;
  --el-color-primary-light-8: #464848;
  --el-color-primary-light-9: #eaedfd;
  --el-color-primary-dark-2: var(--theme-color);
  --el-table-header-bg-color: #e9edfd;
  --el-table-header-text-color: var(--text-color);
  --el-table-text-color: #000000;
  --el-button-active-border-color: transparent !important;
  --el-border-color: #d5d5de;
  --el-border-color-light: #f0f0f0;
  --el-border-color-lighter: #f0f0f0;
  --el-border-radius-base: 6px;
  --el-input-placeholder-color: #cccccc;
  --el-text-color-placeholder: #cccccc;
  --el-pagination-border-radius: 6px;
  --el-font-weight-primary: 400;
  --el-checkbox-font-size: var(--font-size-default);
  --el-component-size: var(--default-height);
  --el-font-size-base: var(--font-size-default);
  --font-size: var(--font-size-default);
  --el-font-size-large: 16px;
  --el-pagination-button-bg-color: #ffffff;
}

.el-button {
  --border-line-color: #979797;
  --el-text-color-regular: #5c5c67;
}

.el-radio,
.el-radio-group .el-radio {
  height: var(--default-height);
}

.el-input__inner,
.el-select,
.el-range-input {
  font-weight: 400;
}

.el-input__inner::placeholder {
  color: #cccccc;
}

.el-button--primary.is-link,
.el-button--primary.is-plain,
.el-button--primary.is-text {
  --el-color-primary: #0e5bf2e0;
}

button.el-button,
button.el-button.is-round {
  padding: 0 15px;
  height: var(--default-height);
}

.el-form {
  --el-component-size: var(--default-height);

  .el-form-item.is-required:not(.is-no-asterisk).asterisk-left
    > .el-form-item__label-wrap
    > .el-form-item__label:before,
  .el-form-item.is-required:not(.is-no-asterisk).asterisk-left > .el-form-item__label:before {
    margin-right: 0;
  }

  .el-form-item {
    --font-size: var(--font-size-default);
    font-size: var(--font-size-default);
    margin-bottom: 16px;
  }

  .el-form-item--label-top .el-form-item__label {
    margin-bottom: 6px;
  }

  .el-form-item__content {
    line-height: var(--default-height);
  }

  .el-form-item__label {
    line-height: var(--default-height);
    height: var(--default-height);
    --el-text-color-regular: var(--text-second-color);
  }

  .el-select__wrapper {
    min-height: var(--default-height);
    font-size: var(--font-size-default);
    padding: 0 12px;
  }
}

.el-table,
.el-table.el-table--fit,
.mj-table {
  border-radius: 12px;
  border-radius: 0px;
  // border: solid 1px var(--el-table-border-color);
  font-size: var(--font-size-default);
  --el-table-row-hover-bg-color: #f7fafe;
  --el-table-current-row-bg-color: #e6f6ff;
  --el-table-text-color: #1a1a1a;

  th.el-table__cell {
    background-color: #e9edfd; // 表头背景色
    color: #1c1f2e; // 表头文字颜色
    font-weight: 500;
    // border-radius-top-left: 2px;
  }

  // 斑马纹样式
  &.el-table--striped {
    .el-table__body {
      tr.el-table__row--striped {
        td {
          background-color: #f4f6fe; // 斑马纹背景色
        }

        &:hover td {
          background-color: #eaf2fe; // 悬停时的背景色
        }
      }
    }
  }
  // 移除单元格边框
  .el-table__cell {
    border: none !important;
  }

  // &.el-table--fit .el-table__inner-wrapper:before {
  //   width: 0;
  // }

  // .el-table__column-resize-proxy {
  //   border: none;
  // }

  // .el-table__border-bottom-patch, .el-table__border-left-patch, .el-table__border-right-patch {
  //   background-color: white;
  // }

  // &.el-table--border {
  //   .el-table__inner-wrapper:after {
  //     height: 0;
  //   }
  // }

  // &.el-table--border:after {
  //   width: 0;
  // }

  // tr:last-child {
  //   .el-table__cell {
  //     border-bottom: none;
  //   }
  // }

  // tr {
  //   td:last-child {
  //     border-right: none;
  //   }
  // }

  .el-button,
  .el-button.is-round {
    &.is-link {
      height: 20px;
      padding: 0 2px !important;
    }
  }

  .descending .sort-caret.descending,
  .ascending .sort-caret.ascending {
    --theme-color: #ff3a9a;
  }

  .el-table__row .el-table__cell,
  .el-table__header .el-table__cell,
  thead {
    --el-table-text-color: #1a1a1a;
    padding: 5px 0;

    .cell {
      padding: 0 10px;
    }
  }
}

.el-overlay-message-box {
  .el-message-box {
    --el-messagebox-padding-primary: 14px;
    --el-messagebox-border-radius: 6px;
    --el-messagebox-width: 400px;
    padding: 24px;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: -20px;
      right: -30px;
      width: 136px;
      height: 100px;
      background-image: linear-gradient(243deg, #82b8f800 0%, #446ff2 85%);
      filter: blur(31px);
      opacity: 0.2;
    }
  }
}
.el-dialog__footer .footer-buttons {
  text-align: center;
}

.el-table .el-table__row .el-table__cell,
.el-table .el-table__header .el-table__cell,
.el-table thead {
  color: var(--el-table-text-color);
  padding: 6px 0;
  line-height: 22px;
  font-weight: 400;
}

.el-table thead th,
.el-table .el-table__header .el-table__cell {
  font-weight: 500;
}

.el-table .el-table__cell .cell {
  padding: 0 12px;
  line-height: 22px;
}

.el-table th.el-table__cell {
  background: var(--el-table-header-bg-color);
}

.el-table-container .el-pagination .el-input,
.el-pagination .el-input {
  --el-input-height: var(--default-height);

  .el-input__wrapper {
    border: 1px solid var(--el-input-border-color);
    box-shadow: none !important;
  }
}

.el-button.is-link:hover {
  --el-button-hover-link-text-color: #0345c6;
}

.el-dialog__headerbtn .el-icon.el-dialog__close {
  font-size: 24px;
}

/** 定义公用字体 */
@font-face {
  font-family: 'D-DIN';
  font-display: swap;
  src: url('https://panda-jojo-brandseller.oss-cn-hangzhou.aliyuncs.com/static/fonts/D-DIN/D-DIN.woff2')
      format('woff2'),
    url('https://panda-jojo-brandseller.oss-cn-hangzhou.aliyuncs.com/static/fonts/D-DIN/D-DIN.woff')
      format('woff'),
    /* chrome、firefox */
      url('https://panda-jojo-brandseller.oss-cn-hangzhou.aliyuncs.com/static/fonts/D-DIN/D-DIN.ttf')
      format('truetype');
  font-weight: normal;
  unicode-range: U+2d-39;
}

@font-face {
  font-family: 'D-DIN';
  font-display: swap;
  src: url('https://panda-jojo-brandseller.oss-cn-hangzhou.aliyuncs.com/static/fonts/D-DIN/D-DIN-Bold.woff2')
      format('woff2'),
    url('https://panda-jojo-brandseller.oss-cn-hangzhou.aliyuncs.com/static/fonts/D-DIN/D-DIN-Bold.woff')
      format('woff'),
    /* chrome、firefox */
      url('https://panda-jojo-brandseller.oss-cn-hangzhou.aliyuncs.com/static/fonts/D-DIN/D-DIN-Bold.ttf')
      format('truetype');
  font-weight: bold;
  unicode-range: U+2d-39;
}

.D-DIN {
  font-family: D-DIN;
}

// 全局css 加上以下代码，可以隐藏上下箭头

// 取消input的上下箭头
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

input::-webkit-outer-spin-button {
  -webkit-appearance: none !important;
}

input[type='number'] {
  -moz-appearance: textfield;
}

.el-input.el-input-group--append .el-input-group__append {
  padding: 0 10px;
}

.item-no-margin {
  .el-form-item {
    margin: 0;
  }
}

.el-message-box__input {
  margin-bottom: 24px;
}

body,
button {
  font-family: var(--tg-font-family);
}

.el-pagination {
  position: relative;
  display: flex;
  --pagination-height: 30px;
  background-color: #fff;
  margin: unset;
  border-radius: 0 0 12px 12px;
  padding: 16px 16px 0;
  justify-content: center;
  align-items: center;
  --el-pagination-button-width: 30px;
  --el-pagination-button-height: 30px;
  --el-pagination-font-size: var(--font-size-default);
  height: 48px;

  .el-pager li,
  &.is-background .btn-next,
  &.is-background .btn-prev {
    border: 1px solid #d8d8d8;
    border-radius: 6px;
  }

  &.is-background .btn-next.is-active,
  &.is-background .btn-prev.is-active,
  &.is-background .el-pager li.is-active,
  &.is-background .el-pager li.is-active.number {
    background: #ff3a9a0d;
    border: 1px solid #ff3a9a;
    border-radius: 6px;
    padding: 0 8px;
    color: #ff3a9a;
  }

  .el-input {
    min-height: var(--default-height);
  }

  .el-input__inner {
    --el-input-inner-height: calc(var(--default-height) - 2px);
  }

  .el-select {
    --el-select-input-font-size: var(--font-size-default);

    .el-select__selected-item {
      font-size: var(--font-size-default);
    }

    .el-select__wrapper {
      min-height: var(--default-height);
    }
  }

  .el-input__wrapper {
    box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) !important;
  }

  .el-pagination__total {
    position: unset;
    margin-right: auto;
    margin-left: 20px;
    color: var(--text-color);
  }

  span.el-pagination__jump {
    margin-right: 20px;
    border: 1px solid #d7dbe7;
    overflow: hidden;
    border-radius: 6px;
    color: var(--text-color);
    padding: 0 10px;
    display: flex;
    line-height: var(--pagination-height);
    height: var(--pagination-height);
    margin-left: auto;

    &.is-last {
      border: none !important;
    }
  }
}

.el-switch.el-switch--small .el-switch__core {
  min-width: 28px;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track,
::-webkit-scrollbar-track-piece {
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  background-image: linear-gradient(180deg, #dbdbdb 0%, #dbdbdb 100%);
  border-radius: 7px;
}

::-webkit-scrollbar-thumb:hover {
  background: #cccccc;
}

// 添加新的字体定义
@font-face {
  font-family: 'PuHuiTi';
  font-display: swap;
  src: url('./assets/font/Alibaba_PuHuiTi_2.0_55_Regular_55_Regular.ttf') format('truetype');
  font-weight: normal;
}

@font-face {
  font-family: 'PuHuiTi';
  font-display: swap;
  src: url('./assets/font/Alibaba_PuHuiTi_2.0_65_Medium_65_Medium.ttf') format('truetype');
  font-weight: Medium;
}
@font-face {
  font-family: 'PuHuiTi';
  font-display: swap;
  src: url('./assets/font/Alibaba_PuHuiTi_2.0_75_SemiBold_75_SemiBold.ttf') format('truetype');
  font-weight: bold;
}

// 使用新的字体
:root {
  font-family: PuHuiTi, Microsoft Yahei, Microsoft Sans Serif, Hiragino Sans GB, Tahoma;
}
body,
button {
  font-family: 'PuHuiTi';
}
