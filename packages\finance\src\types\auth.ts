/**
 * auth
 * <AUTHOR> <<EMAIL>>
 * @since   2021-01-04 09:56:35
 */

/**
 * 获取图片验证码响应
 * <AUTHOR> <<EMAIL>>
 * @since   2021-01-04 10:20:24
 */
export interface VerifyCodeResponse {
    /** actcode */
    actcode: string;
    /** 图片验证码 */
    img: string;
}

/**
 * 发动短信验证码请求参数
 * <AUTHOR> <<EMAIL>>
 * @since   2021-01-04 13:23:56
 */
export interface SMSVerifyCodeParams {
    phone: string;
    usercode: string;
}

/**
 * 登录参数
 * <AUTHOR> <<EMAIL>>
 * @since   2021-01-04 13:15:17
 */
export interface LoginParams {
    mobile: string;
    code: string;
}

/**
 * 修改密码参数
 * <AUTHOR> <<EMAIL>>
 * @since   2021-04-12 14:39:19
 */
export interface UpdatePasswordParams {
    /** 旧密码 */
    password: string;
    /** 新密码 */
    new_password: string;
}
