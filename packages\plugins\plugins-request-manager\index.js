import { join } from 'path';
import { readFile, writeFile } from 'fs/promises';
import { ensureDir, pathExists } from 'fs-extra';
import { parseOpenApi } from './parseOpenApi.js';
let projectConfigFilename = 'project.config.json';
let projectConfig;
const virtualFieldId = `@mjGenerate/request`;
const virtualFieldIdInternal = `\0${virtualFieldId}`;
const generateContentMap = new Map();

export const PluginsRequestManager = () => {
  let viteConfig;

  const init = async () => {
    console.log('初始化接口生成');
    const { envDir } = viteConfig;
    const config = await readFile(join(envDir, projectConfigFilename));
    projectConfig = JSON.parse(config);
    await ensureDir(join(envDir, projectConfig.projectDir));
    await ensureDir(join(envDir, projectConfig.generate));
  };
  // 编译权限声明文件
  const build = async () => {
    const { envDir } = viteConfig;
    const permissionsSource = join(
      envDir,
      projectConfig.projectDir,
      projectConfig.request.sourceName,
    );
    const permissionsTarget = join(envDir, projectConfig.generate, 'request.d.ts');
    await pathExists(permissionsSource);
    const file = await readFile(permissionsSource, {
      encoding: 'utf-8',
    });
    const permissionsObject = JSON.parse(file);
    const parseResult = parseOpenApi(permissionsObject);
    const Code = [`import { request, ObjectFilterEmpty } from 'common';\n`];
    const statement = [
      `declare module '${virtualFieldId}' {
  import { HttpResponse, ListResponse, IGPageQuery } from 'common/src/types/base/http';`,
    ];
    statement.push(...parseResult.declareCode);
    statement.push(`}\n`);

    Code.push(parseResult.sourceCode.join('\n'));
    await writeFile(permissionsTarget, statement.join('\n'));
    generateContentMap.set('cache', Code.join('\n'));
    // const testTarget = join(envDir, projectConfig.generate, 'test.tst');
    // await writeFile(testTarget, generateContentMap.get('cache'));

    console.log('类型生成完成');
    // throw new Error('调试终止..............');
    // console.log('viteConfig', viteConfig);
  };

  return {
    name: 'mj-request-manager',
    async buildStart() {
      await init();
      await build();
    },
    configResolved(config) {
      viteConfig = config;
    },
    configureServer(server) {
      server.watcher.on('change', async filePath => {
        if (filePath.includes(projectConfig.request.sourceName)) {
          await build();
          const md = server.moduleGraph.getModuleById(virtualFieldIdInternal);
          if (md) {
            server.moduleGraph.invalidateModule(md);
            server.ws.send({
              type: 'full-reload',
            });
          }
        }
      });
    },
    resolveId(id) {
      switch (id) {
        case virtualFieldId:
          return virtualFieldIdInternal;
      }
    },
    handleHotUpdate(ctx) {
      // console.log('handleHotUpdate', ctx.modules);
    },
    load(id) {
      switch (id) {
        case virtualFieldIdInternal:
          return generateContentMap.get('cache');
      }
    },
  };
};
