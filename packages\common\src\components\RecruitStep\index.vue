<script src="./index.tsx"></script>
<style lang="scss" scoped>
.recruit-step {
  // max-height: 90px;
  .title {
    width: 56px;
    height: 16px;
    font-size: 14px;
    font-family: -700;
    font-weight: bold;
    color: #333333;
    margin-bottom: 24px;
  }
  :deep(.setp-wrapper) {
    max-height: 90px;
    padding: 0 45px;
    margin: 0 90px;
    padding-bottom: 30px;
    overflow: hidden;
    .el-step__line {
      background-color: rgba(164, 178, 194, 0.3) !important;
    }
    .el-step__line-inner {
      border-color: rgba(164, 178, 194, 0.3) !important;
    }
    .is-finish {
      color: #999 !important;
      height: 48px !important;
    }
    .el-step__head {
      height: 48px !important;
    }
    .el-step__icon {
      border: 2px solid rgb(52, 158, 250) !important;
      color: rgb(52, 158, 250) !important;
    }
    .el-step__icon.is-icon {
      border: none !important;
      width: 64px;
      height: 48px;
      border: 0.5px solid #ff3c9f;
      border-radius: 50%;
    }
    .el-steps.el-steps--horizontal {
      height: 48px !important;
    }
    .cursor {
      cursor: pointer;
    }
    .is-process {
      .is-text {
        background-color: rgb(52, 158, 250) !important;
        color: #fff !important;
      }
    }
    .el-step__line {
      top: 50% !important;
      transform: translateX(-1px) !important;
      height: 1px !important;
      .el-step__line-inner {
        width: 30px;
        height: 1px;
        border: 0.5px solid #979797;
      }
    }
    .diy-icon {
      width: 48px;
      height: 48px;
      border: 0.5px solid #979797;
      border-radius: 50%;
      font-family: PingFangSC-Regular;
      font-weight: 400;
      font-size: 13px;
      color: #666666;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      background: #fff;
      img {
        width: 15px;
        height: 12px;
      }
    }
    .diy-icon.active {
      background: #fff1f9;
    }
    .diy-icon.complete {
      display: flex;
      img {
        margin-bottom: 3px;
      }
    }
    .diy-icon.border-red {
      border: 0.5px solid #ff3c9f;
      color: #ff3c9f !important;
    }
  }
}
</style>
