import './index.scss';
import { defineComponent, nextTick, PropType, ref, VNode } from 'vue';
import EmptyCommon from '@common/components/empty/emptyCommon.vue';
import MJFormList from '../../form/theme-mj';
// import { FormInstance } from 'element-plus';
import { MjTableColumn } from '@common/types/vendor/column';
import { usePagination, useRequest } from '@common/utils/requestHooks';
import { emptyFunc } from '@common/utils/func';

interface IAnyFunc {
  (...args: any): any;
}

export interface ITemplateConfig {
  /** 初始时,是否自动调用 接口拉取数据 **/
  auto?: boolean;
  /** 点击重置按钮调用 **/
  reset?: IAnyFunc;
  /** 禁用重置后重新请求 **/
  disabledResetRequest?: boolean;
  /** 传递给内部table组件的参数 */
  table?: Record<string, unknown> & {
    rowClick?: IAnyFunc;
    cellDblclick?: IAnyFunc;
    summaryMethod?: IAnyFunc;
    showSummary?: boolean;
    selectionChange?: IAnyFunc;
    select?: IAnyFunc;
    selectAll?: IAnyFunc;
    hidePagination?: boolean;
    cellStyle?: IAnyFunc;
    spanMethod?: IAnyFunc;
    rowKey?: IAnyFunc;
    pageSizes?: number[];
    tableHeaderColor?: IAnyFunc;
    key?: string;
  };
  /** 导出附加的参数*/
  exportParams?: Record<string, any>;
  /** 是否显示导出按钮*/
  showExport?: boolean;
  /** 导出的URL */
  exportURL?: string | IAnyFunc;
  export?: IAnyFunc;
  emptyText?: string | undefined;
  /** 开始搜索前调用的函数,传递搜索参数,需要返回修改后的参数 */
  searchBefore?: IAnyFunc;
  /** 自定义搜索函数, 调用前还是会调用 searchBefor */
  search?: IAnyFunc;
  layoutOnResize?: boolean;
  [key: string]: any;
}

type ServiceType = PropType<ReturnType<typeof usePagination> | ReturnType<typeof useRequest>>;
const ListMJTemplate = defineComponent({
  name: 'ListMJTemplate',
  components: {
    MJFormList,
  },
  props: {
    /** 绑定表格的列 **/
    columns: {
      type: Array as PropType<MjTableColumn<any>[]>,
      default: () => [],
    },
    /** 绑定表格的数据**/
    value: {
      type: Object as any,
    },
    /** 顶部面包屑的配置 **/
    routes: {},
    service: {
      type: Object as ServiceType,
    },
    config: {
      type: Object as PropType<ITemplateConfig>,
      default: () => ({}),
    },
    semiAngle: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, ctx) {
    // const formRef = ref<FormInstance>();
    const { reset } = props.config || {};
    const reqData = props.service as any;
    const search = () => {
      let value = props.value;
      if (props.config.searchBefore) {
        value = props.config.searchBefore(value);
      }
      if (props.config.search) {
        return props.config.search(value);
      } else if (reqData.name === 'useRequest') {
        return reqData.runAsync(value);
      } else {
        return reqData?.pagination.reQuery(value);
      }
    };
    const onReset = () => {
      if (reset) reset();
      if (!props.config.disabledResetRequest) nextTick(search);
    };
    const onOtherBtn = () => {
      if (props.config.onOtherBtn) {
        props.config.onOtherBtn();
        nextTick(search);
      }
    };
    if (props.config?.auto) {
      search();
    }

    const tableRef = ref<any>();

    return () => {
      const bodyBorderStyle =
        props.semiAngle && !ctx.slots.default?.() && !ctx.slots.otherBtns?.()
          ? 'border-top-left-radius: 0; border-top-right-radius: 0'
          : '';
      let bodyContainer: VNode | VNode[] | undefined = ctx.slots.bodyContainer?.() as any;
      if (!bodyContainer) {
        const table = ctx.slots.table ? (
          ctx.slots.table?.()
        ) : (
          <mj-table
            ref={tableRef}
            layoutOnResize={props.config?.layoutOnResize}
            stripe={props.config?.table?.stripe === undefined ? true : props.config?.table?.stripe}
            key={props.config?.table?.key}
            border={props.config?.table?.border}
            v-loading={reqData?.loading}
            columns={props.columns}
            height={'100%'}
            row-key={props.config?.table?.rowKey}
            header-cell-style={props.config.table?.tableHeaderColor}
            cell-style={(...args: any) => {
              return props.config?.table?.rowClick
                ? {
                    cursor: 'pointer',
                    ...(props.config.table?.cellStyle?.(...args) || {}),
                  }
                : { ...(props.config.table?.cellStyle?.(...args) || {}) };
            }}
            data={reqData?.data}
            pagination={
              props.config.table?.hidePagination
                ? undefined
                : {
                    ...((reqData as any)?.pagination || {}),
                    ...(props.config.table?.pageSizes?.length
                      ? { page_sizes: props.config.table?.pageSizes }
                      : {}),
                  }
            }
            onSelect={props.config?.table?.select}
            onSelect-all={props.config?.table?.selectAll}
            onSelection-change={props.config?.table?.selectionChange ?? emptyFunc}
            show-summary={props.config?.table?.showSummary}
            summary-method={props.config?.table?.summaryMethod}
            onrow-click={props.config?.table?.rowClick ?? emptyFunc}
            oncell-dblclick={props.config?.table?.cellDblclick ?? emptyFunc}
            span-method={props.config?.table?.spanMethod}
          >
            <div class="tg-page-empty" slot="empty">
              <EmptyCommon detail-text={props.config?.emptyText || '暂无数据'} />
            </div>
          </mj-table>
        );
        bodyContainer = (
          <div class="mjt-list-body-container" style={bodyBorderStyle}>
            {ctx.slots.btnLine && <div class="mjt-list-btn-line">{ctx.slots.btnLine?.()}</div>}
            <div class="mjt-list-table-container">{table}</div>
          </div>
        );
      } else {
        bodyContainer = (
          <div class="mjt-list-body-container" style={bodyBorderStyle}>
            {ctx.slots.btnLine && <div class="mjt-list-btn-line">{ctx.slots.btnLine?.()}</div>}
            {ctx.slots.bodyContainer?.()}
          </div>
        );
      }

      const formListProps = {
        class: ctx.slots.middle ? undefined : 'mgb-12',
        props: {
          showExport: props.config.showExport,
          semiAngle: props.semiAngle,
          search,
          reset: onReset,
          other: onOtherBtn,
        },
        slots: ctx.slots,
      };
      return (
        <article class="tg-page-container mjt-list">
          {ctx.slots.searchBefore?.()}
          {(ctx.slots.default?.() || ctx.slots.otherBtns?.()) && (
            <MJFormList {...formListProps} v-slots={ctx.slots}></MJFormList>
          )}
          {ctx.slots.middle?.()}
          {bodyContainer}
          {ctx.slots.footer?.()}
        </article>
      );
    };
  },
});

export default ListMJTemplate;
