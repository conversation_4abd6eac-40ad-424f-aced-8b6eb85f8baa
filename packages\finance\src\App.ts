import {
  getCurrentBreadcrumbRoutes,
  provideBreadcrumbRoutes,
  RoutesType,
} from 'common/src/use/breadcrumb';
import { defineComponent, computed, ref, watch } from 'vue';
import { removeToken, setUserInfo } from '@/utils/token';
import { useAuthStore } from '@/store';
import { RouterNameHome } from './router/types';
import { useRouter, useRoute } from 'vue-router';
import { useHeaderConfig } from '@/router/headerCnfig';
import { useRequest } from 'common';
import { get_user_info } from '@/services';
import { ElMessageBox } from 'element-plus';

export default defineComponent({
  setup() {
    const authStore = useAuthStore();

    console.log('创建APP');
    const router = useRouter();
    const menus = useHeaderConfig();
    const customBreadCrumbRoutesModel = ref<RoutesType>({
      routes: [],
      append: false,
    });
    provideBreadcrumbRoutes(customBreadCrumbRoutesModel);

    const is_not_first = ref(false);
    const reqUserInfo = useRequest(get_user_info, {
      manual: true,
      manualError: true,
      onSuccess(data) {
        if (
          JSON.stringify(data.right_codes || {}) !==
            JSON.stringify(authStore.userInfo?.right_codes || {}) &&
          is_not_first.value
        ) {
          setUserInfo(data);
          authStore.setUseInfo(data);
          ElMessageBox.confirm('账号权限发生变更，请立即刷新。', {
            showCancelButton: false,
            confirmButtonText: '刷新',
            showClose: false,
          }).then(() => {
            location.reload();
          });
        }
        is_not_first.value = false;
        setUserInfo(data);
        authStore.setUseInfo(data);
      },
    });
    const infoInterval = ref();
    if (!infoInterval.value) {
      if (import.meta.env.VITE_APP_DEVELOP !== 'true') {
        infoInterval.value = setInterval(() => {
          if (authStore.token) {
            reqUserInfo.runAsync();
          }
        }, 60000);
      }
    }
    const token = computed(() => {
      if (authStore.token) {
        reqUserInfo.runAsync();
      }
      return authStore.token;
    });
    const userInfo = computed(() => authStore.userInfo);

    const cachedViews = ref<string[]>([]);
    watch(
      () => useRoute(),
      to => {
        if (to.meta?.isKeepLive && to.name) {
          if (!cachedViews.value.includes(to.name as string)) {
            cachedViews.value.push(to.name as string);
          }
        }
      },
      { immediate: true },
    );

    const logout = () => {
      removeToken();
      router.push({
        name: RouterNameHome.login,
      });
      authStore.setToken('');
    };
    const creadCrumbRoutes = computed(() => {
      const { append, routes } = customBreadCrumbRoutesModel.value || {};
      if (!append && routes?.length) return routes;
      const { values } = getCurrentBreadcrumbRoutes(menus);
      if (!append) {
        return values;
      }
      console.log('计算得出', values, routes);
      return [...(values || []).slice(0, -1), ...(routes || [])].filter(Boolean);
    });
    function getKeepLive() {
      return router.currentRoute.value.meta?.isKeepLive;
    }

    function isExternal() {
      return router.currentRoute.value.path.indexOf('/external/') >= 0;
    }

    // const onCityChange = (value: number, newCityInfo: ICity) => {
    //   setCityInfo(newCityInfo);
    // };
    // const onCityHandler = () => {
    //   reloadCityList({
    //     disableCache: true,
    //   });
    // };

    const isSpecialLayout = computed(() => {
      return ['/login', '/'].includes(router.currentRoute.value.path as string);
    });

    return {
      menus,
      isExternal,
      userInfo,
      token,
      logout,
      creadCrumbRoutes,
      getKeepLive,

      currentRoute: router.currentRoute,
      log: (v, r) => {
        // console.log('r', r);
        console.log(v, r.matched[r.matched.length - 1]?.path ?? r.path);
      },
      cachedViews,
      isSpecialLayout,
    };
  },
});
