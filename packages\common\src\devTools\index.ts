import { createApp } from 'vue';
import ElementPlus from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import Search from './search';
const loadScript = async (url: string) => {
  const p = Promise.withResolvers();
  const script = document.createElement('script');
  script.onload = () => {
    p.resolve(null);
  };
  script.onerror = (e: any) => p.reject(e);
  script.src = url;
  document.head.appendChild(script);
  return p.promise;
};

export const installDebugger = menus => {
  loadScript('https://tiange-oss.goumee.com/prod/npm/pinyin-pro@3.19.0/dist/index.js').then(() => {
    let rootDom = document.querySelector('#app-debugger');
    if (!rootDom) {
      rootDom = document.createElement('div');
      rootDom.id = 'app-debugger';
      document.body.appendChild(rootDom);
    }
    const app = createApp(Search, { menus });
    app.use(ElementPlus);
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component);
    }
    app.mount(rootDom);
  });
};
