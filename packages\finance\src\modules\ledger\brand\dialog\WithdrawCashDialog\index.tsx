import { defineComponent, ref } from 'vue';
import { FormInstance, ElForm } from 'element-plus';
import useSmsCode from './use/use-sms-code';
import { Select } from '@common/components/select';
import { useDialog, useRequest } from 'common';
import { account, get_verification_code, withdrawal } from '@/services/ledger';
import tips from '../tips.vue';

export default defineComponent({
  setup(_, ctx) {
    const initFormData = () => ({
      bank_card_no: '', // 银行卡
      amount: 0, // 提现金额
      verify_code: '', // 验证码
    });

    const formData = ref(initFormData());
    const formDataRef = ref<FormInstance>();
    const rules = {};
    const codeObj = useSmsCode();
    const send_code = ref(false);
    const accountList = ref([]);
    const accountBalance = ref(0);
    const agentMobile = ref('***********');

    const getVerificationCodeReq = useRequest(get_verification_code, {
      manual: true,
      onSuccess: () => {
        codeObj.start();
        send_code.value = true;
      },
    });

    // 获取提现账号列表
    const getAccountListReq = useRequest(account, {
      manual: true,
      onSuccess: (data: any) => {
        agentMobile.value = data.agent_info?.agent_mobile || '';
        if (data.bank_info && data.bank_info.length) {
          accountList.value = data.bank_info.map(item => ({
            label: `${item.bank_name}(${item.bank_card_no.slice(-4)})`,
            value: item.bank_card_no,
          }));
          const defaultAccount = data.bank_info.find(item => item.is_default);
          formData.value.bank_card_no = defaultAccount?.bank_card_no || '';
        }
      },
    });

    // // 获取账户余额
    // const getAccountBalanceReq = useRequest(get_account_balance, {
    //   manual: true,
    //   onSuccess: (data: any) => {
    //     accountBalance.value = data.balance_amount;
    //   },
    // });

    // 提示弹窗
    const tipsDialog = useDialog({
      component: tips,
      title: '温馨提示',
      width: 400,
      disabledOK: true,
      cancelText: '关闭',
    });

    const submitReq = useRequest(withdrawal, {
      manual: true,
      onSuccess: (data: any) => {
        ctx.emit('close');
        tipsDialog.show();
      },
    });

    // 添加手机号脱敏函数
    const desensitizePhone = (phone: string) => {
      // if (!phone || phone.length !== 11) return phone;
      return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    };

    const show = (row?: any) => {
      formData.value = initFormData();
      accountBalance.value = row.intAmount;
      getAccountListReq.runAsync();
      // getAccountBalanceReq.runAsync();
    };

    const onSaveBtnClick = () => {
      submitReq.runAsync(formData.value);
    };

    // 核心
    ctx.expose({ show, onSaveBtnClick });

    return () => (
      <div class="withdrawal-dialog">
        <ElForm
          ref={formDataRef}
          class="form"
          model={formData.value}
          status-icon={false}
          rules={rules}
          label-suffix=":"
        >
          <el-form-item prop="bankCard">
            <Select
              class="form-item-width"
              v-model={formData.value.bank_card_no}
              placeholder="请选择银行卡"
              filterable
              reserve-keyword
              options={accountList.value}
            ></Select>
          </el-form-item>
          <el-form-item prop="withdrawalAmount">
            <div>
              <el-input
                placeholder="请输入提现金额"
                v-model={formData.value.amount}
                class="form-item-width"
              ></el-input>
              <div class="balance-tips">
                <div class="balance">当前余额{accountBalance.value}</div>
                <el-button
                  type="primary"
                  link
                  onClick={() => {
                    formData.value.amount = accountBalance.value;
                  }}
                >
                  全部提现
                </el-button>
              </div>
            </div>
          </el-form-item>

          <el-form-item prop="code">
            <div>
              <el-input
                maxlength="6"
                v-model={formData.value.verify_code}
                placeholder="请输入短信验证码"
                auto-complete="off"
                ref="codeRef"
                autocomplete="off"
              >
                {{
                  suffix: () => {
                    return (
                      <div class="suffix-center">
                        <span
                          class={[codeObj.isCounting.value ? 'disabled' : '', 'vcode-btn']}
                          onClick={() => {
                            getVerificationCodeReq.runAsync({
                              bank_card_no: formData.value.bank_card_no,
                            });
                          }}
                        >
                          {codeObj.label.value}
                        </span>
                      </div>
                    );
                  },
                }}
              </el-input>
              {send_code.value && (
                <div div class="code-tip tart">
                  {`请输入手机号${desensitizePhone(agentMobile.value)}收到的提现验证码`}
                </div>
              )}
            </div>
          </el-form-item>
        </ElForm>
      </div>
    );
  },
});
