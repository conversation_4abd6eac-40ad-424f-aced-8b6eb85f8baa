<script src="./index.tsx"></script>
<style lang="scss" scoped>
.mj-interview-feedback-table {
  display: flex;
  flex-direction: column;
  height: 100%;

  #container {
    width: 100%;
    height: 100%;
    // flex: 1 0 400px;
  }

  .filter-section {
    display: flex;
    margin-bottom: 12px;

    .filter-item {
      display: flex;
      align-items: center;

      .label {
        flex-shrink: 0;
        margin-left: 12px;
      }
    }
  }
}
</style>
