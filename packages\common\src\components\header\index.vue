<script src="./index.tsx"></script>
<style lang="scss">
  .header-menu-popover {
    --el-popover-padding: 12px 0 !important;
  }
</style>
<style scoped lang="scss">
  .mj-header-page-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;
    min-height: 64px;
    background-color: black;
    padding: 0 24px;

    .logo-section {
      .logo {
        // width: 44px;
        // height: 44px;
        // background-color: #bebebe;
        // border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 22px;

        img {
          width: 127px;
          height: 25px;
        }
      }
    }

    .menu-section {
      .menu-item {
        font-size: 15px;
        color: #ffffffe5;
        text-decoration: none;
        padding: 4px 12px;
        cursor: pointer;
        margin-left: 8px;

        &:first-child {
          margin-left: 0;
        }

        &.in-popover {
          margin-left: 0;
          color: black;

          &:not(:first-of-type) {
            margin-top: 6px;
          }
        }

        &.router-link-exact-active {
          background: #ff3a9a;
          border-radius: 4px;
          display: inline-block;
          height: 28px;
          line-height: 27px;
          padding: 0 10px;
          color: #000000;
        }
      }
    }

    .user-section {
      display: flex;
      align-items: center;
      min-width: 150px;
      justify-content: flex-end;

      .user-div {
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }

      .head-img {
        width: 32px;
        height: 32px;
        border-radius: 4px;

        img {
          width: 32px;
          height: 32px;
          border-radius: 4px;
        }
      }

      .avatar {
        width: 40px;
        height: 40px;
        background: #bebebe;
        border-radius: 50%;
      }

      .name {
        margin-left: 16px;
      }
    }

    :deep(.el-popper) {
      .menu-item {
        &.router-link-exact-active {
          background-color: transparent;
        }

        &:hover {
          background-color: #f7f7f7;
        }
      }
    }
  }
</style>
<style lang="scss">
  .mj-header-page-container__popover {
  }
</style>
