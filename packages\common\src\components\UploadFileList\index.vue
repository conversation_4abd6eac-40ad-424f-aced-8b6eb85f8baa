<script src="./index.tsx"></script>
<style lang="scss" scoped>
.upload-file-list {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  .btn-pre {
    margin-left: 12px;
    cursor: pointer;
    color: #0e5bf2e0;
  }

  .icon-doc {
    color: #979797;
  }

  &.column-3 {
    margin-top: 10px;
    margin-bottom: 40px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;

    .file-item {
      width: 320px;
      margin-bottom: 10px;

      .file-name {
        flex: none;
        max-width: 280px;
      }
    }
  }

  &.column-2 {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;

    .file-item {
      width: 360px;

      .file-name {
        flex: none;
        max-width: 300px;
      }
    }
  }

  .file-item {
    display: flex;
    align-items: center;
    row-gap: 14px;
    color: var(--text-color);
    cursor: default;
    width: 100%;

    .icon-delete {
      margin-left: 4px;
      width: 14px;
      height: 14px;
      margin-top: 1px;
      cursor: pointer;
      color: #d8d8d8;
      border-radius: 50%;
      border: 1px solid #d8d8d8;
      font-size: 12px;

      &:hover {
        color: #f04b4b !important;
        border: 1px solid #f04b4b !important;
      }

      svg {
        width: 12px;
        height: 12px;

        &:nth-child(1) {
          height: 12px;
        }
      }
    }

    svg {
      width: 16px;
      height: 16px;
      flex: 0 0 auto;
      fill: currentColor;

      &:nth-child(1) {
        height: 16px;
      }
    }

    .file-name {
      padding: 0 6px 0 4px;
      overflow: hidden;
      flex: 1;
      font-size: var(--font-size-default);
      white-space: nowrap;
      text-overflow: ellipsis;
      color: var(--text-color);
      margin: 0;
    }
  }

  .btn-download {
    display: none;
    cursor: pointer;
    color: #0e5bf2e0;
  }

  &.is-download {
    .btn-download {
      display: flex;
    }
  }

  &.upload-file-list-inline {
    display: inline-flex;
    flex-direction: row;
    flex-wrap: wrap;

    & .file-item {
      display: inline-block;

      & svg {
        vertical-align: middle;
      }

      & p {
        vertical-align: middle;
        display: inline-block;
      }
    }
  }
}
</style>
