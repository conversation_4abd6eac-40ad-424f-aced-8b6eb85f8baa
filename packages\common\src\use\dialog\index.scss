.bl-dialog-component-overlay {
  // --el-overlay-color-lighter: #000000e5;
  // --el-overlay-color-lighter: #000000b2;
  .el-overlay-dialog {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.bl-dialog-component-container.el-dialog {
  --bl-dialog-top-space: 60px;
  //--el-dialog-bg-color: var(--theme-color);
  box-shadow: 0 2px 20px 0 #1e146a0d;
  --el-dialog-border-radius: 12px;
  --el-dialog-padding-primary: 0;
  --bl-dialog-container-height: auto;
  --bl-dialog-container-max-height: calc(100vh - var(--bl-dialog-top-space) * 2);
  max-height: var(--bl-dialog-container-max-height);
  height: var(--bl-dialog-container-height);
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  &::before {
    content: '';
    position: absolute;
    top: -20px;
    right: -30px;
    width: 136px;
    height: 100px;
    background-image: linear-gradient(243deg, #82b8f800 0%, #446ff2 85%);
    filter: blur(31px);
    opacity: 0.3;
  }

  .el-dialog__header {
    //   display: none;
    // background-color: rgba(var(--theme-rgb-color), 0.05);
    display: flex;
    padding: 20px 20px 12px 20px;
    justify-content: center;
  }

  .el-dialog__footer {
    .footer-buttons {
      padding: 16px;
    }
  }

  .el-dialog__footer .footer-buttons {
    text-align: center;
  }

  .close-div {
    $closeHeight: 40px;
    position: fixed;
    top: 32px;
    right: 25px;
    width: $closeHeight;
    height: $closeHeight;
    background: #ffffff99;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    > .icon {
      font-size: 20px;
      color: black;
    }

    &:hover {
      background-color: white;
    }

    // filter: blur(10px);
  }

  .el-dialog__body {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .dialog-new-body {
      display: flex;
      flex-direction: column;
      height: 100%;
      overflow: hidden;

      .header-title {
        padding: 16px;
        font-size: 16px;
      }

      .dialog-div {
        flex: 1;
        overflow: auto;
        display: flex;
        flex-direction: column;
        padding: 20px;
      }
    }
  }

  .mj-dialog-footer {
    padding: 12px 0 0 0;
    display: flex;
    justify-content: center;
  }
}

.mj-dialog-detail {
  .mj-dialog-footer {
    padding: 12px 0 0 0;
    display: flex;
    justify-content: center;
  }
}
