import { ref, Ref } from 'vue';
import { query_store_list_no_right_codes } from '@/services/store';
import { get_manicurist_list_no_right_codes } from '@/services/manicurist';
import { get_agent_list_no_right_codes } from '@/services/agent';
import { get_area_list } from '@/services';

export interface ISelectOptions {
  label: string;
  value: string;
  source?: any;
}

type ISelectService = (value?: string) => Promise<ISelectOptions[]>;
type IUseSelectFn = (config?: {
  remote?: boolean;
  filterable?: boolean;
  service?: ISelectService;
}) => {
  selectProps: Ref<any>;
  options: Ref<ISelectOptions[]>;
};

const defaultService = (async () => {}) as any;
export const useSelectOption: IUseSelectFn = ({
  remote = true,
  filterable = true,
  service = defaultService,
} = {}) => {
  const options = ref<ISelectOptions[]>([]);
  const remoteMethod = (searchName?: string) => {
    service(searchName).then(value => {
      options.value = value;
    });
  };
  if (filterable === false) {
    service('').then(value => {
      options.value = value;
    });
  }
  const selectProps = ref({
    filterable,
    remote,
    remoteMethod,
    options,
  });
  return { selectProps, options };
};

// 获取门店下拉框
export const useSelectOptionStore: IUseSelectFn = (config?: any) =>
  useSelectOption({
    service: async (value: string) => {
      return query_store_list_no_right_codes({ page_num: 1, page_size: 20 }, { name: value }).then(
        res => {
          return res.data.data.data.map(t => {
            return {
              label: t.name,
              value: t.id,
              source: t,
            };
          });
        },
      );
    },
    ...config,
  });

// 获取代理商下拉框
export const useSelectOptionAgent: IUseSelectFn = (config?: any) =>
  useSelectOption({
    service: async (value: string) => {
      return get_agent_list_no_right_codes({ page_num: 1, page_size: 200 }, { name: value }).then(
        res => {
          return res.data.data.data.map(t => {
            return {
              label: t.name,
              value: t.id,
              source: t,
            };
          });
        },
      );
    },
    ...config,
  });

// 获取美甲师下拉框
export const useSelectOptionManicurist: IUseSelectFn = (config?: any) =>
  useSelectOption({
    service: async (value: string) => {
      return get_manicurist_list_no_right_codes(
        { page_num: 1, page_size: 20 },
        { id_name: value },
      ).then(res => {
        return res.data.data.data.map(t => {
          return {
            label: t.id_name,
            value: t.id,
            source: t,
          };
        });
      });
    },
    ...config,
  });

// 获取所有门店区域
export const useSelectOptionArea: IUseSelectFn = (config?: any) =>
  useSelectOption({
    service: async () => {
      return get_area_list().then((res: any) => {
        return (res.data.data?.area_info || []).map(e => {
          return {
            value: e,
            label: e,
          };
        });
      });
    },
    ...config,
  });
